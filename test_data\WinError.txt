_id	message
686358669c3e8a30651e9d85	Experiment Crash
--------------------------------------------------
BUS2-Channel2-ImpaSetMultiParams-ImpaSetParams(12-16)-bus=BUS12-686358b11041b0f155ba1607
--------------------------------------------------
[WinError 10054] 远程主机强迫关闭了一个现有的连接。
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 329, in run
    self._run_experiment()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\library\impa_set_params.py", line 66, in _run_experiment
    self.mic_source.output_off(mic_channel)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\BYFS.py", line 594, in output_off
    protocol = super().output_off(channels)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\BYFS.py", line 463, in output_off
    self.set_freq(channel, 10e9)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\BYFS.py", line 586, in set_freq
    self._connection.send(protocol)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\socket_service\tcp_client.py", line 51, in send
    self._send_data(self._sock, send_data)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\socket_service\tcp_client.py", line 41, in _send_data
    sock.sendall(data)  # 发送数据
ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。

686359479c3e8a30651e9d86	Experiment Crash
--------------------------------------------------
BUS2-Channel2-ImpaSetMultiParams-ImpaSetParams(2-16)-Bus2-686359937201f973cae125ae
--------------------------------------------------
[WinError 10054] 远程主机强迫关闭了一个现有的连接。
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 329, in run
    self._run_experiment()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\library\impa_set_params.py", line 67, in _run_experiment
    self.mic_source.output_off(mic_channel)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\BYFS.py", line 594, in output_off
    protocol = super().output_off(channels)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\BYFS.py", line 463, in output_off
    self.set_freq(channel, 10e9)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\BYFS.py", line 586, in set_freq
    self._connection.send(protocol)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\socket_service\tcp_client.py", line 51, in send
    self._send_data(self._sock, send_data)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\socket_service\tcp_client.py", line 41, in _send_data
    sock.sendall(data)  # 发送数据
ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。

6863594ea47f89e2f61e9d85	Experiment Crash
--------------------------------------------------
BUS4-Channel4-ImpaSetMultiParams-ImpaSetParams(4-16)-BUS4-686359997201f973cae125b0
--------------------------------------------------
[WinError 10054] 远程主机强迫关闭了一个现有的连接。
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 329, in run
    self._run_experiment()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\library\impa_set_params.py", line 67, in _run_experiment
    self.mic_source.output_off(mic_channel)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\BYFS.py", line 594, in output_off
    protocol = super().output_off(channels)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\BYFS.py", line 463, in output_off
    self.set_freq(channel, 10e9)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\BYFS.py", line 586, in set_freq
    self._connection.send(protocol)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\socket_service\tcp_client.py", line 51, in send
    self._send_data(self._sock, send_data)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\socket_service\tcp_client.py", line 41, in _send_data
    sock.sendall(data)  # 发送数据
ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。

68779cc9814a76608c8ce021	Experiment Crash
--------------------------------------------------
q5q6c5-6-CouplerSpectrumZAmpDynamic-68779c9f65bea5c7de595f64
--------------------------------------------------
[WinError 123] 文件名、目录名或卷标语法不正确。: 'Z:\\Y8\\yxy\\Reset_LRU_test\\CouplerSpectrumZAmpDynamic\\q5q6c5-6\x825-07-16\x019.55.24\\CouplerSpectrum'
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\yxy\naga\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 73, in wrapper
    await func(*args, **kwargs)
  File "D:\code\yxy\naga\pyqcat-apps\pyQCat\experiments\composite_experiment.py", line 513, in run_experiment
    self.run()
  File "D:\code\yxy\naga\pyqcat-apps\pyQCat\experiments\composite_experiment.py", line 198, in run
    self._validate_options()
  File "D:\code\yxy\naga\pyqcat-apps\pyQCat\experiments\base_experiment.py", line 931, in _validate_options
    paths = rocket_optimize(self.experiment_options.simulator_data_path)
  File "D:\code\yxy\naga\pyqcat-apps\pyQCat\tools\utilities.py", line 772, in rocket_optimize
    for f in os.listdir(root):
OSError: [WinError 123] 文件名、目录名或卷标语法不正确。: 'Z:\\Y8\\yxy\\Reset_LRU_test\\CouplerSpectrumZAmpDynamic\\q5q6c5-6\x825-07-16\x019.55.24\\CouplerSpectrum'

6888261ff4b30d9991446ec1	Experiment Crash
--------------------------------------------------
q12-CavityPowerScan-CavityFreqSpectrum(2-31)-power=-39dBm-688826aac1c9e18c7070e85b
--------------------------------------------------
[WinError 123] 文件名、目录名或卷标语法不正确。: ':'
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 605, in run_experiment
    self._validate_options()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 362, in _validate_options
    super()._validate_options()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\base_experiment.py", line 942, in _validate_options
    paths = rocket_optimize(self.experiment_options.simulator_data_path)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\tools\utilities.py", line 770, in rocket_optimize
    for f in os.listdir(root):
OSError: [WinError 123] 文件名、目录名或卷标语法不正确。: ':'

68882672f340c94b3b446ff1	Experiment Crash
--------------------------------------------------
q12-CavityPowerScan-CavityFreqSpectrum(2-31)-power=-39dBm-688826fdf13d5118f9313222
--------------------------------------------------
[WinError 123] 文件名、目录名或卷标语法不正确。: ':'
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 605, in run_experiment
    self._validate_options()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 362, in _validate_options
    super()._validate_options()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\base_experiment.py", line 942, in _validate_options
    paths = rocket_optimize(self.experiment_options.simulator_data_path)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\tools\utilities.py", line 770, in rocket_optimize
    for f in os.listdir(root):
OSError: [WinError 123] 文件名、目录名或卷标语法不正确。: ':'

6888272df340c94b3b447000	Experiment Crash
--------------------------------------------------
q12-CavityPowerScan-CavityFreqSpectrum(2-31)-power=-39dBm-688827b87cc95b5f160570ee
--------------------------------------------------
[WinError 123] 文件名、目录名或卷标语法不正确。: ':'
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 605, in run_experiment
    self._validate_options()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 362, in _validate_options
    super()._validate_options()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\base_experiment.py", line 942, in _validate_options
    paths = rocket_optimize(self.experiment_options.simulator_data_path)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\tools\utilities.py", line 770, in rocket_optimize
    for f in os.listdir(root):
OSError: [WinError 123] 文件名、目录名或卷标语法不正确。: ':'

68955f1c5dd635150013b5dd	Experiment Crash
--------------------------------------------------
q19q20q21q22q23q24-BUS4-Channel4-ImpaGain-68955f1b973ee1f9ed36e62d
--------------------------------------------------
[WinError 10054] 远程主机强迫关闭了一个现有的连接。
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\project\lzw\pyqcat-visage\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\project\lzw\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\project\lzw\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 329, in run
    self._run_experiment()
  File "D:\code\project\lzw\pyqcat-visage\pyQCat\preliminary\library\impa_gain.py", line 210, in _run_experiment
    data = self._run_open_mode()
  File "D:\code\project\lzw\pyqcat-visage\pyQCat\preliminary\library\impa_gain.py", line 152, in _run_open_mode
    self.dc_source.output_off(int(dc_channel))
  File "D:\code\project\lzw\pyqcat-visage\pyQCat\instrument\qdc.py", line 52, in output_off
    recv = tcp_client.send(protocol, 1)
  File "D:\code\project\lzw\pyqcat-visage\pyQCat\instrument\socket_service\tcp_client.py", line 51, in send
    self._send_data(self._sock, send_data)
  File "D:\code\project\lzw\pyqcat-visage\pyQCat\instrument\socket_service\tcp_client.py", line 41, in _send_data
    sock.sendall(data)  # 发送数据
ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。

68998a45f340c94b3b44960f	Experiment Crash
--------------------------------------------------
bus-15-ImpaGain-68998aed77ff99b4c37a1b38
--------------------------------------------------
[WinError 10054] 远程主机强迫关闭了一个现有的连接。
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 300, in _initial
    self.mic_source.output_off(mic_channel)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\instrument\BYFS.py", line 595, in output_off
    protocol = super().output_off(channels)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\instrument\BYFS.py", line 464, in output_off
    self.set_freq(channel, 10e9)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\instrument\BYFS.py", line 587, in set_freq
    self._connection.send(protocol)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 51, in send
    self._send_data(self._sock, send_data)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 41, in _send_data
    sock.sendall(data)  # 发送数据
ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
