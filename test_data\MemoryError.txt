_id	message
686cc97d59896c8d2c02fcc7	Experiment Crash
--------------------------------------------------
q-circuit-QCloudPlusV1-686cc8518bf45b2d14e2a760
--------------------------------------------------
Unable to allocate 512. GiB for an array with shape (131072, 2, 131072, 2) and data type float64
--------------------------------------------------
Traceback (most recent call last):
  File "D:\Code\wxy\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\Code\wxy\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 614, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\Code\wxy\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\Code\wxy\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 156, in top_experiment_analysis
    data_acquisition = acq_class(**acquisition_options)
  File "D:\Code\wxy\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 202, in __init__
    self.fidelity_matrix = self.update_fidelity_matrix(is_amend, fidelity_matrix, on_discriminator)
  File "D:\Code\wxy\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 361, in update_fidelity_matrix
    new_fd_matrix = np.kron(new_fd_matrix, dcm.fidelity_matrix)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\numpy\lib\shape_base.py", line 1173, in kron
    result = _nx.multiply(a_arr, b_arr, subok=(not is_any_mat))
numpy.core._exceptions._ArrayMemoryError: Unable to allocate 512. GiB for an array with shape (131072, 2, 131072, 2) and data type float64

686f69f659896c8d2c03033a	Experiment Crash
--------------------------------------------------
q-circuit-QCloudPlusV1-686f68df2d27b927c1ec1c6c
--------------------------------------------------
Unable to allocate 512. GiB for an array with shape (131072, 2, 131072, 2) and data type float64
--------------------------------------------------
Traceback (most recent call last):
  File "D:\Code\wxy\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\Code\wxy\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 614, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\Code\wxy\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\Code\wxy\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 156, in top_experiment_analysis
    data_acquisition = acq_class(**acquisition_options)
  File "D:\Code\wxy\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 202, in __init__
    self.fidelity_matrix = self.update_fidelity_matrix(is_amend, fidelity_matrix, on_discriminator)
  File "D:\Code\wxy\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 361, in update_fidelity_matrix
    new_fd_matrix = np.kron(new_fd_matrix, dcm.fidelity_matrix)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\numpy\lib\shape_base.py", line 1173, in kron
    result = _nx.multiply(a_arr, b_arr, subok=(not is_any_mat))
numpy.core._exceptions._ArrayMemoryError: Unable to allocate 512. GiB for an array with shape (131072, 2, 131072, 2) and data type float64

68765de659896c8d2c031834	Experiment Fail
--------------------------------------------------
q-circuit-QCloudPlusV1-68765586ba251c64cf9ba4df
--------------------------------------------------
<Acq Data Tackle Error> | Traceback (most recent call last):
  File "D:\Code\wxy\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "D:\Code\wxy\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 765, in _async_get_measure_data
    self.correct_res()
  File "D:\Code\wxy\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 437, in correct_res
    prob = correct_fidelity(
  File "D:\Code\wxy\pyqcat-apps\pyQCat\analysis\algorithms\iqprobability.py", line 1270, in correct_fidelity
    after_correct_f = iter_bayesian_unfold(
  File "D:\Code\wxy\pyqcat-apps\pyQCat\analysis\algorithms\iqprobability.py", line 1483, in iter_bayesian_unfold
    ptruen_mat = np.tile(ptruen, (dim, 1))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\numpy\lib\shape_base.py", line 1272, in tile
    c = c.reshape(-1, n).repeat(nrep, 0)
numpy.core._exceptions._ArrayMemoryError: Unable to allocate 128. GiB for an array with shape (131072, 131072) and data type float64
