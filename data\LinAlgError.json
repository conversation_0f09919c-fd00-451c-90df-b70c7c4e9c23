[{"_id": {"$oid": "6890bd4a6946627d159cc5d1"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-04 22:01:56.422114", "message": "Experiment Fail\n--------------------------------------------------\nq2-PurityXEBSingle-6890bc80c7216026b418e9d6\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 406, in run_analysis\n    self._initialize()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 180, in _initialize\n    self._analysis_data_dict = self._create_analysis_data()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\library\\purity_sing_xeb_analysis.py\", line 105, in _create_analysis_data\n    rho = qst_mle(chunks, base_ops)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\tomography.py\", line 258, in qst_mle\n    d, v = np.linalg.eig(rho0)  # 计算特征值和特征向量\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 1329, in eig\n    _assert_finite(a)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 218, in _assert_finite\n    raise LinAlgError(\"Array must not contain infs or NaNs\")\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-04T22:01:46.291Z"}}, {"_id": {"$oid": "6890bd4a9192f4bbc39cc596"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-04 22:01:56.411113", "message": "Experiment Fail\n--------------------------------------------------\nq4-PurityXEBSingle-6890bc80c7216026b418e9d8\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 406, in run_analysis\n    self._initialize()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 180, in _initialize\n    self._analysis_data_dict = self._create_analysis_data()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\library\\purity_sing_xeb_analysis.py\", line 105, in _create_analysis_data\n    rho = qst_mle(chunks, base_ops)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\tomography.py\", line 258, in qst_mle\n    d, v = np.linalg.eig(rho0)  # 计算特征值和特征向量\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 1329, in eig\n    _assert_finite(a)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 218, in _assert_finite\n    raise LinAlgError(\"Array must not contain infs or NaNs\")\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-04T22:01:46.292Z"}}, {"_id": {"$oid": "6890bd4a9192f4bbc39cc597"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-04 22:01:56.433114", "message": "Experiment Fail\n--------------------------------------------------\nq1-PurityXEBSingle-6890bc80c7216026b418e9d5\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 406, in run_analysis\n    self._initialize()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 180, in _initialize\n    self._analysis_data_dict = self._create_analysis_data()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\library\\purity_sing_xeb_analysis.py\", line 105, in _create_analysis_data\n    rho = qst_mle(chunks, base_ops)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\tomography.py\", line 258, in qst_mle\n    d, v = np.linalg.eig(rho0)  # 计算特征值和特征向量\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 1329, in eig\n    _assert_finite(a)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 218, in _assert_finite\n    raise LinAlgError(\"Array must not contain infs or NaNs\")\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-04T22:01:46.381Z"}}, {"_id": {"$oid": "6890bd4a6946627d159cc5d2"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-04 22:01:56.444113", "message": "Experiment Fail\n--------------------------------------------------\nq6-PurityXEBSingle-6890bc80c7216026b418e9da\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 406, in run_analysis\n    self._initialize()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 180, in _initialize\n    self._analysis_data_dict = self._create_analysis_data()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\library\\purity_sing_xeb_analysis.py\", line 105, in _create_analysis_data\n    rho = qst_mle(chunks, base_ops)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\tomography.py\", line 258, in qst_mle\n    d, v = np.linalg.eig(rho0)  # 计算特征值和特征向量\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 1329, in eig\n    _assert_finite(a)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 218, in _assert_finite\n    raise LinAlgError(\"Array must not contain infs or NaNs\")\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-04T22:01:46.383Z"}}, {"_id": {"$oid": "6890bd4a9192f4bbc39cc598"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-04 22:01:56.523114", "message": "Experiment Fail\n--------------------------------------------------\nq3-PurityXEBSingle-6890bc80c7216026b418e9d7\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 406, in run_analysis\n    self._initialize()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 180, in _initialize\n    self._analysis_data_dict = self._create_analysis_data()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\library\\purity_sing_xeb_analysis.py\", line 105, in _create_analysis_data\n    rho = qst_mle(chunks, base_ops)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\tomography.py\", line 258, in qst_mle\n    d, v = np.linalg.eig(rho0)  # 计算特征值和特征向量\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 1329, in eig\n    _assert_finite(a)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 218, in _assert_finite\n    raise LinAlgError(\"Array must not contain infs or NaNs\")\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-04T22:01:46.43Z"}}, {"_id": {"$oid": "6890bd4e9192f4bbc39cc599"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-04 22:02:00.781911", "message": "Experiment Fail\n--------------------------------------------------\nq5-PurityXEBSingle-6890bc80c7216026b418e9d9\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 406, in run_analysis\n    self._initialize()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 180, in _initialize\n    self._analysis_data_dict = self._create_analysis_data()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\library\\purity_sing_xeb_analysis.py\", line 105, in _create_analysis_data\n    rho = qst_mle(chunks, base_ops)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\tomography.py\", line 258, in qst_mle\n    d, v = np.linalg.eig(rho0)  # 计算特征值和特征向量\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 1329, in eig\n    _assert_finite(a)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 218, in _assert_finite\n    raise LinAlgError(\"Array must not contain infs or NaNs\")\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-04T22:01:50.652Z"}}, {"_id": {"$oid": "689172bb6946627d159cc8a4"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 10:56:06.168517", "message": "Experiment Fail\n--------------------------------------------------\nq2-PurityXEBSingle-689171f390761059a6ef36a1\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 406, in run_analysis\n    self._initialize()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 180, in _initialize\n    self._analysis_data_dict = self._create_analysis_data()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\library\\purity_sing_xeb_analysis.py\", line 105, in _create_analysis_data\n    rho = qst_mle(chunks, base_ops)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\tomography.py\", line 258, in qst_mle\n    d, v = np.linalg.eig(rho0)  # 计算特征值和特征向量\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 1329, in eig\n    _assert_finite(a)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 218, in _assert_finite\n    raise LinAlgError(\"Array must not contain infs or NaNs\")\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T10:55:55.5Z"}}, {"_id": {"$oid": "689172bb9192f4bbc39cc8a7"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 10:56:06.179518", "message": "Experiment Fail\n--------------------------------------------------\nq1-PurityXEBSingle-689171f390761059a6ef36a0\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 406, in run_analysis\n    self._initialize()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 180, in _initialize\n    self._analysis_data_dict = self._create_analysis_data()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\library\\purity_sing_xeb_analysis.py\", line 105, in _create_analysis_data\n    rho = qst_mle(chunks, base_ops)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\tomography.py\", line 258, in qst_mle\n    d, v = np.linalg.eig(rho0)  # 计算特征值和特征向量\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 1329, in eig\n    _assert_finite(a)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 218, in _assert_finite\n    raise LinAlgError(\"Array must not contain infs or NaNs\")\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T10:55:55.515Z"}}, {"_id": {"$oid": "689172bb6946627d159cc8a5"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 10:56:06.265522", "message": "Experiment Fail\n--------------------------------------------------\nq6-PurityXEBSingle-689171f390761059a6ef36a5\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 406, in run_analysis\n    self._initialize()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 180, in _initialize\n    self._analysis_data_dict = self._create_analysis_data()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\library\\purity_sing_xeb_analysis.py\", line 105, in _create_analysis_data\n    rho = qst_mle(chunks, base_ops)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\tomography.py\", line 258, in qst_mle\n    d, v = np.linalg.eig(rho0)  # 计算特征值和特征向量\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 1329, in eig\n    _assert_finite(a)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 218, in _assert_finite\n    raise LinAlgError(\"Array must not contain infs or NaNs\")\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T10:55:55.633Z"}}, {"_id": {"$oid": "689172bb6946627d159cc8a6"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 10:56:06.317525", "message": "Experiment Fail\n--------------------------------------------------\nq4-PurityXEBSingle-689171f390761059a6ef36a3\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 406, in run_analysis\n    self._initialize()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 180, in _initialize\n    self._analysis_data_dict = self._create_analysis_data()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\library\\purity_sing_xeb_analysis.py\", line 105, in _create_analysis_data\n    rho = qst_mle(chunks, base_ops)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\tomography.py\", line 258, in qst_mle\n    d, v = np.linalg.eig(rho0)  # 计算特征值和特征向量\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 1329, in eig\n    _assert_finite(a)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 218, in _assert_finite\n    raise LinAlgError(\"Array must not contain infs or NaNs\")\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T10:55:55.702Z"}}, {"_id": {"$oid": "689172bb6946627d159cc8a7"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 10:56:06.387530", "message": "Experiment Fail\n--------------------------------------------------\nq3-PurityXEBSingle-689171f390761059a6ef36a2\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 406, in run_analysis\n    self._initialize()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 180, in _initialize\n    self._analysis_data_dict = self._create_analysis_data()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\library\\purity_sing_xeb_analysis.py\", line 105, in _create_analysis_data\n    rho = qst_mle(chunks, base_ops)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\tomography.py\", line 258, in qst_mle\n    d, v = np.linalg.eig(rho0)  # 计算特征值和特征向量\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 1329, in eig\n    _assert_finite(a)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 218, in _assert_finite\n    raise LinAlgError(\"Array must not contain infs or NaNs\")\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T10:55:55.753Z"}}, {"_id": {"$oid": "689172bf6946627d159cc8a8"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 10:56:10.061778", "message": "Experiment Fail\n--------------------------------------------------\nq5-PurityXEBSingle-689171f390761059a6ef36a4\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 406, in run_analysis\n    self._initialize()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 180, in _initialize\n    self._analysis_data_dict = self._create_analysis_data()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\library\\purity_sing_xeb_analysis.py\", line 105, in _create_analysis_data\n    rho = qst_mle(chunks, base_ops)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\tomography.py\", line 258, in qst_mle\n    d, v = np.linalg.eig(rho0)  # 计算特征值和特征向量\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 1329, in eig\n    _assert_finite(a)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 218, in _assert_finite\n    raise LinAlgError(\"Array must not contain infs or NaNs\")\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T10:55:59.389Z"}}, {"_id": {"$oid": "6891ba3f890a246c6cfb08f7"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 16:01:14.645245", "message": "Experiment Fail\n--------------------------------------------------\nq6-PurityXEBSingle-6891b97594a082d218d9f9a0\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 406, in run_analysis\n    self._initialize()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 180, in _initialize\n    self._analysis_data_dict = self._create_analysis_data()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\library\\purity_sing_xeb_analysis.py\", line 105, in _create_analysis_data\n    rho = qst_mle(chunks, base_ops)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\tomography.py\", line 258, in qst_mle\n    d, v = np.linalg.eig(rho0)  # 计算特征值和特征向量\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 1329, in eig\n    _assert_finite(a)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 218, in _assert_finite\n    raise LinAlgError(\"Array must not contain infs or NaNs\")\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T16:01:03.872Z"}}, {"_id": {"$oid": "6891ba3fb84b084e52fb0860"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 16:01:14.667248", "message": "Experiment Fail\n--------------------------------------------------\nq2-PurityXEBSingle-6891b97594a082d218d9f99c\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 406, in run_analysis\n    self._initialize()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 180, in _initialize\n    self._analysis_data_dict = self._create_analysis_data()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\library\\purity_sing_xeb_analysis.py\", line 105, in _create_analysis_data\n    rho = qst_mle(chunks, base_ops)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\tomography.py\", line 258, in qst_mle\n    d, v = np.linalg.eig(rho0)  # 计算特征值和特征向量\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 1329, in eig\n    _assert_finite(a)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 218, in _assert_finite\n    raise LinAlgError(\"Array must not contain infs or NaNs\")\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T16:01:03.887Z"}}, {"_id": {"$oid": "6891ba3f890a246c6cfb08f8"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 16:01:14.680248", "message": "Experiment Fail\n--------------------------------------------------\nq4-PurityXEBSingle-6891b97594a082d218d9f99e\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 406, in run_analysis\n    self._initialize()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 180, in _initialize\n    self._analysis_data_dict = self._create_analysis_data()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\library\\purity_sing_xeb_analysis.py\", line 105, in _create_analysis_data\n    rho = qst_mle(chunks, base_ops)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\tomography.py\", line 258, in qst_mle\n    d, v = np.linalg.eig(rho0)  # 计算特征值和特征向量\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 1329, in eig\n    _assert_finite(a)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 218, in _assert_finite\n    raise LinAlgError(\"Array must not contain infs or NaNs\")\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T16:01:03.963Z"}}, {"_id": {"$oid": "6891ba3fb84b084e52fb0861"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 16:01:14.692247", "message": "Experiment Fail\n--------------------------------------------------\nq1-PurityXEBSingle-6891b97594a082d218d9f99b\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 406, in run_analysis\n    self._initialize()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 180, in _initialize\n    self._analysis_data_dict = self._create_analysis_data()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\library\\purity_sing_xeb_analysis.py\", line 105, in _create_analysis_data\n    rho = qst_mle(chunks, base_ops)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\tomography.py\", line 258, in qst_mle\n    d, v = np.linalg.eig(rho0)  # 计算特征值和特征向量\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 1329, in eig\n    _assert_finite(a)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 218, in _assert_finite\n    raise LinAlgError(\"Array must not contain infs or NaNs\")\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T16:01:03.977Z"}}, {"_id": {"$oid": "6891ba40890a246c6cfb08f9"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 16:01:14.926271", "message": "Experiment Fail\n--------------------------------------------------\nq3-PurityXEBSingle-6891b97594a082d218d9f99d\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 406, in run_analysis\n    self._initialize()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 180, in _initialize\n    self._analysis_data_dict = self._create_analysis_data()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\library\\purity_sing_xeb_analysis.py\", line 105, in _create_analysis_data\n    rho = qst_mle(chunks, base_ops)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\tomography.py\", line 258, in qst_mle\n    d, v = np.linalg.eig(rho0)  # 计算特征值和特征向量\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 1329, in eig\n    _assert_finite(a)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 218, in _assert_finite\n    raise LinAlgError(\"Array must not contain infs or NaNs\")\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T16:01:04.17Z"}}, {"_id": {"$oid": "6891ba44890a246c6cfb08fa"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 16:01:19.144485", "message": "Experiment Fail\n--------------------------------------------------\nq5-PurityXEBSingle-6891b97594a082d218d9f99f\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 132, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 406, in run_analysis\n    self._initialize()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\curve_analysis.py\", line 180, in _initialize\n    self._analysis_data_dict = self._create_analysis_data()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\library\\purity_sing_xeb_analysis.py\", line 105, in _create_analysis_data\n    rho = qst_mle(chunks, base_ops)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\tomography.py\", line 258, in qst_mle\n    d, v = np.linalg.eig(rho0)  # 计算特征值和特征向量\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 1329, in eig\n    _assert_finite(a)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\linalg\\linalg.py\", line 218, in _assert_finite\n    raise LinAlgError(\"Array must not contain infs or NaNs\")\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nnumpy.linalg.LinAlgError: Array must not contain infs or NaNs\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T16:01:08.354Z"}}]