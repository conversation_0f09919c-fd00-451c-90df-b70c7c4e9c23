_id	message
6888261ff340c94b3b446fd5	Experiment Crash
--------------------------------------------------
q12-CavityPowerScan-CavityFreqSpectrum(1-31)-power=-40dBm-688826aac1c9e18c7070e85a
--------------------------------------------------
local variable 'visage_plot' referenced before assignment
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 614, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 164, in top_experiment_analysis
    data_acquisition.execute_loop()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 898, in execute_loop
    if visage_plot:
UnboundLocalError: local variable 'visage_plot' referenced before assignment

6888261ff340c94b3b446fd6	Experiment Crash
--------------------------------------------------
q12-CavityPowerScan-CavityFreqSpectrum(3-31)-power=-38dBm-688826aac1c9e18c7070e85c
--------------------------------------------------
local variable 'visage_plot' referenced before assignment
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 614, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 164, in top_experiment_analysis
    data_acquisition.execute_loop()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 898, in execute_loop
    if visage_plot:
UnboundLocalError: local variable 'visage_plot' referenced before assignment

6888261ff4b30d9991446ec2	Experiment Crash
--------------------------------------------------
q12-CavityPowerScan-CavityFreqSpectrum(4-31)-power=-37dBm-688826aac1c9e18c7070e85d
--------------------------------------------------
local variable 'visage_plot' referenced before assignment
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 614, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 164, in top_experiment_analysis
    data_acquisition.execute_loop()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 898, in execute_loop
    if visage_plot:
UnboundLocalError: local variable 'visage_plot' referenced before assignment

68882620f340c94b3b446fd7	Experiment Crash
--------------------------------------------------
q12-CavityPowerScan-CavityFreqSpectrum(5-31)-power=-36dBm-688826aac1c9e18c7070e85e
--------------------------------------------------
local variable 'visage_plot' referenced before assignment
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 614, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 164, in top_experiment_analysis
    data_acquisition.execute_loop()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 898, in execute_loop
    if visage_plot:
UnboundLocalError: local variable 'visage_plot' referenced before assignment

68882620f4b30d9991446ec3	Experiment Crash
--------------------------------------------------
q12-CavityPowerScan-CavityFreqSpectrum(6-31)-power=-35dBm-688826aac1c9e18c7070e85f
--------------------------------------------------
local variable 'visage_plot' referenced before assignment
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 614, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 164, in top_experiment_analysis
    data_acquisition.execute_loop()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 898, in execute_loop
    if visage_plot:
UnboundLocalError: local variable 'visage_plot' referenced before assignment

68882620f4b30d9991446ec4	Experiment Crash
--------------------------------------------------
q12-CavityPowerScan-CavityFreqSpectrum(7-31)-power=-34dBm-688826aac1c9e18c7070e860
--------------------------------------------------
local variable 'visage_plot' referenced before assignment
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 614, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 164, in top_experiment_analysis
    data_acquisition.execute_loop()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 898, in execute_loop
    if visage_plot:
UnboundLocalError: local variable 'visage_plot' referenced before assignment

68882620f340c94b3b446fd8	Experiment Crash
--------------------------------------------------
q12-CavityPowerScan-CavityFreqSpectrum(8-31)-power=-33dBm-688826aac1c9e18c7070e861
--------------------------------------------------
local variable 'visage_plot' referenced before assignment
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 614, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 164, in top_experiment_analysis
    data_acquisition.execute_loop()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 898, in execute_loop
    if visage_plot:
UnboundLocalError: local variable 'visage_plot' referenced before assignment

68882620f4b30d9991446ec5	Experiment Crash
--------------------------------------------------
q12-CavityPowerScan-CavityFreqSpectrum(9-31)-power=-32dBm-688826aac1c9e18c7070e862
--------------------------------------------------
local variable 'visage_plot' referenced before assignment
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 614, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 164, in top_experiment_analysis
    data_acquisition.execute_loop()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 898, in execute_loop
    if visage_plot:
UnboundLocalError: local variable 'visage_plot' referenced before assignment

68882620f340c94b3b446fd9	Experiment Crash
--------------------------------------------------
q12-CavityPowerScan-CavityFreqSpectrum(10-31)-power=-31dBm-688826aac1c9e18c7070e863
--------------------------------------------------
local variable 'visage_plot' referenced before assignment
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 614, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 164, in top_experiment_analysis
    data_acquisition.execute_loop()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 898, in execute_loop
    if visage_plot:
UnboundLocalError: local variable 'visage_plot' referenced before assignment

68882620f4b30d9991446ec6	Experiment Crash
--------------------------------------------------
q12-CavityPowerScan-CavityFreqSpectrum(11-31)-power=-30dBm-688826abc1c9e18c7070e864
--------------------------------------------------
local variable 'visage_plot' referenced before assignment
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 614, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 164, in top_experiment_analysis
    data_acquisition.execute_loop()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 898, in execute_loop
    if visage_plot:
UnboundLocalError: local variable 'visage_plot' referenced before assignment

68882620f340c94b3b446fda	Experiment Crash
--------------------------------------------------
q12-CavityPowerScan-CavityFreqSpectrum(12-31)-power=-29dBm-688826abc1c9e18c7070e865
--------------------------------------------------
local variable 'visage_plot' referenced before assignment
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 614, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 164, in top_experiment_analysis
    data_acquisition.execute_loop()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 898, in execute_loop
    if visage_plot:
UnboundLocalError: local variable 'visage_plot' referenced before assignment

68882620f340c94b3b446fdb	Experiment Crash
--------------------------------------------------
q12-CavityPowerScan-CavityFreqSpectrum(13-31)-power=-28dBm-688826abc1c9e18c7070e866
--------------------------------------------------
local variable 'visage_plot' referenced before assignment
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 614, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 164, in top_experiment_analysis
    data_acquisition.execute_loop()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 898, in execute_loop
    if visage_plot:
UnboundLocalError: local variable 'visage_plot' referenced before assignment

68882620f340c94b3b446fdc	Experiment Crash
--------------------------------------------------
q12-CavityPowerScan-CavityFreqSpectrum(14-31)-power=-27dBm-688826abc1c9e18c7070e867
--------------------------------------------------
local variable 'visage_plot' referenced before assignment
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 614, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 164, in top_experiment_analysis
    data_acquisition.execute_loop()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 898, in execute_loop
    if visage_plot:
UnboundLocalError: local variable 'visage_plot' referenced before assignment

68882620f340c94b3b446fdd	Experiment Crash
--------------------------------------------------
q12-CavityPowerScan-CavityFreqSpectrum(15-31)-power=-26dBm-688826abc1c9e18c7070e868
--------------------------------------------------
local variable 'visage_plot' referenced before assignment
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 614, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 164, in top_experiment_analysis
    data_acquisition.execute_loop()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 898, in execute_loop
    if visage_plot:
UnboundLocalError: local variable 'visage_plot' referenced before assignment

68882620f340c94b3b446fde	Experiment Crash
--------------------------------------------------
q12-CavityPowerScan-CavityFreqSpectrum(16-31)-power=-25dBm-688826abc1c9e18c7070e869
--------------------------------------------------
local variable 'visage_plot' referenced before assignment
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 614, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 164, in top_experiment_analysis
    data_acquisition.execute_loop()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 898, in execute_loop
    if visage_plot:
UnboundLocalError: local variable 'visage_plot' referenced before assignment

68882620f340c94b3b446fdf	Experiment Crash
--------------------------------------------------
q12-CavityPowerScan-CavityFreqSpectrum(17-31)-power=-24dBm-688826abc1c9e18c7070e86a
--------------------------------------------------
local variable 'visage_plot' referenced before assignment
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 614, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 164, in top_experiment_analysis
    data_acquisition.execute_loop()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 898, in execute_loop
    if visage_plot:
UnboundLocalError: local variable 'visage_plot' referenced before assignment

68882620f340c94b3b446fe0	Experiment Crash
--------------------------------------------------
q12-CavityPowerScan-CavityFreqSpectrum(18-31)-power=-23dBm-688826abc1c9e18c7070e86b
--------------------------------------------------
local variable 'visage_plot' referenced before assignment
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 614, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 164, in top_experiment_analysis
    data_acquisition.execute_loop()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 898, in execute_loop
    if visage_plot:
UnboundLocalError: local variable 'visage_plot' referenced before assignment

68882620f340c94b3b446fe1	Experiment Crash
--------------------------------------------------
q12-CavityPowerScan-CavityFreqSpectrum(19-31)-power=-22dBm-688826abc1c9e18c7070e86c
--------------------------------------------------
local variable 'visage_plot' referenced before assignment
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 614, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 164, in top_experiment_analysis
    data_acquisition.execute_loop()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 898, in execute_loop
    if visage_plot:
UnboundLocalError: local variable 'visage_plot' referenced before assignment

68882620f340c94b3b446fe2	Experiment Crash
--------------------------------------------------
q12-CavityPowerScan-CavityFreqSpectrum(20-31)-power=-21dBm-688826abc1c9e18c7070e86d
--------------------------------------------------
local variable 'visage_plot' referenced before assignment
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 614, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 164, in top_experiment_analysis
    data_acquisition.execute_loop()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 898, in execute_loop
    if visage_plot:
UnboundLocalError: local variable 'visage_plot' referenced before assignment

68882620f340c94b3b446fe3	Experiment Crash
--------------------------------------------------
q12-CavityPowerScan-CavityFreqSpectrum(21-31)-power=-20dBm-688826abc1c9e18c7070e86e
--------------------------------------------------
local variable 'visage_plot' referenced before assignment
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 614, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 164, in top_experiment_analysis
    data_acquisition.execute_loop()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 898, in execute_loop
    if visage_plot:
UnboundLocalError: local variable 'visage_plot' referenced before assignment

68882620f340c94b3b446fe4	Experiment Crash
--------------------------------------------------
q12-CavityPowerScan-CavityFreqSpectrum(22-31)-power=-19dBm-688826abc1c9e18c7070e86f
--------------------------------------------------
local variable 'visage_plot' referenced before assignment
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 614, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 164, in top_experiment_analysis
    data_acquisition.execute_loop()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 898, in execute_loop
    if visage_plot:
UnboundLocalError: local variable 'visage_plot' referenced before assignment

68882620f340c94b3b446fe5	Experiment Crash
--------------------------------------------------
q12-CavityPowerScan-CavityFreqSpectrum(23-31)-power=-18dBm-688826abc1c9e18c7070e870
--------------------------------------------------
local variable 'visage_plot' referenced before assignment
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 614, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 164, in top_experiment_analysis
    data_acquisition.execute_loop()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 898, in execute_loop
    if visage_plot:
UnboundLocalError: local variable 'visage_plot' referenced before assignment

68882620f340c94b3b446fe6	Experiment Crash
--------------------------------------------------
q12-CavityPowerScan-CavityFreqSpectrum(24-31)-power=-17dBm-688826abc1c9e18c7070e871
--------------------------------------------------
local variable 'visage_plot' referenced before assignment
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 614, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 164, in top_experiment_analysis
    data_acquisition.execute_loop()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 898, in execute_loop
    if visage_plot:
UnboundLocalError: local variable 'visage_plot' referenced before assignment

68882620f340c94b3b446fe7	Experiment Crash
--------------------------------------------------
q12-CavityPowerScan-CavityFreqSpectrum(25-31)-power=-16dBm-688826abc1c9e18c7070e872
--------------------------------------------------
local variable 'visage_plot' referenced before assignment
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 614, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 164, in top_experiment_analysis
    data_acquisition.execute_loop()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 898, in execute_loop
    if visage_plot:
UnboundLocalError: local variable 'visage_plot' referenced before assignment

68882620f340c94b3b446fe8	Experiment Crash
--------------------------------------------------
q12-CavityPowerScan-CavityFreqSpectrum(26-31)-power=-15dBm-688826abc1c9e18c7070e873
--------------------------------------------------
local variable 'visage_plot' referenced before assignment
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 614, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 164, in top_experiment_analysis
    data_acquisition.execute_loop()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 898, in execute_loop
    if visage_plot:
UnboundLocalError: local variable 'visage_plot' referenced before assignment

68882620f340c94b3b446fe9	Experiment Crash
--------------------------------------------------
q12-CavityPowerScan-CavityFreqSpectrum(27-31)-power=-14dBm-688826abc1c9e18c7070e874
--------------------------------------------------
local variable 'visage_plot' referenced before assignment
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 614, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 164, in top_experiment_analysis
    data_acquisition.execute_loop()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 898, in execute_loop
    if visage_plot:
UnboundLocalError: local variable 'visage_plot' referenced before assignment

68882620f340c94b3b446fea	Experiment Crash
--------------------------------------------------
q12-CavityPowerScan-CavityFreqSpectrum(28-31)-power=-13dBm-688826abc1c9e18c7070e875
--------------------------------------------------
local variable 'visage_plot' referenced before assignment
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 614, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 164, in top_experiment_analysis
    data_acquisition.execute_loop()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 898, in execute_loop
    if visage_plot:
UnboundLocalError: local variable 'visage_plot' referenced before assignment

68882620f340c94b3b446feb	Experiment Crash
--------------------------------------------------
q12-CavityPowerScan-CavityFreqSpectrum(29-31)-power=-12dBm-688826abc1c9e18c7070e876
--------------------------------------------------
local variable 'visage_plot' referenced before assignment
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 614, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 164, in top_experiment_analysis
    data_acquisition.execute_loop()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 898, in execute_loop
    if visage_plot:
UnboundLocalError: local variable 'visage_plot' referenced before assignment

68882620f340c94b3b446fec	Experiment Crash
--------------------------------------------------
q12-CavityPowerScan-CavityFreqSpectrum(30-31)-power=-11dBm-688826abc1c9e18c7070e877
--------------------------------------------------
local variable 'visage_plot' referenced before assignment
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 614, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 164, in top_experiment_analysis
    data_acquisition.execute_loop()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 898, in execute_loop
    if visage_plot:
UnboundLocalError: local variable 'visage_plot' referenced before assignment

68882620f340c94b3b446fed	Experiment Crash
--------------------------------------------------
q12-CavityPowerScan-CavityFreqSpectrum(31-31)-power=-10dBm-688826abc1c9e18c7070e878
--------------------------------------------------
local variable 'visage_plot' referenced before assignment
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 614, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 164, in top_experiment_analysis
    data_acquisition.execute_loop()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 898, in execute_loop
    if visage_plot:
UnboundLocalError: local variable 'visage_plot' referenced before assignment
