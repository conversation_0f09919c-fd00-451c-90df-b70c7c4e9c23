_id	message
6890b90e50c6afe211883df5	Experiment Fail
--------------------------------------------------
q17-QubitSpectrumF12-6890b909a64d966d6103e78b
--------------------------------------------------
<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: 
"""
Traceback (most recent call last):
  File "D:\software\Anaconda\envs\visage\lib\concurrent\futures\process.py", line 246, in _process_worker
    r = call_item.fn(*call_item.args, **call_item.kwargs)
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\experiments\_wrappers.py", line 140, in build_experiment_message
    exp_env.set_xy_pulses(builder)
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\experiments\single\single_gate\f12_spectrum.py", line 81, in set_xy_pulses
    xy_pulses = QubitSpectrumF12.get_xy_pulse(
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\experiments\single\single_gate\f12_spectrum.py", line 132, in get_xy_pulse
    + new_f12_pulse()
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\pulse\base_pulse.py", line 132, in __call__
    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\pulse\base_pulse.py", line 147, in _from_parameters
    return cls(**parameters)
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\pulse\pulse_lib.py", line 286, in __init__
    super().__init__(time, name)
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\pulse\base_pulse.py", line 98, in __init__
    self.validate_parameters()
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\pulse\pulse_lib.py", line 326, in validate_parameters
    raise PulseError(
pyQCat.errors.PulseError: 'baseband_freq must be greater than 800 and smaller than 1300 MHz, found: 718.105 MHz'
"""

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\concurrent\calculate_resource.py", line 35, in run_in_executor
    return await loop.run_in_executor(executor, fn, *args)
pyQCat.errors.PulseError: 'baseband_freq must be greater than 800 and smaller than 1300 MHz, found: 718.105 MHz'

6890b90e150663897c883a2a	Experiment Fail
--------------------------------------------------
q11-QubitSpectrumF12-6890b909a64d966d6103e78a
--------------------------------------------------
<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: 
"""
Traceback (most recent call last):
  File "D:\software\Anaconda\envs\visage\lib\concurrent\futures\process.py", line 246, in _process_worker
    r = call_item.fn(*call_item.args, **call_item.kwargs)
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\experiments\_wrappers.py", line 140, in build_experiment_message
    exp_env.set_xy_pulses(builder)
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\experiments\single\single_gate\f12_spectrum.py", line 81, in set_xy_pulses
    xy_pulses = QubitSpectrumF12.get_xy_pulse(
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\experiments\single\single_gate\f12_spectrum.py", line 132, in get_xy_pulse
    + new_f12_pulse()
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\pulse\base_pulse.py", line 132, in __call__
    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\pulse\base_pulse.py", line 147, in _from_parameters
    return cls(**parameters)
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\pulse\pulse_lib.py", line 286, in __init__
    super().__init__(time, name)
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\pulse\base_pulse.py", line 98, in __init__
    self.validate_parameters()
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\pulse\pulse_lib.py", line 326, in validate_parameters
    raise PulseError(
pyQCat.errors.PulseError: 'baseband_freq must be greater than 800 and smaller than 1300 MHz, found: 683.952 MHz'
"""

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\concurrent\calculate_resource.py", line 35, in run_in_executor
    return await loop.run_in_executor(executor, fn, *args)
pyQCat.errors.PulseError: 'baseband_freq must be greater than 800 and smaller than 1300 MHz, found: 683.952 MHz'

6890b90e50c6afe211883df6	Experiment Fail
--------------------------------------------------
q5-QubitSpectrumF12-6890b909a64d966d6103e78d
--------------------------------------------------
<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: 
"""
Traceback (most recent call last):
  File "D:\software\Anaconda\envs\visage\lib\concurrent\futures\process.py", line 246, in _process_worker
    r = call_item.fn(*call_item.args, **call_item.kwargs)
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\experiments\_wrappers.py", line 140, in build_experiment_message
    exp_env.set_xy_pulses(builder)
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\experiments\single\single_gate\f12_spectrum.py", line 81, in set_xy_pulses
    xy_pulses = QubitSpectrumF12.get_xy_pulse(
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\experiments\single\single_gate\f12_spectrum.py", line 132, in get_xy_pulse
    + new_f12_pulse()
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\pulse\base_pulse.py", line 132, in __call__
    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\pulse\base_pulse.py", line 147, in _from_parameters
    return cls(**parameters)
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\pulse\pulse_lib.py", line 286, in __init__
    super().__init__(time, name)
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\pulse\base_pulse.py", line 98, in __init__
    self.validate_parameters()
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\pulse\pulse_lib.py", line 326, in validate_parameters
    raise PulseError(
pyQCat.errors.PulseError: 'baseband_freq must be greater than 800 and smaller than 1300 MHz, found: 725.829 MHz'
"""

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\concurrent\calculate_resource.py", line 35, in run_in_executor
    return await loop.run_in_executor(executor, fn, *args)
pyQCat.errors.PulseError: 'baseband_freq must be greater than 800 and smaller than 1300 MHz, found: 725.829 MHz'
