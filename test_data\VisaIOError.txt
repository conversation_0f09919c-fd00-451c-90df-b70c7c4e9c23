_id	message
6888936f11ad73c4231f636e	Experiment Crash
--------------------------------------------------
BUS1-Channel1-BusS21Collector-688893b76735a1218163e6ba
--------------------------------------------------
VI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.
--------------------------------------------------
Traceback (most recent call last):
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 457, in after_parsing
    self.interface = Vxi11CoreClient(host_address, port, self.open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 361, in __init__
    rpc.TCPClient.__init__(self, host, prog, vers, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 823, in __init__
    pmap = TCPPortMapperClient(host, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 803, in __init__
    RawTCPClient.__init__(self, host, PMAP_PROG, PMAP_VERS, PMAP_PORT, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 454, in __init__
    self.connect(1e-3 * open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 488, in connect
    raise RPCError("can't connect to server")
pyvisa_py.protocols.rpc.RPCError: can't connect to server

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 271, in _initial
    self.select_net_analyzer(network_analyzer_type)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 127, in select_net_analyzer
    self.net_analyzer = NetworkAnalyzer(net_analyzer, self.config_path)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 37, in __init__
    super(NetworkAnalyzer, self).__init__(instrument_model, config_path)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 29, in __init__
    self.inst = INSTRUMENT(config_path)[instrument_model]
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\INSTRUMENT.py", line 70, in __getitem__
    return self._get(secname)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\INSTRUMENT.py", line 75, in _get
    return self._INST_LST.get(self._type)(self._visa_address)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\structures.py", line 154, in __call__
    obj = super().__call__(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\E5071C.py", line 12, in __init__
    super().__init__(visa_address)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\visa_inst.py", line 35, in __init__
    self.inst_obj = rm.open_resource(self.visa_address)
  File "D:\miniconda3\lib\site-packages\pyvisa\highlevel.py", line 3284, in open_resource
    res.open(access_mode, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa\resources\resource.py", line 282, in open
    self.session, status = self._resource_manager.open_bare_resource(
  File "D:\miniconda3\lib\site-packages\pyvisa\highlevel.py", line 3209, in open_bare_resource
    return self.visalib.open(self.session, resource_name, access_mode, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\highlevel.py", line 168, in open
    sess = cls(session, resource_name, parsed, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 86, in __new__
    return newcls(resource_manager_session, resource_name, parsed, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\sessions.py", line 322, in __init__
    self.after_parsing()
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 459, in after_parsing
    raise errors.VisaIOError(constants.VI_ERROR_RSRC_NFOUND)
pyvisa.errors.VisaIOError: VI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.

6888963ff43e794f372aa0da	Experiment Crash
--------------------------------------------------
BUS1-Channel1-BusS21Collector-6888968640be4e3426e55172
--------------------------------------------------
VI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.
--------------------------------------------------
Traceback (most recent call last):
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 457, in after_parsing
    self.interface = Vxi11CoreClient(host_address, port, self.open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 361, in __init__
    rpc.TCPClient.__init__(self, host, prog, vers, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 823, in __init__
    pmap = TCPPortMapperClient(host, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 803, in __init__
    RawTCPClient.__init__(self, host, PMAP_PROG, PMAP_VERS, PMAP_PORT, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 454, in __init__
    self.connect(1e-3 * open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 488, in connect
    raise RPCError("can't connect to server")
pyvisa_py.protocols.rpc.RPCError: can't connect to server

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 271, in _initial
    self.select_net_analyzer(network_analyzer_type)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 127, in select_net_analyzer
    self.net_analyzer = NetworkAnalyzer(net_analyzer, self.config_path)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 37, in __init__
    super(NetworkAnalyzer, self).__init__(instrument_model, config_path)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 29, in __init__
    self.inst = INSTRUMENT(config_path)[instrument_model]
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\INSTRUMENT.py", line 70, in __getitem__
    return self._get(secname)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\INSTRUMENT.py", line 75, in _get
    return self._INST_LST.get(self._type)(self._visa_address)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\structures.py", line 154, in __call__
    obj = super().__call__(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\E5071C.py", line 12, in __init__
    super().__init__(visa_address)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\visa_inst.py", line 35, in __init__
    self.inst_obj = rm.open_resource(self.visa_address)
  File "D:\miniconda3\lib\site-packages\pyvisa\highlevel.py", line 3284, in open_resource
    res.open(access_mode, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa\resources\resource.py", line 282, in open
    self.session, status = self._resource_manager.open_bare_resource(
  File "D:\miniconda3\lib\site-packages\pyvisa\highlevel.py", line 3209, in open_bare_resource
    return self.visalib.open(self.session, resource_name, access_mode, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\highlevel.py", line 168, in open
    sess = cls(session, resource_name, parsed, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 86, in __new__
    return newcls(resource_manager_session, resource_name, parsed, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\sessions.py", line 322, in __init__
    self.after_parsing()
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 459, in after_parsing
    raise errors.VisaIOError(constants.VI_ERROR_RSRC_NFOUND)
pyvisa.errors.VisaIOError: VI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.

68889675f6689fe5521f636d	Experiment Crash
--------------------------------------------------
BUS1-Channel1-BusS21Collector-688896bd6bb8ebc1fcbf886e
--------------------------------------------------
VI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.
--------------------------------------------------
Traceback (most recent call last):
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 457, in after_parsing
    self.interface = Vxi11CoreClient(host_address, port, self.open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 361, in __init__
    rpc.TCPClient.__init__(self, host, prog, vers, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 823, in __init__
    pmap = TCPPortMapperClient(host, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 803, in __init__
    RawTCPClient.__init__(self, host, PMAP_PROG, PMAP_VERS, PMAP_PORT, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 454, in __init__
    self.connect(1e-3 * open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 488, in connect
    raise RPCError("can't connect to server")
pyvisa_py.protocols.rpc.RPCError: can't connect to server

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 271, in _initial
    self.select_net_analyzer(network_analyzer_type)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 127, in select_net_analyzer
    self.net_analyzer = NetworkAnalyzer(net_analyzer, self.config_path)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 37, in __init__
    super(NetworkAnalyzer, self).__init__(instrument_model, config_path)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 29, in __init__
    self.inst = INSTRUMENT(config_path)[instrument_model]
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\INSTRUMENT.py", line 70, in __getitem__
    return self._get(secname)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\INSTRUMENT.py", line 75, in _get
    return self._INST_LST.get(self._type)(self._visa_address)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\structures.py", line 154, in __call__
    obj = super().__call__(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\E5071C.py", line 12, in __init__
    super().__init__(visa_address)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\visa_inst.py", line 35, in __init__
    self.inst_obj = rm.open_resource(self.visa_address)
  File "D:\miniconda3\lib\site-packages\pyvisa\highlevel.py", line 3284, in open_resource
    res.open(access_mode, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa\resources\resource.py", line 282, in open
    self.session, status = self._resource_manager.open_bare_resource(
  File "D:\miniconda3\lib\site-packages\pyvisa\highlevel.py", line 3209, in open_bare_resource
    return self.visalib.open(self.session, resource_name, access_mode, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\highlevel.py", line 168, in open
    sess = cls(session, resource_name, parsed, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 86, in __new__
    return newcls(resource_manager_session, resource_name, parsed, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\sessions.py", line 322, in __init__
    self.after_parsing()
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 459, in after_parsing
    raise errors.VisaIOError(constants.VI_ERROR_RSRC_NFOUND)
pyvisa.errors.VisaIOError: VI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.

6888971c32aee6d0092aa0da	Experiment Crash
--------------------------------------------------
BUS-1-FindBusCavityFreq-Rough-688897647f92df5525d835c7
--------------------------------------------------
VI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.
--------------------------------------------------
Traceback (most recent call last):
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 457, in after_parsing
    self.interface = Vxi11CoreClient(host_address, port, self.open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 361, in __init__
    rpc.TCPClient.__init__(self, host, prog, vers, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 823, in __init__
    pmap = TCPPortMapperClient(host, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 803, in __init__
    RawTCPClient.__init__(self, host, PMAP_PROG, PMAP_VERS, PMAP_PORT, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 454, in __init__
    self.connect(1e-3 * open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 488, in connect
    raise RPCError("can't connect to server")
pyvisa_py.protocols.rpc.RPCError: can't connect to server

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\library\find_cavity_freq.py", line 124, in _initial
    super()._initial()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 271, in _initial
    self.select_net_analyzer(network_analyzer_type)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 127, in select_net_analyzer
    self.net_analyzer = NetworkAnalyzer(net_analyzer, self.config_path)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 37, in __init__
    super(NetworkAnalyzer, self).__init__(instrument_model, config_path)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 29, in __init__
    self.inst = INSTRUMENT(config_path)[instrument_model]
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\INSTRUMENT.py", line 70, in __getitem__
    return self._get(secname)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\INSTRUMENT.py", line 75, in _get
    return self._INST_LST.get(self._type)(self._visa_address)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\structures.py", line 154, in __call__
    obj = super().__call__(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\E5071C.py", line 12, in __init__
    super().__init__(visa_address)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\visa_inst.py", line 35, in __init__
    self.inst_obj = rm.open_resource(self.visa_address)
  File "D:\miniconda3\lib\site-packages\pyvisa\highlevel.py", line 3284, in open_resource
    res.open(access_mode, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa\resources\resource.py", line 282, in open
    self.session, status = self._resource_manager.open_bare_resource(
  File "D:\miniconda3\lib\site-packages\pyvisa\highlevel.py", line 3209, in open_bare_resource
    return self.visalib.open(self.session, resource_name, access_mode, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\highlevel.py", line 168, in open
    sess = cls(session, resource_name, parsed, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 86, in __new__
    return newcls(resource_manager_session, resource_name, parsed, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\sessions.py", line 322, in __init__
    self.after_parsing()
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 459, in after_parsing
    raise errors.VisaIOError(constants.VI_ERROR_RSRC_NFOUND)
pyvisa.errors.VisaIOError: VI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.

6888972232aee6d0092aa0db	Experiment Crash
--------------------------------------------------
BUS-2-FindBusCavityFreq-Rough-6888976a7f92df5525d835ca
--------------------------------------------------
VI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.
--------------------------------------------------
Traceback (most recent call last):
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 457, in after_parsing
    self.interface = Vxi11CoreClient(host_address, port, self.open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 361, in __init__
    rpc.TCPClient.__init__(self, host, prog, vers, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 823, in __init__
    pmap = TCPPortMapperClient(host, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 803, in __init__
    RawTCPClient.__init__(self, host, PMAP_PROG, PMAP_VERS, PMAP_PORT, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 454, in __init__
    self.connect(1e-3 * open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 488, in connect
    raise RPCError("can't connect to server")
pyvisa_py.protocols.rpc.RPCError: can't connect to server

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\library\find_cavity_freq.py", line 124, in _initial
    super()._initial()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 271, in _initial
    self.select_net_analyzer(network_analyzer_type)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 127, in select_net_analyzer
    self.net_analyzer = NetworkAnalyzer(net_analyzer, self.config_path)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 37, in __init__
    super(NetworkAnalyzer, self).__init__(instrument_model, config_path)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 29, in __init__
    self.inst = INSTRUMENT(config_path)[instrument_model]
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\INSTRUMENT.py", line 70, in __getitem__
    return self._get(secname)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\INSTRUMENT.py", line 75, in _get
    return self._INST_LST.get(self._type)(self._visa_address)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\structures.py", line 154, in __call__
    obj = super().__call__(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\E5071C.py", line 12, in __init__
    super().__init__(visa_address)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\visa_inst.py", line 35, in __init__
    self.inst_obj = rm.open_resource(self.visa_address)
  File "D:\miniconda3\lib\site-packages\pyvisa\highlevel.py", line 3284, in open_resource
    res.open(access_mode, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa\resources\resource.py", line 282, in open
    self.session, status = self._resource_manager.open_bare_resource(
  File "D:\miniconda3\lib\site-packages\pyvisa\highlevel.py", line 3209, in open_bare_resource
    return self.visalib.open(self.session, resource_name, access_mode, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\highlevel.py", line 168, in open
    sess = cls(session, resource_name, parsed, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 86, in __new__
    return newcls(resource_manager_session, resource_name, parsed, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\sessions.py", line 322, in __init__
    self.after_parsing()
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 459, in after_parsing
    raise errors.VisaIOError(constants.VI_ERROR_RSRC_NFOUND)
pyvisa.errors.VisaIOError: VI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.

68889728f43e794f372aa0dc	Experiment Crash
--------------------------------------------------
BUS-3-FindBusCavityFreq-Rough-688897707f92df5525d835cd
--------------------------------------------------
VI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.
--------------------------------------------------
Traceback (most recent call last):
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 457, in after_parsing
    self.interface = Vxi11CoreClient(host_address, port, self.open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 361, in __init__
    rpc.TCPClient.__init__(self, host, prog, vers, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 823, in __init__
    pmap = TCPPortMapperClient(host, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 803, in __init__
    RawTCPClient.__init__(self, host, PMAP_PROG, PMAP_VERS, PMAP_PORT, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 454, in __init__
    self.connect(1e-3 * open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 488, in connect
    raise RPCError("can't connect to server")
pyvisa_py.protocols.rpc.RPCError: can't connect to server

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\library\find_cavity_freq.py", line 124, in _initial
    super()._initial()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 271, in _initial
    self.select_net_analyzer(network_analyzer_type)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 127, in select_net_analyzer
    self.net_analyzer = NetworkAnalyzer(net_analyzer, self.config_path)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 37, in __init__
    super(NetworkAnalyzer, self).__init__(instrument_model, config_path)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 29, in __init__
    self.inst = INSTRUMENT(config_path)[instrument_model]
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\INSTRUMENT.py", line 70, in __getitem__
    return self._get(secname)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\INSTRUMENT.py", line 75, in _get
    return self._INST_LST.get(self._type)(self._visa_address)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\structures.py", line 154, in __call__
    obj = super().__call__(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\E5071C.py", line 12, in __init__
    super().__init__(visa_address)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\visa_inst.py", line 35, in __init__
    self.inst_obj = rm.open_resource(self.visa_address)
  File "D:\miniconda3\lib\site-packages\pyvisa\highlevel.py", line 3284, in open_resource
    res.open(access_mode, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa\resources\resource.py", line 282, in open
    self.session, status = self._resource_manager.open_bare_resource(
  File "D:\miniconda3\lib\site-packages\pyvisa\highlevel.py", line 3209, in open_bare_resource
    return self.visalib.open(self.session, resource_name, access_mode, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\highlevel.py", line 168, in open
    sess = cls(session, resource_name, parsed, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 86, in __new__
    return newcls(resource_manager_session, resource_name, parsed, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\sessions.py", line 322, in __init__
    self.after_parsing()
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 459, in after_parsing
    raise errors.VisaIOError(constants.VI_ERROR_RSRC_NFOUND)
pyvisa.errors.VisaIOError: VI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.

6888972ef43e794f372aa0dd	Experiment Crash
--------------------------------------------------
BUS-4-FindBusCavityFreq-Rough-688897767f92df5525d835d0
--------------------------------------------------
VI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.
--------------------------------------------------
Traceback (most recent call last):
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 457, in after_parsing
    self.interface = Vxi11CoreClient(host_address, port, self.open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 361, in __init__
    rpc.TCPClient.__init__(self, host, prog, vers, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 823, in __init__
    pmap = TCPPortMapperClient(host, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 803, in __init__
    RawTCPClient.__init__(self, host, PMAP_PROG, PMAP_VERS, PMAP_PORT, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 454, in __init__
    self.connect(1e-3 * open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 488, in connect
    raise RPCError("can't connect to server")
pyvisa_py.protocols.rpc.RPCError: can't connect to server

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\library\find_cavity_freq.py", line 124, in _initial
    super()._initial()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 271, in _initial
    self.select_net_analyzer(network_analyzer_type)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 127, in select_net_analyzer
    self.net_analyzer = NetworkAnalyzer(net_analyzer, self.config_path)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 37, in __init__
    super(NetworkAnalyzer, self).__init__(instrument_model, config_path)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 29, in __init__
    self.inst = INSTRUMENT(config_path)[instrument_model]
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\INSTRUMENT.py", line 70, in __getitem__
    return self._get(secname)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\INSTRUMENT.py", line 75, in _get
    return self._INST_LST.get(self._type)(self._visa_address)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\structures.py", line 154, in __call__
    obj = super().__call__(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\E5071C.py", line 12, in __init__
    super().__init__(visa_address)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\visa_inst.py", line 35, in __init__
    self.inst_obj = rm.open_resource(self.visa_address)
  File "D:\miniconda3\lib\site-packages\pyvisa\highlevel.py", line 3284, in open_resource
    res.open(access_mode, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa\resources\resource.py", line 282, in open
    self.session, status = self._resource_manager.open_bare_resource(
  File "D:\miniconda3\lib\site-packages\pyvisa\highlevel.py", line 3209, in open_bare_resource
    return self.visalib.open(self.session, resource_name, access_mode, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\highlevel.py", line 168, in open
    sess = cls(session, resource_name, parsed, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 86, in __new__
    return newcls(resource_manager_session, resource_name, parsed, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\sessions.py", line 322, in __init__
    self.after_parsing()
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 459, in after_parsing
    raise errors.VisaIOError(constants.VI_ERROR_RSRC_NFOUND)
pyvisa.errors.VisaIOError: VI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.

68889734f43e794f372aa0de	Experiment Crash
--------------------------------------------------
BUS-5-FindBusCavityFreq-Rough-6888977c7f92df5525d835d3
--------------------------------------------------
VI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.
--------------------------------------------------
Traceback (most recent call last):
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 457, in after_parsing
    self.interface = Vxi11CoreClient(host_address, port, self.open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 361, in __init__
    rpc.TCPClient.__init__(self, host, prog, vers, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 823, in __init__
    pmap = TCPPortMapperClient(host, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 803, in __init__
    RawTCPClient.__init__(self, host, PMAP_PROG, PMAP_VERS, PMAP_PORT, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 454, in __init__
    self.connect(1e-3 * open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 488, in connect
    raise RPCError("can't connect to server")
pyvisa_py.protocols.rpc.RPCError: can't connect to server

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\library\find_cavity_freq.py", line 124, in _initial
    super()._initial()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 271, in _initial
    self.select_net_analyzer(network_analyzer_type)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 127, in select_net_analyzer
    self.net_analyzer = NetworkAnalyzer(net_analyzer, self.config_path)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 37, in __init__
    super(NetworkAnalyzer, self).__init__(instrument_model, config_path)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 29, in __init__
    self.inst = INSTRUMENT(config_path)[instrument_model]
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\INSTRUMENT.py", line 70, in __getitem__
    return self._get(secname)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\INSTRUMENT.py", line 75, in _get
    return self._INST_LST.get(self._type)(self._visa_address)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\structures.py", line 154, in __call__
    obj = super().__call__(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\E5071C.py", line 12, in __init__
    super().__init__(visa_address)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\visa_inst.py", line 35, in __init__
    self.inst_obj = rm.open_resource(self.visa_address)
  File "D:\miniconda3\lib\site-packages\pyvisa\highlevel.py", line 3284, in open_resource
    res.open(access_mode, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa\resources\resource.py", line 282, in open
    self.session, status = self._resource_manager.open_bare_resource(
  File "D:\miniconda3\lib\site-packages\pyvisa\highlevel.py", line 3209, in open_bare_resource
    return self.visalib.open(self.session, resource_name, access_mode, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\highlevel.py", line 168, in open
    sess = cls(session, resource_name, parsed, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 86, in __new__
    return newcls(resource_manager_session, resource_name, parsed, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\sessions.py", line 322, in __init__
    self.after_parsing()
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 459, in after_parsing
    raise errors.VisaIOError(constants.VI_ERROR_RSRC_NFOUND)
pyvisa.errors.VisaIOError: VI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.

6888973af43e794f372aa0df	Experiment Crash
--------------------------------------------------
BUS-6-FindBusCavityFreq-Rough-688897817f92df5525d835d6
--------------------------------------------------
VI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.
--------------------------------------------------
Traceback (most recent call last):
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 457, in after_parsing
    self.interface = Vxi11CoreClient(host_address, port, self.open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 361, in __init__
    rpc.TCPClient.__init__(self, host, prog, vers, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 823, in __init__
    pmap = TCPPortMapperClient(host, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 803, in __init__
    RawTCPClient.__init__(self, host, PMAP_PROG, PMAP_VERS, PMAP_PORT, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 454, in __init__
    self.connect(1e-3 * open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 488, in connect
    raise RPCError("can't connect to server")
pyvisa_py.protocols.rpc.RPCError: can't connect to server

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\library\find_cavity_freq.py", line 124, in _initial
    super()._initial()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 271, in _initial
    self.select_net_analyzer(network_analyzer_type)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 127, in select_net_analyzer
    self.net_analyzer = NetworkAnalyzer(net_analyzer, self.config_path)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 37, in __init__
    super(NetworkAnalyzer, self).__init__(instrument_model, config_path)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 29, in __init__
    self.inst = INSTRUMENT(config_path)[instrument_model]
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\INSTRUMENT.py", line 70, in __getitem__
    return self._get(secname)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\INSTRUMENT.py", line 75, in _get
    return self._INST_LST.get(self._type)(self._visa_address)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\structures.py", line 154, in __call__
    obj = super().__call__(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\E5071C.py", line 12, in __init__
    super().__init__(visa_address)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\visa_inst.py", line 35, in __init__
    self.inst_obj = rm.open_resource(self.visa_address)
  File "D:\miniconda3\lib\site-packages\pyvisa\highlevel.py", line 3284, in open_resource
    res.open(access_mode, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa\resources\resource.py", line 282, in open
    self.session, status = self._resource_manager.open_bare_resource(
  File "D:\miniconda3\lib\site-packages\pyvisa\highlevel.py", line 3209, in open_bare_resource
    return self.visalib.open(self.session, resource_name, access_mode, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\highlevel.py", line 168, in open
    sess = cls(session, resource_name, parsed, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 86, in __new__
    return newcls(resource_manager_session, resource_name, parsed, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\sessions.py", line 322, in __init__
    self.after_parsing()
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 459, in after_parsing
    raise errors.VisaIOError(constants.VI_ERROR_RSRC_NFOUND)
pyvisa.errors.VisaIOError: VI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.

688897fc32aee6d0092aa0dc	Experiment Crash
--------------------------------------------------
BUS1-Channel1-BusS21Collector-68889844871b6c2cc92fec34
--------------------------------------------------
VI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.
--------------------------------------------------
Traceback (most recent call last):
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 457, in after_parsing
    self.interface = Vxi11CoreClient(host_address, port, self.open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 361, in __init__
    rpc.TCPClient.__init__(self, host, prog, vers, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 823, in __init__
    pmap = TCPPortMapperClient(host, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 803, in __init__
    RawTCPClient.__init__(self, host, PMAP_PROG, PMAP_VERS, PMAP_PORT, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 454, in __init__
    self.connect(1e-3 * open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 488, in connect
    raise RPCError("can't connect to server")
pyvisa_py.protocols.rpc.RPCError: can't connect to server

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 271, in _initial
    self.select_net_analyzer(network_analyzer_type)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 127, in select_net_analyzer
    self.net_analyzer = NetworkAnalyzer(net_analyzer, self.config_path)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 37, in __init__
    super(NetworkAnalyzer, self).__init__(instrument_model, config_path)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 29, in __init__
    self.inst = INSTRUMENT(config_path)[instrument_model]
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\INSTRUMENT.py", line 70, in __getitem__
    return self._get(secname)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\INSTRUMENT.py", line 75, in _get
    return self._INST_LST.get(self._type)(self._visa_address)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\structures.py", line 154, in __call__
    obj = super().__call__(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\E5071C.py", line 12, in __init__
    super().__init__(visa_address)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\visa_inst.py", line 35, in __init__
    self.inst_obj = rm.open_resource(self.visa_address)
  File "D:\miniconda3\lib\site-packages\pyvisa\highlevel.py", line 3284, in open_resource
    res.open(access_mode, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa\resources\resource.py", line 282, in open
    self.session, status = self._resource_manager.open_bare_resource(
  File "D:\miniconda3\lib\site-packages\pyvisa\highlevel.py", line 3209, in open_bare_resource
    return self.visalib.open(self.session, resource_name, access_mode, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\highlevel.py", line 168, in open
    sess = cls(session, resource_name, parsed, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 86, in __new__
    return newcls(resource_manager_session, resource_name, parsed, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\sessions.py", line 322, in __init__
    self.after_parsing()
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 459, in after_parsing
    raise errors.VisaIOError(constants.VI_ERROR_RSRC_NFOUND)
pyvisa.errors.VisaIOError: VI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.

68889988f6689fe5521f636f	Experiment Crash
--------------------------------------------------
BUS1-Channel1-BusS21Collector-688899cf4e0989b747fd439c
--------------------------------------------------
VI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.
--------------------------------------------------
Traceback (most recent call last):
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 457, in after_parsing
    self.interface = Vxi11CoreClient(host_address, port, self.open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 361, in __init__
    rpc.TCPClient.__init__(self, host, prog, vers, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 823, in __init__
    pmap = TCPPortMapperClient(host, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 803, in __init__
    RawTCPClient.__init__(self, host, PMAP_PROG, PMAP_VERS, PMAP_PORT, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 454, in __init__
    self.connect(1e-3 * open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\protocols\rpc.py", line 488, in connect
    raise RPCError("can't connect to server")
pyvisa_py.protocols.rpc.RPCError: can't connect to server

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 271, in _initial
    self.select_net_analyzer(network_analyzer_type)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 127, in select_net_analyzer
    self.net_analyzer = NetworkAnalyzer(net_analyzer, self.config_path)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 37, in __init__
    super(NetworkAnalyzer, self).__init__(instrument_model, config_path)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 29, in __init__
    self.inst = INSTRUMENT(config_path)[instrument_model]
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\INSTRUMENT.py", line 70, in __getitem__
    return self._get(secname)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\INSTRUMENT.py", line 75, in _get
    return self._INST_LST.get(self._type)(self._visa_address)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\structures.py", line 154, in __call__
    obj = super().__call__(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\E5071C.py", line 12, in __init__
    super().__init__(visa_address)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\visa_inst.py", line 35, in __init__
    self.inst_obj = rm.open_resource(self.visa_address)
  File "D:\miniconda3\lib\site-packages\pyvisa\highlevel.py", line 3284, in open_resource
    res.open(access_mode, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa\resources\resource.py", line 282, in open
    self.session, status = self._resource_manager.open_bare_resource(
  File "D:\miniconda3\lib\site-packages\pyvisa\highlevel.py", line 3209, in open_bare_resource
    return self.visalib.open(self.session, resource_name, access_mode, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\highlevel.py", line 168, in open
    sess = cls(session, resource_name, parsed, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 86, in __new__
    return newcls(resource_manager_session, resource_name, parsed, open_timeout)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\sessions.py", line 322, in __init__
    self.after_parsing()
  File "D:\miniconda3\lib\site-packages\pyvisa_py\tcpip.py", line 459, in after_parsing
    raise errors.VisaIOError(constants.VI_ERROR_RSRC_NFOUND)
pyvisa.errors.VisaIOError: VI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.

68955efc26465fe58213b5e7	Experiment Crash
--------------------------------------------------
q19q20q21q22q23q24-BUS4-Channel4-ImpaGain-68955efa973ee1f9ed36e62a
--------------------------------------------------
VI_ERROR_IO (-1073807298): Could not perform operation because of I/O error.
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\project\lzw\pyqcat-visage\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\project\lzw\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\project\lzw\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\project\lzw\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 271, in _initial
    self.select_net_analyzer(network_analyzer_type)
  File "D:\code\project\lzw\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 127, in select_net_analyzer
    self.net_analyzer = NetworkAnalyzer(net_analyzer, self.config_path)
  File "D:\code\project\lzw\pyqcat-visage\pyQCat\preliminary\preliminary_inst.py", line 38, in __init__
    self.inst.outputON()
  File "D:\code\project\lzw\pyqcat-visage\pyQCat\instrument\E5071C.py", line 16, in outputON
    self.inst_obj.write(':OUTPUT ON')
  File "C:\Users\<USER>\anaconda3\envs\visage\lib\site-packages\pyvisa\resources\messagebased.py", line 197, in write
    count = self.write_raw(message.encode(enco))
  File "C:\Users\<USER>\anaconda3\envs\visage\lib\site-packages\pyvisa\resources\messagebased.py", line 157, in write_raw
    return self.visalib.write(self.session, message)[0]
  File "C:\Users\<USER>\anaconda3\envs\visage\lib\site-packages\pyvisa_py\highlevel.py", line 548, in write
    return written, self.handle_return_value(session, status_code)
  File "C:\Users\<USER>\anaconda3\envs\visage\lib\site-packages\pyvisa\highlevel.py", line 251, in handle_return_value
    raise errors.VisaIOError(rv)
pyvisa.errors.VisaIOError: VI_ERROR_IO (-1073807298): Could not perform operation because of I/O error.

68955efc5dd635150013b5dc	Experiment Crash
--------------------------------------------------
bus-6-ImpaGain-68955f4dc4eedb107ae68e7f
--------------------------------------------------
VI_ERROR_IO (-1073807298): Could not perform operation because of I/O error.
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 329, in run
    self._run_experiment()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\library\impa_gain.py", line 213, in _run_experiment
    data = self._run_open_mode()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\library\impa_gain.py", line 159, in _run_open_mode
    _, amp_off, _ = self.net_analyzer.read_result(True, True, True)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 77, in read_result
    amp = self.inst.getAmpList()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\E5071C.py", line 162, in getAmpList
    AmpListStr = self.inst_obj.query(':CALC1:DATA:FDAT?')
  File "D:\miniconda3\lib\site-packages\pyvisa\resources\messagebased.py", line 648, in query
    return self.read()
  File "D:\miniconda3\lib\site-packages\pyvisa\resources\messagebased.py", line 486, in read
    message = self._read_raw().decode(enco)
  File "D:\miniconda3\lib\site-packages\pyvisa\resources\messagebased.py", line 442, in _read_raw
    chunk, status = self.visalib.read(self.session, size)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\highlevel.py", line 520, in read
    return data, self.handle_return_value(session, status_code)
  File "D:\miniconda3\lib\site-packages\pyvisa\highlevel.py", line 251, in handle_return_value
    raise errors.VisaIOError(rv)
pyvisa.errors.VisaIOError: VI_ERROR_IO (-1073807298): Could not perform operation because of I/O error.

68955f4c26465fe58213b5e8	Experiment Crash
--------------------------------------------------
q19q20q21q22q23q24-BUS4-Channel4-ImpaGain-68955f41973ee1f9ed36e630
--------------------------------------------------
VI_ERROR_IO (-1073807298): Could not perform operation because of I/O error.
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\project\lzw\pyqcat-visage\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\project\lzw\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\project\lzw\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 329, in run
    self._run_experiment()
  File "D:\code\project\lzw\pyqcat-visage\pyQCat\preliminary\library\impa_gain.py", line 210, in _run_experiment
    data = self._run_open_mode()
  File "D:\code\project\lzw\pyqcat-visage\pyQCat\preliminary\library\impa_gain.py", line 163, in _run_open_mode
    _, amp_on, _ = self.net_analyzer.read_result(True, True, True)
  File "D:\code\project\lzw\pyqcat-visage\pyQCat\preliminary\preliminary_inst.py", line 79, in read_result
    phase = self.inst.getExtPhaseList()
  File "D:\code\project\lzw\pyqcat-visage\pyQCat\instrument\E5071C.py", line 187, in getExtPhaseList
    ExtPhaseListStr = self.inst_obj.query(':CALC1:DATA:FDAT?')
  File "C:\Users\<USER>\anaconda3\envs\visage\lib\site-packages\pyvisa\resources\messagebased.py", line 648, in query
    return self.read()
  File "C:\Users\<USER>\anaconda3\envs\visage\lib\site-packages\pyvisa\resources\messagebased.py", line 486, in read
    message = self._read_raw().decode(enco)
  File "C:\Users\<USER>\anaconda3\envs\visage\lib\site-packages\pyvisa\resources\messagebased.py", line 442, in _read_raw
    chunk, status = self.visalib.read(self.session, size)
  File "C:\Users\<USER>\anaconda3\envs\visage\lib\site-packages\pyvisa_py\highlevel.py", line 520, in read
    return data, self.handle_return_value(session, status_code)
  File "C:\Users\<USER>\anaconda3\envs\visage\lib\site-packages\pyvisa\highlevel.py", line 251, in handle_return_value
    raise errors.VisaIOError(rv)
pyvisa.errors.VisaIOError: VI_ERROR_IO (-1073807298): Could not perform operation because of I/O error.

68955fe726465fe58213b5ea	Experiment Crash
--------------------------------------------------
bus-16-ImpaGain-68956039c4eedb107ae68ea0
--------------------------------------------------
VI_ERROR_IO (-1073807298): Could not perform operation because of I/O error.
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 329, in run
    self._run_experiment()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\library\impa_gain.py", line 213, in _run_experiment
    data = self._run_open_mode()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\library\impa_gain.py", line 159, in _run_open_mode
    _, amp_off, _ = self.net_analyzer.read_result(True, True, True)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 72, in read_result
    self.inst.singleTrig()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\E5071C.py", line 148, in singleTrig
    while self.isMeasuring():
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\E5071C.py", line 135, in isMeasuring
    runState = self.inst_obj.query(':STAT:OPER:COND?')
  File "D:\miniconda3\lib\site-packages\pyvisa\resources\messagebased.py", line 648, in query
    return self.read()
  File "D:\miniconda3\lib\site-packages\pyvisa\resources\messagebased.py", line 486, in read
    message = self._read_raw().decode(enco)
  File "D:\miniconda3\lib\site-packages\pyvisa\resources\messagebased.py", line 442, in _read_raw
    chunk, status = self.visalib.read(self.session, size)
  File "D:\miniconda3\lib\site-packages\pyvisa_py\highlevel.py", line 520, in read
    return data, self.handle_return_value(session, status_code)
  File "D:\miniconda3\lib\site-packages\pyvisa\highlevel.py", line 251, in handle_return_value
    raise errors.VisaIOError(rv)
pyvisa.errors.VisaIOError: VI_ERROR_IO (-1073807298): Could not perform operation because of I/O error.

68955fe726465fe58213b5eb	Experiment Crash
--------------------------------------------------
q25q26q27q28q29q30-BUS5-Channel5-ImpaGain-68955fdc973ee1f9ed36e636
--------------------------------------------------
VI_ERROR_IO (-1073807298): Could not perform operation because of I/O error.
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\project\lzw\pyqcat-visage\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\project\lzw\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\project\lzw\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 329, in run
    self._run_experiment()
  File "D:\code\project\lzw\pyqcat-visage\pyQCat\preliminary\library\impa_gain.py", line 210, in _run_experiment
    data = self._run_open_mode()
  File "D:\code\project\lzw\pyqcat-visage\pyQCat\preliminary\library\impa_gain.py", line 163, in _run_open_mode
    _, amp_on, _ = self.net_analyzer.read_result(True, True, True)
  File "D:\code\project\lzw\pyqcat-visage\pyQCat\preliminary\preliminary_inst.py", line 79, in read_result
    phase = self.inst.getExtPhaseList()
  File "D:\code\project\lzw\pyqcat-visage\pyQCat\instrument\E5071C.py", line 186, in getExtPhaseList
    self.inst_obj.write(':CALC1:FORM UPH\n')
  File "C:\Users\<USER>\anaconda3\envs\visage\lib\site-packages\pyvisa\resources\messagebased.py", line 197, in write
    count = self.write_raw(message.encode(enco))
  File "C:\Users\<USER>\anaconda3\envs\visage\lib\site-packages\pyvisa\resources\messagebased.py", line 157, in write_raw
    return self.visalib.write(self.session, message)[0]
  File "C:\Users\<USER>\anaconda3\envs\visage\lib\site-packages\pyvisa_py\highlevel.py", line 548, in write
    return written, self.handle_return_value(session, status_code)
  File "C:\Users\<USER>\anaconda3\envs\visage\lib\site-packages\pyvisa\highlevel.py", line 251, in handle_return_value
    raise errors.VisaIOError(rv)
pyvisa.errors.VisaIOError: VI_ERROR_IO (-1073807298): Could not perform operation because of I/O error.
