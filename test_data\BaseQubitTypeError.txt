_id	message
682d711ea77f347882573a21	Experiment Fail
--------------------------------------------------
q1-ReadoutPowerF02Calibrate-SingleShotF02_02(1-21)-probe_power=-35
--------------------------------------------------
<Base Qubit type error> | F12 drive freq is null
--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 543, in _async_compile
    experiment_file: ExperimentFile = build_experiment_message(exp_env)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 126, in build_experiment_message
    exp_env.set_xy_pulses(builder)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 155, in set_xy_pulses
    xy_pulse_list = self.create_xy_pulses(self.qubit, level_str, loop_num)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 167, in create_xy_pulses
    drive_two_pulse = self.stimulate_state_two(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 195, in stimulate_state_two
    f12_pi = f12_pi_pulse(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 146, in f12_pi_pulse
    drag = _f12_drag_pulse_creator(qubit, 'pi')
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 117, in _f12_drag_pulse_creator
    raise BaseQubitTypeError("F12 drive freq is null")
pyQCat.errors.BaseQubitTypeError: <Base Qubit type error> | F12 drive freq is null

682d711ea77f347882573a22	Experiment Fail
--------------------------------------------------
q1-ReadoutPowerF02Calibrate-SingleShotF02_02(3-21)-probe_power=-33
--------------------------------------------------
<Base Qubit type error> | F12 drive freq is null
--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 543, in _async_compile
    experiment_file: ExperimentFile = build_experiment_message(exp_env)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 126, in build_experiment_message
    exp_env.set_xy_pulses(builder)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 155, in set_xy_pulses
    xy_pulse_list = self.create_xy_pulses(self.qubit, level_str, loop_num)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 167, in create_xy_pulses
    drive_two_pulse = self.stimulate_state_two(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 195, in stimulate_state_two
    f12_pi = f12_pi_pulse(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 146, in f12_pi_pulse
    drag = _f12_drag_pulse_creator(qubit, 'pi')
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 117, in _f12_drag_pulse_creator
    raise BaseQubitTypeError("F12 drive freq is null")
pyQCat.errors.BaseQubitTypeError: <Base Qubit type error> | F12 drive freq is null

682d711ea77f347882573a23	Experiment Fail
--------------------------------------------------
q1-ReadoutPowerF02Calibrate-SingleShotF02_02(5-21)-probe_power=-31
--------------------------------------------------
<Base Qubit type error> | F12 drive freq is null
--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 543, in _async_compile
    experiment_file: ExperimentFile = build_experiment_message(exp_env)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 126, in build_experiment_message
    exp_env.set_xy_pulses(builder)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 155, in set_xy_pulses
    xy_pulse_list = self.create_xy_pulses(self.qubit, level_str, loop_num)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 167, in create_xy_pulses
    drive_two_pulse = self.stimulate_state_two(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 195, in stimulate_state_two
    f12_pi = f12_pi_pulse(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 146, in f12_pi_pulse
    drag = _f12_drag_pulse_creator(qubit, 'pi')
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 117, in _f12_drag_pulse_creator
    raise BaseQubitTypeError("F12 drive freq is null")
pyQCat.errors.BaseQubitTypeError: <Base Qubit type error> | F12 drive freq is null

682d711ea77f347882573a24	Experiment Fail
--------------------------------------------------
q1-ReadoutPowerF02Calibrate-SingleShotF02_02(9-21)-probe_power=-27
--------------------------------------------------
<Base Qubit type error> | F12 drive freq is null
--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 543, in _async_compile
    experiment_file: ExperimentFile = build_experiment_message(exp_env)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 126, in build_experiment_message
    exp_env.set_xy_pulses(builder)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 155, in set_xy_pulses
    xy_pulse_list = self.create_xy_pulses(self.qubit, level_str, loop_num)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 167, in create_xy_pulses
    drive_two_pulse = self.stimulate_state_two(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 195, in stimulate_state_two
    f12_pi = f12_pi_pulse(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 146, in f12_pi_pulse
    drag = _f12_drag_pulse_creator(qubit, 'pi')
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 117, in _f12_drag_pulse_creator
    raise BaseQubitTypeError("F12 drive freq is null")
pyQCat.errors.BaseQubitTypeError: <Base Qubit type error> | F12 drive freq is null

682d711ea77f347882573a25	Experiment Fail
--------------------------------------------------
q1-ReadoutPowerF02Calibrate-SingleShotF02_02(7-21)-probe_power=-29
--------------------------------------------------
<Base Qubit type error> | F12 drive freq is null
--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 543, in _async_compile
    experiment_file: ExperimentFile = build_experiment_message(exp_env)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 126, in build_experiment_message
    exp_env.set_xy_pulses(builder)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 155, in set_xy_pulses
    xy_pulse_list = self.create_xy_pulses(self.qubit, level_str, loop_num)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 167, in create_xy_pulses
    drive_two_pulse = self.stimulate_state_two(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 195, in stimulate_state_two
    f12_pi = f12_pi_pulse(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 146, in f12_pi_pulse
    drag = _f12_drag_pulse_creator(qubit, 'pi')
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 117, in _f12_drag_pulse_creator
    raise BaseQubitTypeError("F12 drive freq is null")
pyQCat.errors.BaseQubitTypeError: <Base Qubit type error> | F12 drive freq is null

682d711ea77f347882573a26	Experiment Fail
--------------------------------------------------
q1-ReadoutPowerF02Calibrate-SingleShotF02_02(2-21)-probe_power=-34
--------------------------------------------------
<Base Qubit type error> | F12 drive freq is null
--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 543, in _async_compile
    experiment_file: ExperimentFile = build_experiment_message(exp_env)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 126, in build_experiment_message
    exp_env.set_xy_pulses(builder)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 155, in set_xy_pulses
    xy_pulse_list = self.create_xy_pulses(self.qubit, level_str, loop_num)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 167, in create_xy_pulses
    drive_two_pulse = self.stimulate_state_two(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 195, in stimulate_state_two
    f12_pi = f12_pi_pulse(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 146, in f12_pi_pulse
    drag = _f12_drag_pulse_creator(qubit, 'pi')
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 117, in _f12_drag_pulse_creator
    raise BaseQubitTypeError("F12 drive freq is null")
pyQCat.errors.BaseQubitTypeError: <Base Qubit type error> | F12 drive freq is null

682d711ea77f347882573a27	Experiment Fail
--------------------------------------------------
q1-ReadoutPowerF02Calibrate-SingleShotF02_02(4-21)-probe_power=-32
--------------------------------------------------
<Base Qubit type error> | F12 drive freq is null
--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 543, in _async_compile
    experiment_file: ExperimentFile = build_experiment_message(exp_env)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 126, in build_experiment_message
    exp_env.set_xy_pulses(builder)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 155, in set_xy_pulses
    xy_pulse_list = self.create_xy_pulses(self.qubit, level_str, loop_num)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 167, in create_xy_pulses
    drive_two_pulse = self.stimulate_state_two(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 195, in stimulate_state_two
    f12_pi = f12_pi_pulse(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 146, in f12_pi_pulse
    drag = _f12_drag_pulse_creator(qubit, 'pi')
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 117, in _f12_drag_pulse_creator
    raise BaseQubitTypeError("F12 drive freq is null")
pyQCat.errors.BaseQubitTypeError: <Base Qubit type error> | F12 drive freq is null

682d711fa77f347882573a28	Experiment Fail
--------------------------------------------------
q1-ReadoutPowerF02Calibrate-SingleShotF02_02(6-21)-probe_power=-30
--------------------------------------------------
<Base Qubit type error> | F12 drive freq is null
--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 543, in _async_compile
    experiment_file: ExperimentFile = build_experiment_message(exp_env)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 126, in build_experiment_message
    exp_env.set_xy_pulses(builder)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 155, in set_xy_pulses
    xy_pulse_list = self.create_xy_pulses(self.qubit, level_str, loop_num)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 167, in create_xy_pulses
    drive_two_pulse = self.stimulate_state_two(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 195, in stimulate_state_two
    f12_pi = f12_pi_pulse(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 146, in f12_pi_pulse
    drag = _f12_drag_pulse_creator(qubit, 'pi')
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 117, in _f12_drag_pulse_creator
    raise BaseQubitTypeError("F12 drive freq is null")
pyQCat.errors.BaseQubitTypeError: <Base Qubit type error> | F12 drive freq is null

682d711fa77f347882573a29	Experiment Fail
--------------------------------------------------
q1-ReadoutPowerF02Calibrate-SingleShotF02_02(8-21)-probe_power=-28
--------------------------------------------------
<Base Qubit type error> | F12 drive freq is null
--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 543, in _async_compile
    experiment_file: ExperimentFile = build_experiment_message(exp_env)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 126, in build_experiment_message
    exp_env.set_xy_pulses(builder)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 155, in set_xy_pulses
    xy_pulse_list = self.create_xy_pulses(self.qubit, level_str, loop_num)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 167, in create_xy_pulses
    drive_two_pulse = self.stimulate_state_two(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 195, in stimulate_state_two
    f12_pi = f12_pi_pulse(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 146, in f12_pi_pulse
    drag = _f12_drag_pulse_creator(qubit, 'pi')
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 117, in _f12_drag_pulse_creator
    raise BaseQubitTypeError("F12 drive freq is null")
pyQCat.errors.BaseQubitTypeError: <Base Qubit type error> | F12 drive freq is null

682d711fa77f347882573a2a	Experiment Fail
--------------------------------------------------
q1-ReadoutPowerF02Calibrate-SingleShotF02_02(10-21)-probe_power=-26
--------------------------------------------------
<Base Qubit type error> | F12 drive freq is null
--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 543, in _async_compile
    experiment_file: ExperimentFile = build_experiment_message(exp_env)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 126, in build_experiment_message
    exp_env.set_xy_pulses(builder)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 155, in set_xy_pulses
    xy_pulse_list = self.create_xy_pulses(self.qubit, level_str, loop_num)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 167, in create_xy_pulses
    drive_two_pulse = self.stimulate_state_two(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 195, in stimulate_state_two
    f12_pi = f12_pi_pulse(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 146, in f12_pi_pulse
    drag = _f12_drag_pulse_creator(qubit, 'pi')
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 117, in _f12_drag_pulse_creator
    raise BaseQubitTypeError("F12 drive freq is null")
pyQCat.errors.BaseQubitTypeError: <Base Qubit type error> | F12 drive freq is null

682d711fa77f347882573a2b	Experiment Fail
--------------------------------------------------
q1-ReadoutPowerF02Calibrate-SingleShotF02_02(17-21)-probe_power=-19
--------------------------------------------------
<Base Qubit type error> | F12 drive freq is null
--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 543, in _async_compile
    experiment_file: ExperimentFile = build_experiment_message(exp_env)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 126, in build_experiment_message
    exp_env.set_xy_pulses(builder)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 155, in set_xy_pulses
    xy_pulse_list = self.create_xy_pulses(self.qubit, level_str, loop_num)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 167, in create_xy_pulses
    drive_two_pulse = self.stimulate_state_two(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 195, in stimulate_state_two
    f12_pi = f12_pi_pulse(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 146, in f12_pi_pulse
    drag = _f12_drag_pulse_creator(qubit, 'pi')
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 117, in _f12_drag_pulse_creator
    raise BaseQubitTypeError("F12 drive freq is null")
pyQCat.errors.BaseQubitTypeError: <Base Qubit type error> | F12 drive freq is null

682d711fa77f347882573a2c	Experiment Fail
--------------------------------------------------
q1-ReadoutPowerF02Calibrate-SingleShotF02_02(11-21)-probe_power=-25
--------------------------------------------------
<Base Qubit type error> | F12 drive freq is null
--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 543, in _async_compile
    experiment_file: ExperimentFile = build_experiment_message(exp_env)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 126, in build_experiment_message
    exp_env.set_xy_pulses(builder)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 155, in set_xy_pulses
    xy_pulse_list = self.create_xy_pulses(self.qubit, level_str, loop_num)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 167, in create_xy_pulses
    drive_two_pulse = self.stimulate_state_two(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 195, in stimulate_state_two
    f12_pi = f12_pi_pulse(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 146, in f12_pi_pulse
    drag = _f12_drag_pulse_creator(qubit, 'pi')
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 117, in _f12_drag_pulse_creator
    raise BaseQubitTypeError("F12 drive freq is null")
pyQCat.errors.BaseQubitTypeError: <Base Qubit type error> | F12 drive freq is null

682d711fa77f347882573a2d	Experiment Fail
--------------------------------------------------
q1-ReadoutPowerF02Calibrate-SingleShotF02_02(20-21)-probe_power=-16
--------------------------------------------------
<Base Qubit type error> | F12 drive freq is null
--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 543, in _async_compile
    experiment_file: ExperimentFile = build_experiment_message(exp_env)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 126, in build_experiment_message
    exp_env.set_xy_pulses(builder)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 155, in set_xy_pulses
    xy_pulse_list = self.create_xy_pulses(self.qubit, level_str, loop_num)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 167, in create_xy_pulses
    drive_two_pulse = self.stimulate_state_two(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 195, in stimulate_state_two
    f12_pi = f12_pi_pulse(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 146, in f12_pi_pulse
    drag = _f12_drag_pulse_creator(qubit, 'pi')
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 117, in _f12_drag_pulse_creator
    raise BaseQubitTypeError("F12 drive freq is null")
pyQCat.errors.BaseQubitTypeError: <Base Qubit type error> | F12 drive freq is null

682d711fa77f347882573a2e	Experiment Fail
--------------------------------------------------
q1-ReadoutPowerF02Calibrate-SingleShotF02_02(13-21)-probe_power=-23
--------------------------------------------------
<Base Qubit type error> | F12 drive freq is null
--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 543, in _async_compile
    experiment_file: ExperimentFile = build_experiment_message(exp_env)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 126, in build_experiment_message
    exp_env.set_xy_pulses(builder)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 155, in set_xy_pulses
    xy_pulse_list = self.create_xy_pulses(self.qubit, level_str, loop_num)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 167, in create_xy_pulses
    drive_two_pulse = self.stimulate_state_two(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 195, in stimulate_state_two
    f12_pi = f12_pi_pulse(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 146, in f12_pi_pulse
    drag = _f12_drag_pulse_creator(qubit, 'pi')
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 117, in _f12_drag_pulse_creator
    raise BaseQubitTypeError("F12 drive freq is null")
pyQCat.errors.BaseQubitTypeError: <Base Qubit type error> | F12 drive freq is null

682d711fa77f347882573a2f	Experiment Fail
--------------------------------------------------
q1-ReadoutPowerF02Calibrate-SingleShotF02_02(18-21)-probe_power=-18
--------------------------------------------------
<Base Qubit type error> | F12 drive freq is null
--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 543, in _async_compile
    experiment_file: ExperimentFile = build_experiment_message(exp_env)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 126, in build_experiment_message
    exp_env.set_xy_pulses(builder)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 155, in set_xy_pulses
    xy_pulse_list = self.create_xy_pulses(self.qubit, level_str, loop_num)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 167, in create_xy_pulses
    drive_two_pulse = self.stimulate_state_two(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 195, in stimulate_state_two
    f12_pi = f12_pi_pulse(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 146, in f12_pi_pulse
    drag = _f12_drag_pulse_creator(qubit, 'pi')
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 117, in _f12_drag_pulse_creator
    raise BaseQubitTypeError("F12 drive freq is null")
pyQCat.errors.BaseQubitTypeError: <Base Qubit type error> | F12 drive freq is null

682d711fa77f347882573a30	Experiment Fail
--------------------------------------------------
q1-ReadoutPowerF02Calibrate-SingleShotF02_02(12-21)-probe_power=-24
--------------------------------------------------
<Base Qubit type error> | F12 drive freq is null
--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 543, in _async_compile
    experiment_file: ExperimentFile = build_experiment_message(exp_env)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 126, in build_experiment_message
    exp_env.set_xy_pulses(builder)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 155, in set_xy_pulses
    xy_pulse_list = self.create_xy_pulses(self.qubit, level_str, loop_num)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 167, in create_xy_pulses
    drive_two_pulse = self.stimulate_state_two(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 195, in stimulate_state_two
    f12_pi = f12_pi_pulse(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 146, in f12_pi_pulse
    drag = _f12_drag_pulse_creator(qubit, 'pi')
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 117, in _f12_drag_pulse_creator
    raise BaseQubitTypeError("F12 drive freq is null")
pyQCat.errors.BaseQubitTypeError: <Base Qubit type error> | F12 drive freq is null

682d711fa77f347882573a31	Experiment Fail
--------------------------------------------------
q1-ReadoutPowerF02Calibrate-SingleShotF02_02(15-21)-probe_power=-21
--------------------------------------------------
<Base Qubit type error> | F12 drive freq is null
--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 543, in _async_compile
    experiment_file: ExperimentFile = build_experiment_message(exp_env)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 126, in build_experiment_message
    exp_env.set_xy_pulses(builder)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 155, in set_xy_pulses
    xy_pulse_list = self.create_xy_pulses(self.qubit, level_str, loop_num)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 167, in create_xy_pulses
    drive_two_pulse = self.stimulate_state_two(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 195, in stimulate_state_two
    f12_pi = f12_pi_pulse(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 146, in f12_pi_pulse
    drag = _f12_drag_pulse_creator(qubit, 'pi')
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 117, in _f12_drag_pulse_creator
    raise BaseQubitTypeError("F12 drive freq is null")
pyQCat.errors.BaseQubitTypeError: <Base Qubit type error> | F12 drive freq is null

682d711fa77f347882573a32	Experiment Fail
--------------------------------------------------
q1-ReadoutPowerF02Calibrate-SingleShotF02_02(19-21)-probe_power=-17
--------------------------------------------------
<Base Qubit type error> | F12 drive freq is null
--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 543, in _async_compile
    experiment_file: ExperimentFile = build_experiment_message(exp_env)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 126, in build_experiment_message
    exp_env.set_xy_pulses(builder)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 155, in set_xy_pulses
    xy_pulse_list = self.create_xy_pulses(self.qubit, level_str, loop_num)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 167, in create_xy_pulses
    drive_two_pulse = self.stimulate_state_two(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 195, in stimulate_state_two
    f12_pi = f12_pi_pulse(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 146, in f12_pi_pulse
    drag = _f12_drag_pulse_creator(qubit, 'pi')
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 117, in _f12_drag_pulse_creator
    raise BaseQubitTypeError("F12 drive freq is null")
pyQCat.errors.BaseQubitTypeError: <Base Qubit type error> | F12 drive freq is null

682d711fa77f347882573a33	Experiment Fail
--------------------------------------------------
q1-ReadoutPowerF02Calibrate-SingleShotF02_02(14-21)-probe_power=-22
--------------------------------------------------
<Base Qubit type error> | F12 drive freq is null
--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 543, in _async_compile
    experiment_file: ExperimentFile = build_experiment_message(exp_env)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 126, in build_experiment_message
    exp_env.set_xy_pulses(builder)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 155, in set_xy_pulses
    xy_pulse_list = self.create_xy_pulses(self.qubit, level_str, loop_num)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 167, in create_xy_pulses
    drive_two_pulse = self.stimulate_state_two(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 195, in stimulate_state_two
    f12_pi = f12_pi_pulse(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 146, in f12_pi_pulse
    drag = _f12_drag_pulse_creator(qubit, 'pi')
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 117, in _f12_drag_pulse_creator
    raise BaseQubitTypeError("F12 drive freq is null")
pyQCat.errors.BaseQubitTypeError: <Base Qubit type error> | F12 drive freq is null

682d831fa77f347882573ab7	Experiment Fail
--------------------------------------------------
q1-F12Calibration-RamseyF12(2-2)-fringe=-25MHz
--------------------------------------------------
<Base Qubit type error> | F12 drive freq is null
--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 543, in _async_compile
    experiment_file: ExperimentFile = build_experiment_message(exp_env)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 126, in build_experiment_message
    exp_env.set_xy_pulses(builder)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\single_gate\f12_ramsey.py", line 31, in set_xy_pulses
    xy_pulse_list = RamseyF12.get_xy_pulse(
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\single_gate\f12_ramsey.py", line 50, in get_xy_pulse
    front_drag = half_f12_pi_pulse(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 151, in half_f12_pi_pulse
    drag = _f12_drag_pulse_creator(qubit, 'pi/2')
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 117, in _f12_drag_pulse_creator
    raise BaseQubitTypeError("F12 drive freq is null")
pyQCat.errors.BaseQubitTypeError: <Base Qubit type error> | F12 drive freq is null

682d831fa77f347882573ab8	Experiment Fail
--------------------------------------------------
q1-F12Calibration-RamseyF12(1-2)-fringe=25MHz
--------------------------------------------------
<Base Qubit type error> | F12 drive freq is null
--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 543, in _async_compile
    experiment_file: ExperimentFile = build_experiment_message(exp_env)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 126, in build_experiment_message
    exp_env.set_xy_pulses(builder)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\single_gate\f12_ramsey.py", line 31, in set_xy_pulses
    xy_pulse_list = RamseyF12.get_xy_pulse(
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\single_gate\f12_ramsey.py", line 50, in get_xy_pulse
    front_drag = half_f12_pi_pulse(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 151, in half_f12_pi_pulse
    drag = _f12_drag_pulse_creator(qubit, 'pi/2')
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 117, in _f12_drag_pulse_creator
    raise BaseQubitTypeError("F12 drive freq is null")
pyQCat.errors.BaseQubitTypeError: <Base Qubit type error> | F12 drive freq is null

682d83f5a77f347882573ac2	Experiment Fail
--------------------------------------------------
q1-RabiScanWidthF12
--------------------------------------------------
<Base Qubit type error> | F12 drive freq is null
--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 543, in _async_compile
    experiment_file: ExperimentFile = build_experiment_message(exp_env)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 126, in build_experiment_message
    exp_env.set_xy_pulses(builder)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\single_gate\f12_rabi.py", line 51, in set_xy_pulses
    pulse_list = RabiScanWidthF12.get_xy_pulse(
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\single_gate\f12_rabi.py", line 68, in get_xy_pulse
    f12_pulse = f12_pi_pulse(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 146, in f12_pi_pulse
    drag = _f12_drag_pulse_creator(qubit, 'pi')
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 117, in _f12_drag_pulse_creator
    raise BaseQubitTypeError("F12 drive freq is null")
pyQCat.errors.BaseQubitTypeError: <Base Qubit type error> | F12 drive freq is null

682d83f5a77f347882573ac3	Experiment Fail
--------------------------------------------------
q1-RabiScanAmpF12
--------------------------------------------------
<Base Qubit type error> | F12 drive freq is null
--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 543, in _async_compile
    experiment_file: ExperimentFile = build_experiment_message(exp_env)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 126, in build_experiment_message
    exp_env.set_xy_pulses(builder)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\single_gate\f12_rabi.py", line 149, in set_xy_pulses
    pulse_list = RabiScanAmpF12.get_xy_pulse(
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\single_gate\f12_rabi.py", line 159, in get_xy_pulse
    f12_pulse = f12_pi_pulse(qubit)(time=qubit.f12_options.time)
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 146, in f12_pi_pulse
    drag = _f12_drag_pulse_creator(qubit, 'pi')
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 117, in _f12_drag_pulse_creator
    raise BaseQubitTypeError("F12 drive freq is null")
pyQCat.errors.BaseQubitTypeError: <Base Qubit type error> | F12 drive freq is null

682d83f5a77f347882573ac4	Experiment Fail
--------------------------------------------------
q1-RamseyF12
--------------------------------------------------
<Base Qubit type error> | F12 drive freq is null
--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 543, in _async_compile
    experiment_file: ExperimentFile = build_experiment_message(exp_env)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 126, in build_experiment_message
    exp_env.set_xy_pulses(builder)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\single_gate\f12_ramsey.py", line 31, in set_xy_pulses
    xy_pulse_list = RamseyF12.get_xy_pulse(
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\single_gate\f12_ramsey.py", line 50, in get_xy_pulse
    front_drag = half_f12_pi_pulse(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 151, in half_f12_pi_pulse
    drag = _f12_drag_pulse_creator(qubit, 'pi/2')
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 117, in _f12_drag_pulse_creator
    raise BaseQubitTypeError("F12 drive freq is null")
pyQCat.errors.BaseQubitTypeError: <Base Qubit type error> | F12 drive freq is null

682d8401a77f347882573ac5	Experiment Fail
--------------------------------------------------
q1-SingleShotF012_012
--------------------------------------------------
<Base Qubit type error> | F12 drive freq is null
--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 543, in _async_compile
    experiment_file: ExperimentFile = build_experiment_message(exp_env)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 126, in build_experiment_message
    exp_env.set_xy_pulses(builder)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 155, in set_xy_pulses
    xy_pulse_list = self.create_xy_pulses(self.qubit, level_str, loop_num)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 171, in create_xy_pulses
    drive_two_pulse = self.stimulate_state_two(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 195, in stimulate_state_two
    f12_pi = f12_pi_pulse(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 146, in f12_pi_pulse
    drag = _f12_drag_pulse_creator(qubit, 'pi')
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 117, in _f12_drag_pulse_creator
    raise BaseQubitTypeError("F12 drive freq is null")
pyQCat.errors.BaseQubitTypeError: <Base Qubit type error> | F12 drive freq is null

682d8401a77f347882573ac6	Experiment Fail
--------------------------------------------------
q1-SingleShotF02_02
--------------------------------------------------
<Base Qubit type error> | F12 drive freq is null
--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 543, in _async_compile
    experiment_file: ExperimentFile = build_experiment_message(exp_env)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 126, in build_experiment_message
    exp_env.set_xy_pulses(builder)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 155, in set_xy_pulses
    xy_pulse_list = self.create_xy_pulses(self.qubit, level_str, loop_num)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 167, in create_xy_pulses
    drive_two_pulse = self.stimulate_state_two(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\readout\single_shot.py", line 195, in stimulate_state_two
    f12_pi = f12_pi_pulse(qubit)
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 146, in f12_pi_pulse
    drag = _f12_drag_pulse_creator(qubit, 'pi')
  File "F:\Monitor\pyqcat-apps\pyQCat\pulse\pulse_function.py", line 117, in _f12_drag_pulse_creator
    raise BaseQubitTypeError("F12 drive freq is null")
pyQCat.errors.BaseQubitTypeError: <Base Qubit type error> | F12 drive freq is null
