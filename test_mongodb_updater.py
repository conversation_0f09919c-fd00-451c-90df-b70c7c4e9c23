#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MongoDB批量更新脚本测试
作者: KangKang Geng
功能: 测试MongoDB更新脚本的各项功能
"""

import unittest
import tempfile
import json
import os
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from batch_update_mongodb import MongoDBUpdater


class TestMongoDBUpdater(unittest.TestCase):
    """MongoDB更新器测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时配置文件
        self.temp_config = {
            "host": "localhost",
            "port": 27017,
            "db_name": "test_db",
            "collection_name": "test_collection",
            "username": "test_user",
            "password": "test_pass",
            "authSource": "admin"
        }
        
        # 创建临时配置文件
        self.config_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        json.dump(self.temp_config, self.config_file)
        self.config_file.close()
        
        # 创建临时txt文件
        self.txt_content = """_id\tmessage
683821adf04f1fe4feb73d40\tExperiment Crash
68382f44f04f1fe4feb73d43\tExperiment Crash
68391830f04f1fe4feb73e43\tExperiment Crash
683940dff04f1fe4feb73f9a\tExperiment Crash
683940f1f04f1fe4feb74124\tExperiment Crash"""
        
        self.txt_file = tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False)
        self.txt_file.write(self.txt_content)
        self.txt_file.close()
        
    def tearDown(self):
        """测试后清理"""
        # 删除临时文件
        os.unlink(self.config_file.name)
        os.unlink(self.txt_file.name)
        
    def test_load_config_success(self):
        """测试配置文件加载成功"""
        updater = MongoDBUpdater(self.config_file.name)
        self.assertEqual(updater.config['host'], 'localhost')
        self.assertEqual(updater.config['port'], 27017)
        
    def test_load_config_file_not_found(self):
        """测试配置文件不存在"""
        with self.assertRaises(FileNotFoundError):
            MongoDBUpdater('nonexistent_config.json')
            
    def test_parse_txt_file_success(self):
        """测试txt文件解析成功"""
        updater = MongoDBUpdater(self.config_file.name)
        ids = updater.parse_txt_file(self.txt_file.name, 2, 4)
        
        expected_ids = [
            '683821adf04f1fe4feb73d40',
            '68382f44f04f1fe4feb73d43',
            '68391830f04f1fe4feb73e43'
        ]
        self.assertEqual(ids, expected_ids)
        
    def test_parse_txt_file_invalid_range(self):
        """测试无效的行号范围"""
        updater = MongoDBUpdater(self.config_file.name)
        
        with self.assertRaises(ValueError):
            updater.parse_txt_file(self.txt_file.name, 0, 5)
            
        with self.assertRaises(ValueError):
            updater.parse_txt_file(self.txt_file.name, 5, 2)
            
    def test_parse_txt_file_not_found(self):
        """测试txt文件不存在"""
        updater = MongoDBUpdater(self.config_file.name)
        
        with self.assertRaises(FileNotFoundError):
            updater.parse_txt_file('nonexistent_file.txt', 1, 5)
            
    @patch('batch_update_mongodb.MongoClient')
    def test_connect_success(self, mock_mongo_client):
        """测试数据库连接成功"""
        # 模拟成功连接
        mock_client = Mock()
        mock_client.admin.command.return_value = True
        mock_mongo_client.return_value = mock_client
        
        updater = MongoDBUpdater(self.config_file.name)
        result = updater.connect()
        
        self.assertTrue(result)
        self.assertIsNotNone(updater.client)
        
    @patch('batch_update_mongodb.MongoClient')
    def test_connect_failure(self, mock_mongo_client):
        """测试数据库连接失败"""
        # 模拟连接失败
        mock_mongo_client.side_effect = Exception("Connection failed")
        
        updater = MongoDBUpdater(self.config_file.name)
        result = updater.connect()
        
        self.assertFalse(result)
        
    @patch('batch_update_mongodb.MongoClient')
    def test_update_tackle_field(self, mock_mongo_client):
        """测试tackle字段更新"""
        # 模拟数据库连接和操作
        mock_client = Mock()
        mock_collection = Mock()
        mock_client.admin.command.return_value = True
        mock_client.__getitem__.return_value.__getitem__.return_value = mock_collection
        mock_mongo_client.return_value = mock_client
        
        # 模拟更新结果
        mock_result = Mock()
        mock_result.matched_count = 1
        mock_collection.update_one.return_value = mock_result
        
        updater = MongoDBUpdater(self.config_file.name)
        updater.connect()
        
        object_ids = ['683821adf04f1fe4feb73d40', '68382f44f04f1fe4feb73d43']
        stats = updater.update_tackle_field(object_ids, 1)
        
        self.assertEqual(stats['total'], 2)
        self.assertEqual(stats['updated'], 2)
        self.assertEqual(stats['not_found'], 0)
        self.assertEqual(stats['errors'], 0)


class TestIntegration(unittest.TestCase):
    """集成测试类"""
    
    def test_file_parsing_with_real_data(self):
        """使用真实数据测试文件解析"""
        # 检查是否存在真实的txt文件
        txt_file_path = Path('txt_data/AttributeError.txt')
        if not txt_file_path.exists():
            self.skipTest("真实数据文件不存在，跳过集成测试")
            
        config_file_path = Path('db_config.json')
        if not config_file_path.exists():
            self.skipTest("配置文件不存在，跳过集成测试")
            
        updater = MongoDBUpdater('db_config.json')
        
        try:
            # 测试解析前10行
            ids = updater.parse_txt_file('txt_data/AttributeError.txt', 2, 11)
            
            # 验证结果
            self.assertIsInstance(ids, list)
            self.assertGreater(len(ids), 0)
            
            # 验证ID格式（应该是24位十六进制字符串）
            for oid in ids:
                self.assertEqual(len(oid), 24)
                self.assertTrue(all(c in '0123456789abcdef' for c in oid.lower()))
                
        except Exception as e:
            self.fail(f"集成测试失败: {e}")


def run_basic_functionality_test():
    """运行基本功能测试"""
    print("MongoDB批量更新脚本 - 基本功能测试")
    print("=" * 50)
    
    # 检查配置文件
    config_path = Path('db_config.json')
    if not config_path.exists():
        print("❌ 配置文件 db_config.json 不存在")
        return False
        
    print("✅ 配置文件存在")
    
    # 检查txt数据文件
    txt_dir = Path('txt_data')
    if not txt_dir.exists():
        print("❌ txt_data 目录不存在")
        return False
        
    txt_files = list(txt_dir.glob('*.txt'))
    if not txt_files:
        print("❌ txt_data 目录中没有找到txt文件")
        return False
        
    print(f"✅ 找到 {len(txt_files)} 个txt文件")
    
    # 测试配置文件加载
    try:
        updater = MongoDBUpdater('db_config.json')
        print("✅ 配置文件加载成功")
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        return False
        
    # 测试文件解析
    try:
        test_file = txt_files[0]
        ids = updater.parse_txt_file(str(test_file), 2, 5)
        print(f"✅ 文件解析成功，提取到 {len(ids)} 个ID")
        
        # 显示前几个ID作为示例
        if ids:
            print("   示例ID:")
            for i, oid in enumerate(ids[:3]):
                print(f"   {i+1}. {oid}")
                
    except Exception as e:
        print(f"❌ 文件解析失败: {e}")
        return False
        
    print("=" * 50)
    print("✅ 基本功能测试通过")
    print("\n注意: 这只是基本功能测试，实际数据库连接需要在真实环境中测试")
    return True


if __name__ == "__main__":
    print("选择测试模式:")
    print("1. 运行单元测试")
    print("2. 运行基本功能测试")
    print("3. 运行所有测试")
    
    choice = input("请输入选择 (1/2/3): ").strip()
    
    if choice == "1":
        # 运行单元测试
        unittest.main(argv=[''], exit=False, verbosity=2)
    elif choice == "2":
        # 运行基本功能测试
        run_basic_functionality_test()
    elif choice == "3":
        # 运行所有测试
        print("运行基本功能测试...")
        run_basic_functionality_test()
        print("\n" + "="*50)
        print("运行单元测试...")
        unittest.main(argv=[''], exit=False, verbosity=2)
    else:
        print("无效选择")
