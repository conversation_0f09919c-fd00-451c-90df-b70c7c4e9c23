import os
import json
from collections import defaultdict


def process_json_files(input_dir, output_dir):
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 要过滤的用户名
    filtered_users = {"zyc", "BY250013", "gkk", "job"}
    # 统计每个被过滤用户的记录数
    filter_count = defaultdict(int)

    # 遍历输入目录中的所有文件
    for filename in os.listdir(input_dir):
        # 只处理JSON文件
        if not filename.endswith(".json"):
            continue

        input_path = os.path.join(input_dir, filename)
        output_path = os.path.join(output_dir, f"{os.path.splitext(filename)[0]}.txt")

        try:
            # 读取JSON文件
            with open(input_path, "r", encoding="utf-8") as f:
                data = json.load(f)

            # 确保数据是列表格式
            if not isinstance(data, list):
                print(f"警告：{filename} 中的数据不是列表格式，已跳过")
                continue

            # 处理每个条目
            results = []
            for item in data:
                # 检查必要字段是否存在
                if "username" not in item or "_id" not in item or "message" not in item:
                    continue

                username = item["username"]
                _id = item["_id"].get("$oid", str(item["_id"]))  # 提取ObjectId
                message = item["message"]

                # 检查是否需要过滤
                if username in filtered_users:
                    filter_count[username] += 1
                    continue

                # 处理message中的换行符（保持原有的换行）
                # 将消息中的制表符替换为空格，避免干扰输出格式
                message = message.replace("\t", "    ")

                # 保存结果，使用特殊分隔符区分_id和message
                results.append(f"{_id}\t{message}")

            # 写入结果文件
            with open(output_path, "w", encoding="utf-8") as f:
                # 写入标题
                f.write("_id\tmessage\n")
                # 写入数据
                f.write("\n".join(results))

            print(f"已处理: {filename} -> {os.path.basename(output_path)}")

        except Exception as e:
            print(f"处理 {filename} 时出错: {str(e)}")

    # 输出过滤统计信息
    print("\n过滤统计：")
    total_filtered = 0
    for user in filtered_users:
        count = filter_count[user]
        total_filtered += count
        print(f"{user}: {count}条")
    print(f"总计过滤: {total_filtered}条")


if __name__ == "__main__":
    # 输入和输出文件夹路径
    input_directory = "data"  # 存放JSON文件的文件夹
    output_directory = "test_data"  # 输出TXT文件的文件夹

    # 处理文件
    process_json_files(input_directory, output_directory)
    print(f"\n所有文件处理完成，结果保存在 {output_directory} 文件夹中")
