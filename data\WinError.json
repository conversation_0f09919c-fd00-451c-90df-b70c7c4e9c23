[{"_id": {"$oid": "686358669c3e8a30651e9d85"}, "username": "D9_super", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-01 11:40:36.016419", "message": "Experiment Crash\n--------------------------------------------------\nBUS2-Channel2-ImpaSetMultiParams-ImpaSetParams(12-16)-bus=BUS12-686358b11041b0f155ba1607\n--------------------------------------------------\n[WinError 10054] 远程主机强迫关闭了一个现有的连接。\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 329, in run\n    self._run_experiment()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\library\\impa_set_params.py\", line 66, in _run_experiment\n    self.mic_source.output_off(mic_channel)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\BYFS.py\", line 594, in output_off\n    protocol = super().output_off(channels)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\BYFS.py\", line 463, in output_off\n    self.set_freq(channel, 10e9)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\BYFS.py\", line 586, in set_freq\n    self._connection.send(protocol)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 51, in send\n    self._send_data(self._sock, send_data)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 41, in _send_data\n    sock.sendall(data)  # 发送数据\nConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-01T11:39:18.407Z"}}, {"_id": {"$oid": "686359479c3e8a30651e9d86"}, "username": "D9_super", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-01 11:44:21.439510", "message": "Experiment Crash\n--------------------------------------------------\nBUS2-Channel2-ImpaSetMultiParams-ImpaSetParams(2-16)-Bus2-686359937201f973cae125ae\n--------------------------------------------------\n[WinError 10054] 远程主机强迫关闭了一个现有的连接。\n--------------------------------------------------\n<PERSON>back (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 329, in run\n    self._run_experiment()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\library\\impa_set_params.py\", line 67, in _run_experiment\n    self.mic_source.output_off(mic_channel)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\BYFS.py\", line 594, in output_off\n    protocol = super().output_off(channels)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\BYFS.py\", line 463, in output_off\n    self.set_freq(channel, 10e9)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\BYFS.py\", line 586, in set_freq\n    self._connection.send(protocol)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 51, in send\n    self._send_data(self._sock, send_data)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 41, in _send_data\n    sock.sendall(data)  # 发送数据\nConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-01T11:43:03.824Z"}}, {"_id": {"$oid": "6863594ea47f89e2f61e9d85"}, "username": "D9_super", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-01 11:44:27.609713", "message": "Experiment Crash\n--------------------------------------------------\nBUS4-Channel4-ImpaSetMultiParams-ImpaSetParams(4-16)-BUS4-686359997201f973cae125b0\n--------------------------------------------------\n[WinError 10054] 远程主机强迫关闭了一个现有的连接。\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 329, in run\n    self._run_experiment()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\library\\impa_set_params.py\", line 67, in _run_experiment\n    self.mic_source.output_off(mic_channel)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\BYFS.py\", line 594, in output_off\n    protocol = super().output_off(channels)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\BYFS.py\", line 463, in output_off\n    self.set_freq(channel, 10e9)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\BYFS.py\", line 586, in set_freq\n    self._connection.send(protocol)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 51, in send\n    self._send_data(self._sock, send_data)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 41, in _send_data\n    sock.sendall(data)  # 发送数据\nConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-01T11:43:10Z"}}, {"_id": {"$oid": "68779cc9814a76608c8ce021"}, "username": "yxyY413", "sample": "Reset_LRU_test", "env_name": "Y8", "level": 10, "time": "2025-07-16 20:35:44.369781", "message": "Experiment Crash\n--------------------------------------------------\nq5q6c5-6-CouplerSpectrumZAmpDynamic-68779c9f65bea5c7de595f64\n--------------------------------------------------\n[WinError 123] 文件名、目录名或卷标语法不正确。: 'Z:\\\\Y8\\\\yxy\\\\Reset_LRU_test\\\\CouplerSpectrumZAmpDynamic\\\\q5q6c5-6\\x825-07-16\\x019.55.24\\\\CouplerSpectrum'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 198, in run\n    self._validate_options()\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 931, in _validate_options\n    paths = rocket_optimize(self.experiment_options.simulator_data_path)\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\tools\\utilities.py\", line 772, in rocket_optimize\n    for f in os.listdir(root):\nOSError: [WinError 123] 文件名、目录名或卷标语法不正确。: 'Z:\\\\Y8\\\\yxy\\\\Reset_LRU_test\\\\CouplerSpectrumZAmpDynamic\\\\q5q6c5-6\\x825-07-16\\x019.55.24\\\\CouplerSpectrum'\n", "version": "monster | 0.23.2 | B", "create_time": {"$date": "2025-07-16T20:36:25.381Z"}}, {"_id": {"$oid": "6888261ff4b30d9991446ec1"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-29 09:40:58.757223", "message": "Experiment Crash\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(2-31)-power=-39dBm-688826aac1c9e18c7070e85b\n--------------------------------------------------\n[WinError 123] 文件名、目录名或卷标语法不正确。: ':'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 605, in run_experiment\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 362, in _validate_options\n    super()._validate_options()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 942, in _validate_options\n    paths = rocket_optimize(self.experiment_options.simulator_data_path)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\tools\\utilities.py\", line 770, in rocket_optimize\n    for f in os.listdir(root):\nOSError: [WinError 123] 文件名、目录名或卷标语法不正确。: ':'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:38:39.928Z"}}, {"_id": {"$oid": "68882672f340c94b3b446ff1"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-29 09:42:21.523040", "message": "Experiment Crash\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(2-31)-power=-39dBm-688826fdf13d5118f9313222\n--------------------------------------------------\n[WinError 123] 文件名、目录名或卷标语法不正确。: ':'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 605, in run_experiment\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 362, in _validate_options\n    super()._validate_options()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 942, in _validate_options\n    paths = rocket_optimize(self.experiment_options.simulator_data_path)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\tools\\utilities.py\", line 770, in rocket_optimize\n    for f in os.listdir(root):\nOSError: [WinError 123] 文件名、目录名或卷标语法不正确。: ':'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:40:02.691Z"}}, {"_id": {"$oid": "6888272df340c94b3b447000"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-29 09:45:28.058822", "message": "Experiment Crash\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(2-31)-power=-39dBm-688827b87cc95b5f160570ee\n--------------------------------------------------\n[WinError 123] 文件名、目录名或卷标语法不正确。: ':'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 605, in run_experiment\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 362, in _validate_options\n    super()._validate_options()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 942, in _validate_options\n    paths = rocket_optimize(self.experiment_options.simulator_data_path)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\tools\\utilities.py\", line 770, in rocket_optimize\n    for f in os.listdir(root):\nOSError: [WinError 123] 文件名、目录名或卷标语法不正确。: ':'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:43:09.21Z"}}, {"_id": {"$oid": "68955f1c5dd635150013b5dd"}, "username": "zqq", "sample": "250620-设计验证-102bit-V4.0（Si+Nb120nm+Ta30nm）-Base-6#（Flip-5#-B1）", "env_name": "Y2", "level": 10, "time": "2025-08-08 10:21:17.884521", "message": "Experiment Crash\n--------------------------------------------------\nq19q20q21q22q23q24-BUS4-Channel4-ImpaGain-68955f1b973ee1f9ed36e62d\n--------------------------------------------------\n[WinError 10054] 远程主机强迫关闭了一个现有的连接。\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 329, in run\n    self._run_experiment()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\preliminary\\library\\impa_gain.py\", line 210, in _run_experiment\n    data = self._run_open_mode()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\preliminary\\library\\impa_gain.py\", line 152, in _run_open_mode\n    self.dc_source.output_off(int(dc_channel))\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\instrument\\qdc.py\", line 52, in output_off\n    recv = tcp_client.send(protocol, 1)\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 51, in send\n    self._send_data(self._sock, send_data)\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 41, in _send_data\n    sock.sendall(data)  # 发送数据\nConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-08-08T10:21:16.549Z"}}, {"_id": {"$oid": "68998a45f340c94b3b44960f"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-08-11 14:17:17.364254", "message": "Experiment Crash\n--------------------------------------------------\nbus-15-ImpaGain-68998aed77ff99b4c37a1b38\n--------------------------------------------------\n[WinError 10054] 远程主机强迫关闭了一个现有的连接。\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 300, in _initial\n    self.mic_source.output_off(mic_channel)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\BYFS.py\", line 595, in output_off\n    protocol = super().output_off(channels)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\BYFS.py\", line 464, in output_off\n    self.set_freq(channel, 10e9)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\BYFS.py\", line 587, in set_freq\n    self._connection.send(protocol)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 51, in send\n    self._send_data(self._sock, send_data)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 41, in _send_data\n    sock.sendall(data)  # 发送数据\nConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-08-11T14:14:29.525Z"}}]