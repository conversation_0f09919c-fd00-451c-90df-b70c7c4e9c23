[{"_id": {"$oid": "68635583a0b5e01b01b32a24"}, "username": "D9_super", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-01 11:28:16.851648", "message": "Experiment Crash\n--------------------------------------------------\nBUS1-Channel1-ImpaSetParams-686355cca120104c8c63f63e\n--------------------------------------------------\nconnect fail, ip:*************. port:6001\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 294, in _initial\n    self.select_mic_source(\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 121, in select_mic_source\n    self.mic_source = DomesticMic(mic_source, ip, device_id)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_inst.py\", line 315, in __init__\n    self._mic_obj = mic_obj(ip=ip, device_id=device_id)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_inst.py\", line 286, in __init__\n    super(Stfsh9004, self).__init__(ip, port, device_id)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\STFSH9004.py\", line 31, in __init__\n    self._connection = TcpClient(ip, port, once=once)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 17, in __init__\n    self.connect()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 29, in connect\n    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')\nTimeoutError: connect fail, ip:*************. port:6001\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-01T11:26:59.269Z"}}, {"_id": {"$oid": "68945b343ca81fdd3d90ae90"}, "username": "zqq", "sample": "250620-设计验证-102bit-V4.0（Si+Nb120nm+Ta30nm）-Base-6#（Flip-5#-B1）", "env_name": "Y2", "level": 10, "time": "2025-08-07 15:53:49.404737", "message": "Experiment Crash\n--------------------------------------------------\nbus-3-ImpaOptiParams-68945b89445196c99a9844df\n--------------------------------------------------\nconnect fail, ip:************. port:5001\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 294, in _initial\n    self.select_mic_source(\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 121, in select_mic_source\n    self.mic_source = DomesticMic(mic_source, ip, device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 315, in __init__\n    self._mic_obj = mic_obj(ip=ip, device_id=device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\BYFS.py\", line 474, in __init__\n    self._connection = TcpClient(ip, port, once=True)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 17, in __init__\n    self.connect()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 29, in connect\n    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')\nTimeoutError: connect fail, ip:************. port:5001\n", "version": "monster | ********* | C", "create_time": {"$date": "2025-08-07T15:52:20.429Z"}}, {"_id": {"$oid": "68945b383366dee48090afc1"}, "username": "zqq", "sample": "250620-设计验证-102bit-V4.0（Si+Nb120nm+Ta30nm）-Base-6#（Flip-5#-B1）", "env_name": "Y2", "level": 10, "time": "2025-08-07 15:53:53.628048", "message": "Experiment Crash\n--------------------------------------------------\nbus-3-ImpaOptiParams-68945b8d445196c99a9844e2\n--------------------------------------------------\nconnect fail, ip:************. port:5001\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 294, in _initial\n    self.select_mic_source(\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 121, in select_mic_source\n    self.mic_source = DomesticMic(mic_source, ip, device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 315, in __init__\n    self._mic_obj = mic_obj(ip=ip, device_id=device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\BYFS.py\", line 474, in __init__\n    self._connection = TcpClient(ip, port, once=True)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 17, in __init__\n    self.connect()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 29, in connect\n    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')\nTimeoutError: connect fail, ip:************. port:5001\n", "version": "monster | ********* | C", "create_time": {"$date": "2025-08-07T15:52:24.64Z"}}, {"_id": {"$oid": "689462775dd635150013b5d0"}, "username": "zqq", "sample": "250620-设计验证-102bit-V4.0（Si+Nb120nm+Ta30nm）-Base-6#（Flip-5#-B1）", "env_name": "Y2", "level": 10, "time": "2025-08-07 16:24:48.522917", "message": "Experiment Crash\n--------------------------------------------------\nbus-3-ImpaOptiParams-689462cc3d04340aef6a5c7e\n--------------------------------------------------\nconnect fail, ip:************. port:5001\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 294, in _initial\n    self.select_mic_source(\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 121, in select_mic_source\n    self.mic_source = DomesticMic(mic_source, ip, device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 315, in __init__\n    self._mic_obj = mic_obj(ip=ip, device_id=device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\BYFS.py\", line 474, in __init__\n    self._connection = TcpClient(ip, port, once=True)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 17, in __init__\n    self.connect()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 29, in connect\n    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')\nTimeoutError: connect fail, ip:************. port:5001\n", "version": "monster | ********* | C", "create_time": {"$date": "2025-08-07T16:23:19.54Z"}}, {"_id": {"$oid": "6894628126465fe58213b5da"}, "username": "zqq", "sample": "250620-设计验证-102bit-V4.0（Si+Nb120nm+Ta30nm）-Base-6#（Flip-5#-B1）", "env_name": "Y2", "level": 10, "time": "2025-08-07 16:24:58.245277", "message": "Experiment Crash\n--------------------------------------------------\nbus-4-ImpaCavityFluxScan-689462d63d04340aef6a5c81\n--------------------------------------------------\nconnect fail, ip:************. port:5001\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 294, in _initial\n    self.select_mic_source(\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 121, in select_mic_source\n    self.mic_source = DomesticMic(mic_source, ip, device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 315, in __init__\n    self._mic_obj = mic_obj(ip=ip, device_id=device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\BYFS.py\", line 474, in __init__\n    self._connection = TcpClient(ip, port, once=True)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 17, in __init__\n    self.connect()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 29, in connect\n    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')\nTimeoutError: connect fail, ip:************. port:5001\n", "version": "monster | ********* | C", "create_time": {"$date": "2025-08-07T16:23:29.271Z"}}, {"_id": {"$oid": "6894693c26465fe58213b5dc"}, "username": "zqq", "sample": "250620-设计验证-102bit-V4.0（Si+Nb120nm+Ta30nm）-Base-6#（Flip-5#-B1）", "env_name": "Y2", "level": 10, "time": "2025-08-07 16:53:41.697787", "message": "Experiment Crash\n--------------------------------------------------\nbus-11-ImpaOptiParams-689469913d04340aef6a5cc4\n--------------------------------------------------\nconnect fail, ip:************. port:5001\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 294, in _initial\n    self.select_mic_source(\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 121, in select_mic_source\n    self.mic_source = DomesticMic(mic_source, ip, device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 315, in __init__\n    self._mic_obj = mic_obj(ip=ip, device_id=device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\BYFS.py\", line 474, in __init__\n    self._connection = TcpClient(ip, port, once=True)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 17, in __init__\n    self.connect()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 29, in connect\n    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')\nTimeoutError: connect fail, ip:************. port:5001\n", "version": "monster | ********* | C", "create_time": {"$date": "2025-08-07T16:52:12.728Z"}}, {"_id": {"$oid": "689469405dd635150013b5d2"}, "username": "zqq", "sample": "250620-设计验证-102bit-V4.0（Si+Nb120nm+Ta30nm）-Base-6#（Flip-5#-B1）", "env_name": "Y2", "level": 10, "time": "2025-08-07 16:53:45.910559", "message": "Experiment Crash\n--------------------------------------------------\nbus-11-ImpaOptiParams-689469953d04340aef6a5cc7\n--------------------------------------------------\nconnect fail, ip:************. port:5001\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 294, in _initial\n    self.select_mic_source(\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 121, in select_mic_source\n    self.mic_source = DomesticMic(mic_source, ip, device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 315, in __init__\n    self._mic_obj = mic_obj(ip=ip, device_id=device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\BYFS.py\", line 474, in __init__\n    self._connection = TcpClient(ip, port, once=True)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 17, in __init__\n    self.connect()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 29, in connect\n    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')\nTimeoutError: connect fail, ip:************. port:5001\n", "version": "monster | ********* | C", "create_time": {"$date": "2025-08-07T16:52:16.939Z"}}, {"_id": {"$oid": "6894694526465fe58213b5dd"}, "username": "zqq", "sample": "250620-设计验证-102bit-V4.0（Si+Nb120nm+Ta30nm）-Base-6#（Flip-5#-B1）", "env_name": "Y2", "level": 10, "time": "2025-08-07 16:53:50.122213", "message": "Experiment Crash\n--------------------------------------------------\nbus-11-ImpaOptiParams-6894699a3d04340aef6a5cca\n--------------------------------------------------\nconnect fail, ip:************. port:5001\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 294, in _initial\n    self.select_mic_source(\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 121, in select_mic_source\n    self.mic_source = DomesticMic(mic_source, ip, device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 315, in __init__\n    self._mic_obj = mic_obj(ip=ip, device_id=device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\BYFS.py\", line 474, in __init__\n    self._connection = TcpClient(ip, port, once=True)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 17, in __init__\n    self.connect()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 29, in connect\n    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')\nTimeoutError: connect fail, ip:************. port:5001\n", "version": "monster | ********* | C", "create_time": {"$date": "2025-08-07T16:52:21.155Z"}}, {"_id": {"$oid": "689469495dd635150013b5d3"}, "username": "zqq", "sample": "250620-设计验证-102bit-V4.0（Si+Nb120nm+Ta30nm）-Base-6#（Flip-5#-B1）", "env_name": "Y2", "level": 10, "time": "2025-08-07 16:53:54.321688", "message": "Experiment Crash\n--------------------------------------------------\nbus-11-ImpaOptiParams-6894699e3d04340aef6a5ccd\n--------------------------------------------------\nconnect fail, ip:************. port:5001\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 294, in _initial\n    self.select_mic_source(\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 121, in select_mic_source\n    self.mic_source = DomesticMic(mic_source, ip, device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 315, in __init__\n    self._mic_obj = mic_obj(ip=ip, device_id=device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\BYFS.py\", line 474, in __init__\n    self._connection = TcpClient(ip, port, once=True)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 17, in __init__\n    self.connect()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 29, in connect\n    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')\nTimeoutError: connect fail, ip:************. port:5001\n", "version": "monster | ********* | C", "create_time": {"$date": "2025-08-07T16:52:25.345Z"}}, {"_id": {"$oid": "6894695326465fe58213b5de"}, "username": "zqq", "sample": "250620-设计验证-102bit-V4.0（Si+Nb120nm+Ta30nm）-Base-6#（Flip-5#-B1）", "env_name": "Y2", "level": 10, "time": "2025-08-07 16:54:04.046420", "message": "Experiment Crash\n--------------------------------------------------\nbus-12-ImpaCavityFluxScan-689469a83d04340aef6a5cd0\n--------------------------------------------------\nconnect fail, ip:************. port:5001\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 294, in _initial\n    self.select_mic_source(\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 121, in select_mic_source\n    self.mic_source = DomesticMic(mic_source, ip, device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 315, in __init__\n    self._mic_obj = mic_obj(ip=ip, device_id=device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\BYFS.py\", line 474, in __init__\n    self._connection = TcpClient(ip, port, once=True)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 17, in __init__\n    self.connect()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 29, in connect\n    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')\nTimeoutError: connect fail, ip:************. port:5001\n", "version": "monster | ********* | C", "create_time": {"$date": "2025-08-07T16:52:35.072Z"}}, {"_id": {"$oid": "68947e6226465fe58213b5df"}, "username": "zqq", "sample": "250620-设计验证-102bit-V4.0（Si+Nb120nm+Ta30nm）-Base-6#（Flip-5#-B1）", "env_name": "Y2", "level": 10, "time": "2025-08-07 18:23:55.753635", "message": "Experiment Crash\n--------------------------------------------------\nbus-8-ImpaOptiParams-68947eb7cdb9adbbae8f4351\n--------------------------------------------------\nconnect fail, ip:************. port:5001\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 294, in _initial\n    self.select_mic_source(\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 121, in select_mic_source\n    self.mic_source = DomesticMic(mic_source, ip, device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 315, in __init__\n    self._mic_obj = mic_obj(ip=ip, device_id=device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\BYFS.py\", line 474, in __init__\n    self._connection = TcpClient(ip, port, once=True)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 17, in __init__\n    self.connect()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 29, in connect\n    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')\nTimeoutError: connect fail, ip:************. port:5001\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-08-07T18:22:26.799Z"}}, {"_id": {"$oid": "68947e6726465fe58213b5e0"}, "username": "zqq", "sample": "250620-设计验证-102bit-V4.0（Si+Nb120nm+Ta30nm）-Base-6#（Flip-5#-B1）", "env_name": "Y2", "level": 10, "time": "2025-08-07 18:23:59.990981", "message": "Experiment Crash\n--------------------------------------------------\nbus-8-ImpaOptiParams-68947ebbcdb9adbbae8f4354\n--------------------------------------------------\nconnect fail, ip:************. port:5001\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 294, in _initial\n    self.select_mic_source(\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 121, in select_mic_source\n    self.mic_source = DomesticMic(mic_source, ip, device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 315, in __init__\n    self._mic_obj = mic_obj(ip=ip, device_id=device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\BYFS.py\", line 474, in __init__\n    self._connection = TcpClient(ip, port, once=True)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 17, in __init__\n    self.connect()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 29, in connect\n    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')\nTimeoutError: connect fail, ip:************. port:5001\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-08-07T18:22:31.04Z"}}, {"_id": {"$oid": "68947e6b26465fe58213b5e1"}, "username": "zqq", "sample": "250620-设计验证-102bit-V4.0（Si+Nb120nm+Ta30nm）-Base-6#（Flip-5#-B1）", "env_name": "Y2", "level": 10, "time": "2025-08-07 18:24:04.235548", "message": "Experiment Crash\n--------------------------------------------------\nbus-8-ImpaOptiParams-68947ec0cdb9adbbae8f4357\n--------------------------------------------------\nconnect fail, ip:************. port:5001\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 294, in _initial\n    self.select_mic_source(\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 121, in select_mic_source\n    self.mic_source = DomesticMic(mic_source, ip, device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 315, in __init__\n    self._mic_obj = mic_obj(ip=ip, device_id=device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\BYFS.py\", line 474, in __init__\n    self._connection = TcpClient(ip, port, once=True)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 17, in __init__\n    self.connect()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 29, in connect\n    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')\nTimeoutError: connect fail, ip:************. port:5001\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-08-07T18:22:35.296Z"}}, {"_id": {"$oid": "68947e6f26465fe58213b5e2"}, "username": "zqq", "sample": "250620-设计验证-102bit-V4.0（Si+Nb120nm+Ta30nm）-Base-6#（Flip-5#-B1）", "env_name": "Y2", "level": 10, "time": "2025-08-07 18:24:08.497242", "message": "Experiment Crash\n--------------------------------------------------\nbus-8-ImpaOptiParams-68947ec4cdb9adbbae8f435a\n--------------------------------------------------\nconnect fail, ip:************. port:5001\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 294, in _initial\n    self.select_mic_source(\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 121, in select_mic_source\n    self.mic_source = DomesticMic(mic_source, ip, device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 315, in __init__\n    self._mic_obj = mic_obj(ip=ip, device_id=device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\BYFS.py\", line 474, in __init__\n    self._connection = TcpClient(ip, port, once=True)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 17, in __init__\n    self.connect()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 29, in connect\n    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')\nTimeoutError: connect fail, ip:************. port:5001\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-08-07T18:22:39.544Z"}}, {"_id": {"$oid": "68948ec826465fe58213b5e3"}, "username": "zqq", "sample": "250620-设计验证-102bit-V4.0（Si+Nb120nm+Ta30nm）-Base-6#（Flip-5#-B1）", "env_name": "Y2", "level": 10, "time": "2025-08-07 19:33:53.483257", "message": "Experiment Crash\n--------------------------------------------------\nbus-5-ImpaOptiParams-68948f1dcdb9adbbae8f43ef\n--------------------------------------------------\nconnect fail, ip:************. port:5001\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 294, in _initial\n    self.select_mic_source(\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 121, in select_mic_source\n    self.mic_source = DomesticMic(mic_source, ip, device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 315, in __init__\n    self._mic_obj = mic_obj(ip=ip, device_id=device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\BYFS.py\", line 474, in __init__\n    self._connection = TcpClient(ip, port, once=True)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 17, in __init__\n    self.connect()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 29, in connect\n    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')\nTimeoutError: connect fail, ip:************. port:5001\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-08-07T19:32:24.542Z"}}, {"_id": {"$oid": "68948ed426465fe58213b5e4"}, "username": "zqq", "sample": "250620-设计验证-102bit-V4.0（Si+Nb120nm+Ta30nm）-Base-6#（Flip-5#-B1）", "env_name": "Y2", "level": 10, "time": "2025-08-07 19:34:05.493497", "message": "Experiment Crash\n--------------------------------------------------\nbus-7-ImpaCavityFluxScan-68948f29cdb9adbbae8f43f5\n--------------------------------------------------\nconnect fail, ip:************. port:5001\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 294, in _initial\n    self.select_mic_source(\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 121, in select_mic_source\n    self.mic_source = DomesticMic(mic_source, ip, device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 315, in __init__\n    self._mic_obj = mic_obj(ip=ip, device_id=device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\BYFS.py\", line 474, in __init__\n    self._connection = TcpClient(ip, port, once=True)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 17, in __init__\n    self.connect()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 29, in connect\n    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')\nTimeoutError: connect fail, ip:************. port:5001\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-08-07T19:32:36.552Z"}}, {"_id": {"$oid": "68948ed85dd635150013b5d9"}, "username": "zqq", "sample": "250620-设计验证-102bit-V4.0（Si+Nb120nm+Ta30nm）-Base-6#（Flip-5#-B1）", "env_name": "Y2", "level": 10, "time": "2025-08-07 19:34:09.720535", "message": "Experiment Crash\n--------------------------------------------------\nbus-7-ImpaOptiParams-68948f2dcdb9adbbae8f43f8\n--------------------------------------------------\nconnect fail, ip:************. port:5001\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 294, in _initial\n    self.select_mic_source(\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 121, in select_mic_source\n    self.mic_source = DomesticMic(mic_source, ip, device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 315, in __init__\n    self._mic_obj = mic_obj(ip=ip, device_id=device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\BYFS.py\", line 474, in __init__\n    self._connection = TcpClient(ip, port, once=True)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 17, in __init__\n    self.connect()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 29, in connect\n    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')\nTimeoutError: connect fail, ip:************. port:5001\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-08-07T19:32:40.776Z"}}, {"_id": {"$oid": "68948edc26465fe58213b5e5"}, "username": "zqq", "sample": "250620-设计验证-102bit-V4.0（Si+Nb120nm+Ta30nm）-Base-6#（Flip-5#-B1）", "env_name": "Y2", "level": 10, "time": "2025-08-07 19:34:13.917992", "message": "Experiment Crash\n--------------------------------------------------\nbus-7-ImpaOptiParams-68948f31cdb9adbbae8f43fb\n--------------------------------------------------\nconnect fail, ip:************. port:5001\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 294, in _initial\n    self.select_mic_source(\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 121, in select_mic_source\n    self.mic_source = DomesticMic(mic_source, ip, device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 315, in __init__\n    self._mic_obj = mic_obj(ip=ip, device_id=device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\BYFS.py\", line 474, in __init__\n    self._connection = TcpClient(ip, port, once=True)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 17, in __init__\n    self.connect()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 29, in connect\n    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')\nTimeoutError: connect fail, ip:************. port:5001\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-08-07T19:32:44.981Z"}}, {"_id": {"$oid": "68948ee15dd635150013b5da"}, "username": "zqq", "sample": "250620-设计验证-102bit-V4.0（Si+Nb120nm+Ta30nm）-Base-6#（Flip-5#-B1）", "env_name": "Y2", "level": 10, "time": "2025-08-07 19:34:18.107836", "message": "Experiment Crash\n--------------------------------------------------\nbus-7-ImpaOptiParams-68948f36cdb9adbbae8f43fe\n--------------------------------------------------\nconnect fail, ip:************. port:5001\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 294, in _initial\n    self.select_mic_source(\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 121, in select_mic_source\n    self.mic_source = DomesticMic(mic_source, ip, device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 315, in __init__\n    self._mic_obj = mic_obj(ip=ip, device_id=device_id)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\BYFS.py\", line 474, in __init__\n    self._connection = TcpClient(ip, port, once=True)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 17, in __init__\n    self.connect()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 29, in connect\n    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')\nTimeoutError: connect fail, ip:************. port:5001\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-08-07T19:32:49.166Z"}}, {"_id": {"$oid": "68998a4ff340c94b3b449610"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-08-11 14:17:27.016881", "message": "Experiment Crash\n--------------------------------------------------\nbus-15-ImpaGain-68998af277ff99b4c37a1b3b\n--------------------------------------------------\nconnect fail, ip:*************. port:5001\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 294, in _initial\n    self.select_mic_source(\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 121, in select_mic_source\n    self.mic_source = DomesticMic(mic_source, ip, device_id)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 315, in __init__\n    self._mic_obj = mic_obj(ip=ip, device_id=device_id)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\BYFS.py\", line 474, in __init__\n    self._connection = TcpClient(ip, port, once=True)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 17, in __init__\n    self.connect()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 29, in connect\n    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')\nTimeoutError: connect fail, ip:*************. port:5001\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-08-11T14:14:39.181Z"}}, {"_id": {"$oid": "68998a58f4b30d99914495be"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-08-11 14:17:36.683236", "message": "Experiment Crash\n--------------------------------------------------\nbus-15-ImpaGain-68998afc77ff99b4c37a1b3e\n--------------------------------------------------\nconnect fail, ip:*************. port:5001\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 294, in _initial\n    self.select_mic_source(\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 121, in select_mic_source\n    self.mic_source = DomesticMic(mic_source, ip, device_id)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 315, in __init__\n    self._mic_obj = mic_obj(ip=ip, device_id=device_id)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\BYFS.py\", line 474, in __init__\n    self._connection = TcpClient(ip, port, once=True)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 17, in __init__\n    self.connect()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 29, in connect\n    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')\nTimeoutError: connect fail, ip:*************. port:5001\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-08-11T14:14:48.85Z"}}, {"_id": {"$oid": "68998a62f4b30d99914495c0"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-08-11 14:17:46.321169", "message": "Experiment Crash\n--------------------------------------------------\nbus-16-ImpaGain-68998b0677ff99b4c37a1b41\n--------------------------------------------------\nconnect fail, ip:*************. port:5001\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 294, in _initial\n    self.select_mic_source(\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 121, in select_mic_source\n    self.mic_source = DomesticMic(mic_source, ip, device_id)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 315, in __init__\n    self._mic_obj = mic_obj(ip=ip, device_id=device_id)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\BYFS.py\", line 474, in __init__\n    self._connection = TcpClient(ip, port, once=True)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 17, in __init__\n    self.connect()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 29, in connect\n    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')\nTimeoutError: connect fail, ip:*************. port:5001\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-08-11T14:14:58.481Z"}}, {"_id": {"$oid": "68998a6cf340c94b3b449611"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-08-11 14:17:55.950982", "message": "Experiment Crash\n--------------------------------------------------\nbus-16-ImpaGain-68998b0f77ff99b4c37a1b44\n--------------------------------------------------\nconnect fail, ip:*************. port:5001\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 294, in _initial\n    self.select_mic_source(\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 121, in select_mic_source\n    self.mic_source = DomesticMic(mic_source, ip, device_id)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 315, in __init__\n    self._mic_obj = mic_obj(ip=ip, device_id=device_id)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\BYFS.py\", line 474, in __init__\n    self._connection = TcpClient(ip, port, once=True)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 17, in __init__\n    self.connect()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 29, in connect\n    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')\nTimeoutError: connect fail, ip:*************. port:5001\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-08-11T14:15:08.114Z"}}, {"_id": {"$oid": "68998a75f4b30d99914495c1"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-08-11 14:18:05.596401", "message": "Experiment Crash\n--------------------------------------------------\nbus-16-ImpaGain-68998b1977ff99b4c37a1b47\n--------------------------------------------------\nconnect fail, ip:*************. port:5001\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 294, in _initial\n    self.select_mic_source(\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 121, in select_mic_source\n    self.mic_source = DomesticMic(mic_source, ip, device_id)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 315, in __init__\n    self._mic_obj = mic_obj(ip=ip, device_id=device_id)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\BYFS.py\", line 474, in __init__\n    self._connection = TcpClient(ip, port, once=True)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 17, in __init__\n    self.connect()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 29, in connect\n    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')\nTimeoutError: connect fail, ip:*************. port:5001\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-08-11T14:15:17.764Z"}}, {"_id": {"$oid": "68998ba6f6e8e052d6d4262b"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-08-11 14:23:10.745729", "message": "Experiment Crash\n--------------------------------------------------\nq1q2q3q4q5q6-BUS15-Channel7-ImpaSetParams-68998c4aa0467b7306dc43d2\n--------------------------------------------------\nconnect fail, ip:*************. port:5001\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 294, in _initial\n    self.select_mic_source(\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 121, in select_mic_source\n    self.mic_source = DomesticMic(mic_source, ip, device_id)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_inst.py\", line 315, in __init__\n    self._mic_obj = mic_obj(ip=ip, device_id=device_id)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\BYFS.py\", line 473, in __init__\n    self._id = device_id\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 17, in __init__\n    self.connect()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 29, in connect\n    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')\nTimeoutError: connect fail, ip:*************. port:5001\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-08-11T14:20:22.897Z"}}, {"_id": {"$oid": "68998bcab242e0e00cd4217b"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-08-11 14:23:46.481259", "message": "Experiment Crash\n--------------------------------------------------\nq1q2q3q4q5q6-BUS15-Channel7-ImpaSetParams-68998c6ea0467b7306dc43d5\n--------------------------------------------------\nconnect fail, ip:*************. port:5001\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 294, in _initial\n    self.select_mic_source(\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 121, in select_mic_source\n    self.mic_source = DomesticMic(mic_source, ip, device_id)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_inst.py\", line 315, in __init__\n    self._mic_obj = mic_obj(ip=ip, device_id=device_id)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\BYFS.py\", line 473, in __init__\n    self._id = device_id\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 17, in __init__\n    self.connect()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 29, in connect\n    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')\nTimeoutError: connect fail, ip:*************. port:5001\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-08-11T14:20:58.637Z"}}, {"_id": {"$oid": "68998c97b242e0e00cd4217e"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-08-11 14:27:11.360606", "message": "Experiment Crash\n--------------------------------------------------\nq1q2q3q4q5q6-BUS15-Channel7-ImpaSetParams-68998d3ba0467b7306dc43d8\n--------------------------------------------------\nconnect fail, ip:*************. port:5001\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 294, in _initial\n    self.select_mic_source(\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 121, in select_mic_source\n    self.mic_source = DomesticMic(mic_source, ip, device_id)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_inst.py\", line 315, in __init__\n    self._mic_obj = mic_obj(ip=ip, device_id=device_id)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\BYFS.py\", line 473, in __init__\n    self._id = device_id\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 17, in __init__\n    self.connect()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 29, in connect\n    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')\nTimeoutError: connect fail, ip:*************. port:5001\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-08-11T14:24:23.51Z"}}, {"_id": {"$oid": "68998d32b242e0e00cd42185"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-08-11 14:29:46.122043", "message": "Experiment Crash\n--------------------------------------------------\nq1q2q3q4q5q6-BUS15-Channel7-ImpaSetParams-68998dd6a0467b7306dc43db\n--------------------------------------------------\nconnect fail, ip:*************. port:5001\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 294, in _initial\n    self.select_mic_source(\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 121, in select_mic_source\n    self.mic_source = DomesticMic(mic_source, ip, device_id)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_inst.py\", line 315, in __init__\n    self._mic_obj = mic_obj(ip=ip, device_id=device_id)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\BYFS.py\", line 473, in __init__\n    self._id = device_id\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 17, in __init__\n    self.connect()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 29, in connect\n    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')\nTimeoutError: connect fail, ip:*************. port:5001\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-08-11T14:26:58.268Z"}}, {"_id": {"$oid": "68998d51f6e8e052d6d42632"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-08-11 14:30:17.128663", "message": "Experiment Crash\n--------------------------------------------------\nq1q2q3q4q5q6-BUS15-Channel7-ImpaSetParams-68998df5a0467b7306dc43de\n--------------------------------------------------\nconnect fail, ip:*************. port:5001\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 294, in _initial\n    self.select_mic_source(\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 121, in select_mic_source\n    self.mic_source = DomesticMic(mic_source, ip, device_id)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_inst.py\", line 315, in __init__\n    self._mic_obj = mic_obj(ip=ip, device_id=device_id)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\BYFS.py\", line 473, in __init__\n    self._id = device_id\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 17, in __init__\n    self.connect()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 29, in connect\n    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')\nTimeoutError: connect fail, ip:*************. port:5001\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-08-11T14:27:29.275Z"}}, {"_id": {"$oid": "68998da7f6e8e052d6d42633"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-08-11 14:31:43.674637", "message": "Experiment Crash\n--------------------------------------------------\nq1q2q3q4q5q6-BUS15-Channel7-ImpaSetParams-68998e4ba0467b7306dc43e1\n--------------------------------------------------\nconnect fail, ip:*************. port:5001\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 294, in _initial\n    self.select_mic_source(\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 121, in select_mic_source\n    self.mic_source = DomesticMic(mic_source, ip, device_id)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_inst.py\", line 315, in __init__\n    self._mic_obj = mic_obj(ip=ip, device_id=device_id)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\BYFS.py\", line 473, in __init__\n    self._id = device_id\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 17, in __init__\n    self.connect()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 29, in connect\n    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')\nTimeoutError: connect fail, ip:*************. port:5001\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-08-11T14:28:55.817Z"}}, {"_id": {"$oid": "68998de4f6e8e052d6d42634"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-08-11 14:32:43.952326", "message": "Experiment Crash\n--------------------------------------------------\nq1q2q3q4q5q6-BUS15-Channel7-ImpaSetParams-68998e872d6a0302ccaad336\n--------------------------------------------------\nconnect fail, ip:*************. port:5001\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 294, in _initial\n    self.select_mic_source(\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 121, in select_mic_source\n    self.mic_source = DomesticMic(mic_source, ip, device_id)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_inst.py\", line 315, in __init__\n    self._mic_obj = mic_obj(ip=ip, device_id=device_id)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\BYFS.py\", line 474, in __init__\n    self._connection = TcpClient(ip, port, once=True)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 17, in __init__\n    self.connect()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 29, in connect\n    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')\nTimeoutError: connect fail, ip:*************. port:5001\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-08-11T14:29:56.095Z"}}, {"_id": {"$oid": "68998e51b242e0e00cd42186"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-08-11 14:34:33.622710", "message": "Experiment Crash\n--------------------------------------------------\nbus-15-ImpaGain-68998ef52d6a0302ccaad339\n--------------------------------------------------\nconnect fail, ip:*************. port:5001\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 294, in _initial\n    self.select_mic_source(\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 121, in select_mic_source\n    self.mic_source = DomesticMic(mic_source, ip, device_id)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_inst.py\", line 315, in __init__\n    self._mic_obj = mic_obj(ip=ip, device_id=device_id)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\BYFS.py\", line 474, in __init__\n    self._connection = TcpClient(ip, port, once=True)\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 17, in __init__\n    self.connect()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-visage\\pyQCat\\instrument\\socket_service\\tcp_client.py\", line 29, in connect\n    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')\nTimeoutError: connect fail, ip:*************. port:5001\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-08-11T14:31:45.761Z"}}]