[{"_id": {"$oid": "6888936f11ad73c4231f636e"}, "username": "zqq", "sample": "250620-设计验证-102bit-V4.0（Si+Nb120nm+Ta30nm）-Base-6#（Flip-5#-B1）", "env_name": "Y2", "level": 10, "time": "2025-07-29 17:26:15.364631", "message": "Experiment Crash\n--------------------------------------------------\nBUS1-Channel1-BusS21Collector-688893b76735a1218163e6ba\n--------------------------------------------------\nVI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 457, in after_parsing\n    self.interface = Vxi11CoreClient(host_address, port, self.open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 361, in __init__\n    rpc.TCPClient.__init__(self, host, prog, vers, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 823, in __init__\n    pmap = TCPPortMapperClient(host, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 803, in __init__\n    RawTCPClient.__init__(self, host, PMAP_PROG, PMAP_VERS, PMAP_PORT, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 454, in __init__\n    self.connect(1e-3 * open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 488, in connect\n    raise RPCError(\"can't connect to server\")\npyvisa_py.protocols.rpc.RPCError: can't connect to server\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 271, in _initial\n    self.select_net_analyzer(network_analyzer_type)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 127, in select_net_analyzer\n    self.net_analyzer = NetworkAnalyzer(net_analyzer, self.config_path)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 37, in __init__\n    super(NetworkAnalyzer, self).__init__(instrument_model, config_path)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 29, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 75, in _get\n    return self._INST_LST.get(self._type)(self._visa_address)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\structures.py\", line 154, in __call__\n    obj = super().__call__(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\E5071C.py\", line 12, in __init__\n    super().__init__(visa_address)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\visa_inst.py\", line 35, in __init__\n    self.inst_obj = rm.open_resource(self.visa_address)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\highlevel.py\", line 3284, in open_resource\n    res.open(access_mode, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\resources\\resource.py\", line 282, in open\n    self.session, status = self._resource_manager.open_bare_resource(\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\highlevel.py\", line 3209, in open_bare_resource\n    return self.visalib.open(self.session, resource_name, access_mode, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\highlevel.py\", line 168, in open\n    sess = cls(session, resource_name, parsed, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 86, in __new__\n    return newcls(resource_manager_session, resource_name, parsed, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\sessions.py\", line 322, in __init__\n    self.after_parsing()\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 459, in after_parsing\n    raise errors.VisaIOError(constants.VI_ERROR_RSRC_NFOUND)\npyvisa.errors.VisaIOError: VI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-07-29T17:25:03.42Z"}}, {"_id": {"$oid": "6888963ff43e794f372aa0da"}, "username": "zqq", "sample": "250620-设计验证-102bit-V4.0（Si+Nb120nm+Ta30nm）-Base-6#（Flip-5#-B1）", "env_name": "Y2", "level": 10, "time": "2025-07-29 17:38:14.986396", "message": "Experiment Crash\n--------------------------------------------------\nBUS1-Channel1-BusS21Collector-6888968640be4e3426e55172\n--------------------------------------------------\nVI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 457, in after_parsing\n    self.interface = Vxi11CoreClient(host_address, port, self.open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 361, in __init__\n    rpc.TCPClient.__init__(self, host, prog, vers, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 823, in __init__\n    pmap = TCPPortMapperClient(host, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 803, in __init__\n    RawTCPClient.__init__(self, host, PMAP_PROG, PMAP_VERS, PMAP_PORT, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 454, in __init__\n    self.connect(1e-3 * open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 488, in connect\n    raise RPCError(\"can't connect to server\")\npyvisa_py.protocols.rpc.RPCError: can't connect to server\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 271, in _initial\n    self.select_net_analyzer(network_analyzer_type)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 127, in select_net_analyzer\n    self.net_analyzer = NetworkAnalyzer(net_analyzer, self.config_path)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 37, in __init__\n    super(NetworkAnalyzer, self).__init__(instrument_model, config_path)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 29, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 75, in _get\n    return self._INST_LST.get(self._type)(self._visa_address)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\structures.py\", line 154, in __call__\n    obj = super().__call__(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\E5071C.py\", line 12, in __init__\n    super().__init__(visa_address)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\visa_inst.py\", line 35, in __init__\n    self.inst_obj = rm.open_resource(self.visa_address)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\highlevel.py\", line 3284, in open_resource\n    res.open(access_mode, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\resources\\resource.py\", line 282, in open\n    self.session, status = self._resource_manager.open_bare_resource(\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\highlevel.py\", line 3209, in open_bare_resource\n    return self.visalib.open(self.session, resource_name, access_mode, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\highlevel.py\", line 168, in open\n    sess = cls(session, resource_name, parsed, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 86, in __new__\n    return newcls(resource_manager_session, resource_name, parsed, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\sessions.py\", line 322, in __init__\n    self.after_parsing()\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 459, in after_parsing\n    raise errors.VisaIOError(constants.VI_ERROR_RSRC_NFOUND)\npyvisa.errors.VisaIOError: VI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-07-29T17:37:03.039Z"}}, {"_id": {"$oid": "68889675f6689fe5521f636d"}, "username": "zqq", "sample": "250620-设计验证-102bit-V4.0（Si+Nb120nm+Ta30nm）-Base-6#（Flip-5#-B1）", "env_name": "Y2", "level": 10, "time": "2025-07-29 17:39:09.511256", "message": "Experiment Crash\n--------------------------------------------------\nBUS1-Channel1-BusS21Collector-688896bd6bb8ebc1fcbf886e\n--------------------------------------------------\nVI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 457, in after_parsing\n    self.interface = Vxi11CoreClient(host_address, port, self.open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 361, in __init__\n    rpc.TCPClient.__init__(self, host, prog, vers, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 823, in __init__\n    pmap = TCPPortMapperClient(host, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 803, in __init__\n    RawTCPClient.__init__(self, host, PMAP_PROG, PMAP_VERS, PMAP_PORT, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 454, in __init__\n    self.connect(1e-3 * open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 488, in connect\n    raise RPCError(\"can't connect to server\")\npyvisa_py.protocols.rpc.RPCError: can't connect to server\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 271, in _initial\n    self.select_net_analyzer(network_analyzer_type)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 127, in select_net_analyzer\n    self.net_analyzer = NetworkAnalyzer(net_analyzer, self.config_path)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 37, in __init__\n    super(NetworkAnalyzer, self).__init__(instrument_model, config_path)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 29, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 75, in _get\n    return self._INST_LST.get(self._type)(self._visa_address)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\structures.py\", line 154, in __call__\n    obj = super().__call__(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\E5071C.py\", line 12, in __init__\n    super().__init__(visa_address)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\visa_inst.py\", line 35, in __init__\n    self.inst_obj = rm.open_resource(self.visa_address)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\highlevel.py\", line 3284, in open_resource\n    res.open(access_mode, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\resources\\resource.py\", line 282, in open\n    self.session, status = self._resource_manager.open_bare_resource(\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\highlevel.py\", line 3209, in open_bare_resource\n    return self.visalib.open(self.session, resource_name, access_mode, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\highlevel.py\", line 168, in open\n    sess = cls(session, resource_name, parsed, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 86, in __new__\n    return newcls(resource_manager_session, resource_name, parsed, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\sessions.py\", line 322, in __init__\n    self.after_parsing()\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 459, in after_parsing\n    raise errors.VisaIOError(constants.VI_ERROR_RSRC_NFOUND)\npyvisa.errors.VisaIOError: VI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-07-29T17:37:57.563Z"}}, {"_id": {"$oid": "6888971c32aee6d0092aa0da"}, "username": "zqq", "sample": "250620-设计验证-102bit-V4.0（Si+Nb120nm+Ta30nm）-Base-6#（Flip-5#-B1）", "env_name": "Y2", "level": 10, "time": "2025-07-29 17:41:56.591186", "message": "Experiment Crash\n--------------------------------------------------\nBUS-1-FindBusCavityFreq-Rough-688897647f92df5525d835c7\n--------------------------------------------------\nVI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 457, in after_parsing\n    self.interface = Vxi11CoreClient(host_address, port, self.open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 361, in __init__\n    rpc.TCPClient.__init__(self, host, prog, vers, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 823, in __init__\n    pmap = TCPPortMapperClient(host, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 803, in __init__\n    RawTCPClient.__init__(self, host, PMAP_PROG, PMAP_VERS, PMAP_PORT, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 454, in __init__\n    self.connect(1e-3 * open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 488, in connect\n    raise RPCError(\"can't connect to server\")\npyvisa_py.protocols.rpc.RPCError: can't connect to server\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\find_cavity_freq.py\", line 124, in _initial\n    super()._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 271, in _initial\n    self.select_net_analyzer(network_analyzer_type)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 127, in select_net_analyzer\n    self.net_analyzer = NetworkAnalyzer(net_analyzer, self.config_path)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 37, in __init__\n    super(NetworkAnalyzer, self).__init__(instrument_model, config_path)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 29, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 75, in _get\n    return self._INST_LST.get(self._type)(self._visa_address)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\structures.py\", line 154, in __call__\n    obj = super().__call__(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\E5071C.py\", line 12, in __init__\n    super().__init__(visa_address)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\visa_inst.py\", line 35, in __init__\n    self.inst_obj = rm.open_resource(self.visa_address)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\highlevel.py\", line 3284, in open_resource\n    res.open(access_mode, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\resources\\resource.py\", line 282, in open\n    self.session, status = self._resource_manager.open_bare_resource(\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\highlevel.py\", line 3209, in open_bare_resource\n    return self.visalib.open(self.session, resource_name, access_mode, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\highlevel.py\", line 168, in open\n    sess = cls(session, resource_name, parsed, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 86, in __new__\n    return newcls(resource_manager_session, resource_name, parsed, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\sessions.py\", line 322, in __init__\n    self.after_parsing()\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 459, in after_parsing\n    raise errors.VisaIOError(constants.VI_ERROR_RSRC_NFOUND)\npyvisa.errors.VisaIOError: VI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-07-29T17:40:44.637Z"}}, {"_id": {"$oid": "6888972232aee6d0092aa0db"}, "username": "zqq", "sample": "250620-设计验证-102bit-V4.0（Si+Nb120nm+Ta30nm）-Base-6#（Flip-5#-B1）", "env_name": "Y2", "level": 10, "time": "2025-07-29 17:42:02.513501", "message": "Experiment Crash\n--------------------------------------------------\nBUS-2-FindBusCavityFreq-Rough-6888976a7f92df5525d835ca\n--------------------------------------------------\nVI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 457, in after_parsing\n    self.interface = Vxi11CoreClient(host_address, port, self.open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 361, in __init__\n    rpc.TCPClient.__init__(self, host, prog, vers, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 823, in __init__\n    pmap = TCPPortMapperClient(host, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 803, in __init__\n    RawTCPClient.__init__(self, host, PMAP_PROG, PMAP_VERS, PMAP_PORT, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 454, in __init__\n    self.connect(1e-3 * open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 488, in connect\n    raise RPCError(\"can't connect to server\")\npyvisa_py.protocols.rpc.RPCError: can't connect to server\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\find_cavity_freq.py\", line 124, in _initial\n    super()._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 271, in _initial\n    self.select_net_analyzer(network_analyzer_type)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 127, in select_net_analyzer\n    self.net_analyzer = NetworkAnalyzer(net_analyzer, self.config_path)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 37, in __init__\n    super(NetworkAnalyzer, self).__init__(instrument_model, config_path)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 29, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 75, in _get\n    return self._INST_LST.get(self._type)(self._visa_address)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\structures.py\", line 154, in __call__\n    obj = super().__call__(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\E5071C.py\", line 12, in __init__\n    super().__init__(visa_address)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\visa_inst.py\", line 35, in __init__\n    self.inst_obj = rm.open_resource(self.visa_address)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\highlevel.py\", line 3284, in open_resource\n    res.open(access_mode, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\resources\\resource.py\", line 282, in open\n    self.session, status = self._resource_manager.open_bare_resource(\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\highlevel.py\", line 3209, in open_bare_resource\n    return self.visalib.open(self.session, resource_name, access_mode, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\highlevel.py\", line 168, in open\n    sess = cls(session, resource_name, parsed, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 86, in __new__\n    return newcls(resource_manager_session, resource_name, parsed, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\sessions.py\", line 322, in __init__\n    self.after_parsing()\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 459, in after_parsing\n    raise errors.VisaIOError(constants.VI_ERROR_RSRC_NFOUND)\npyvisa.errors.VisaIOError: VI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-07-29T17:40:50.567Z"}}, {"_id": {"$oid": "68889728f43e794f372aa0dc"}, "username": "zqq", "sample": "250620-设计验证-102bit-V4.0（Si+Nb120nm+Ta30nm）-Base-6#（Flip-5#-B1）", "env_name": "Y2", "level": 10, "time": "2025-07-29 17:42:08.414168", "message": "Experiment Crash\n--------------------------------------------------\nBUS-3-FindBusCavityFreq-Rough-688897707f92df5525d835cd\n--------------------------------------------------\nVI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 457, in after_parsing\n    self.interface = Vxi11CoreClient(host_address, port, self.open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 361, in __init__\n    rpc.TCPClient.__init__(self, host, prog, vers, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 823, in __init__\n    pmap = TCPPortMapperClient(host, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 803, in __init__\n    RawTCPClient.__init__(self, host, PMAP_PROG, PMAP_VERS, PMAP_PORT, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 454, in __init__\n    self.connect(1e-3 * open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 488, in connect\n    raise RPCError(\"can't connect to server\")\npyvisa_py.protocols.rpc.RPCError: can't connect to server\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\find_cavity_freq.py\", line 124, in _initial\n    super()._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 271, in _initial\n    self.select_net_analyzer(network_analyzer_type)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 127, in select_net_analyzer\n    self.net_analyzer = NetworkAnalyzer(net_analyzer, self.config_path)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 37, in __init__\n    super(NetworkAnalyzer, self).__init__(instrument_model, config_path)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 29, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 75, in _get\n    return self._INST_LST.get(self._type)(self._visa_address)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\structures.py\", line 154, in __call__\n    obj = super().__call__(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\E5071C.py\", line 12, in __init__\n    super().__init__(visa_address)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\visa_inst.py\", line 35, in __init__\n    self.inst_obj = rm.open_resource(self.visa_address)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\highlevel.py\", line 3284, in open_resource\n    res.open(access_mode, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\resources\\resource.py\", line 282, in open\n    self.session, status = self._resource_manager.open_bare_resource(\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\highlevel.py\", line 3209, in open_bare_resource\n    return self.visalib.open(self.session, resource_name, access_mode, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\highlevel.py\", line 168, in open\n    sess = cls(session, resource_name, parsed, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 86, in __new__\n    return newcls(resource_manager_session, resource_name, parsed, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\sessions.py\", line 322, in __init__\n    self.after_parsing()\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 459, in after_parsing\n    raise errors.VisaIOError(constants.VI_ERROR_RSRC_NFOUND)\npyvisa.errors.VisaIOError: VI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-07-29T17:40:56.474Z"}}, {"_id": {"$oid": "6888972ef43e794f372aa0dd"}, "username": "zqq", "sample": "250620-设计验证-102bit-V4.0（Si+Nb120nm+Ta30nm）-Base-6#（Flip-5#-B1）", "env_name": "Y2", "level": 10, "time": "2025-07-29 17:42:14.355217", "message": "Experiment Crash\n--------------------------------------------------\nBUS-4-FindBusCavityFreq-Rough-688897767f92df5525d835d0\n--------------------------------------------------\nVI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 457, in after_parsing\n    self.interface = Vxi11CoreClient(host_address, port, self.open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 361, in __init__\n    rpc.TCPClient.__init__(self, host, prog, vers, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 823, in __init__\n    pmap = TCPPortMapperClient(host, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 803, in __init__\n    RawTCPClient.__init__(self, host, PMAP_PROG, PMAP_VERS, PMAP_PORT, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 454, in __init__\n    self.connect(1e-3 * open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 488, in connect\n    raise RPCError(\"can't connect to server\")\npyvisa_py.protocols.rpc.RPCError: can't connect to server\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\find_cavity_freq.py\", line 124, in _initial\n    super()._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 271, in _initial\n    self.select_net_analyzer(network_analyzer_type)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 127, in select_net_analyzer\n    self.net_analyzer = NetworkAnalyzer(net_analyzer, self.config_path)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 37, in __init__\n    super(NetworkAnalyzer, self).__init__(instrument_model, config_path)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 29, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 75, in _get\n    return self._INST_LST.get(self._type)(self._visa_address)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\structures.py\", line 154, in __call__\n    obj = super().__call__(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\E5071C.py\", line 12, in __init__\n    super().__init__(visa_address)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\visa_inst.py\", line 35, in __init__\n    self.inst_obj = rm.open_resource(self.visa_address)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\highlevel.py\", line 3284, in open_resource\n    res.open(access_mode, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\resources\\resource.py\", line 282, in open\n    self.session, status = self._resource_manager.open_bare_resource(\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\highlevel.py\", line 3209, in open_bare_resource\n    return self.visalib.open(self.session, resource_name, access_mode, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\highlevel.py\", line 168, in open\n    sess = cls(session, resource_name, parsed, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 86, in __new__\n    return newcls(resource_manager_session, resource_name, parsed, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\sessions.py\", line 322, in __init__\n    self.after_parsing()\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 459, in after_parsing\n    raise errors.VisaIOError(constants.VI_ERROR_RSRC_NFOUND)\npyvisa.errors.VisaIOError: VI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-07-29T17:41:02.402Z"}}, {"_id": {"$oid": "68889734f43e794f372aa0de"}, "username": "zqq", "sample": "250620-设计验证-102bit-V4.0（Si+Nb120nm+Ta30nm）-Base-6#（Flip-5#-B1）", "env_name": "Y2", "level": 10, "time": "2025-07-29 17:42:20.273172", "message": "Experiment Crash\n--------------------------------------------------\nBUS-5-FindBusCavityFreq-Rough-6888977c7f92df5525d835d3\n--------------------------------------------------\nVI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 457, in after_parsing\n    self.interface = Vxi11CoreClient(host_address, port, self.open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 361, in __init__\n    rpc.TCPClient.__init__(self, host, prog, vers, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 823, in __init__\n    pmap = TCPPortMapperClient(host, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 803, in __init__\n    RawTCPClient.__init__(self, host, PMAP_PROG, PMAP_VERS, PMAP_PORT, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 454, in __init__\n    self.connect(1e-3 * open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 488, in connect\n    raise RPCError(\"can't connect to server\")\npyvisa_py.protocols.rpc.RPCError: can't connect to server\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\find_cavity_freq.py\", line 124, in _initial\n    super()._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 271, in _initial\n    self.select_net_analyzer(network_analyzer_type)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 127, in select_net_analyzer\n    self.net_analyzer = NetworkAnalyzer(net_analyzer, self.config_path)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 37, in __init__\n    super(NetworkAnalyzer, self).__init__(instrument_model, config_path)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 29, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 75, in _get\n    return self._INST_LST.get(self._type)(self._visa_address)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\structures.py\", line 154, in __call__\n    obj = super().__call__(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\E5071C.py\", line 12, in __init__\n    super().__init__(visa_address)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\visa_inst.py\", line 35, in __init__\n    self.inst_obj = rm.open_resource(self.visa_address)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\highlevel.py\", line 3284, in open_resource\n    res.open(access_mode, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\resources\\resource.py\", line 282, in open\n    self.session, status = self._resource_manager.open_bare_resource(\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\highlevel.py\", line 3209, in open_bare_resource\n    return self.visalib.open(self.session, resource_name, access_mode, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\highlevel.py\", line 168, in open\n    sess = cls(session, resource_name, parsed, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 86, in __new__\n    return newcls(resource_manager_session, resource_name, parsed, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\sessions.py\", line 322, in __init__\n    self.after_parsing()\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 459, in after_parsing\n    raise errors.VisaIOError(constants.VI_ERROR_RSRC_NFOUND)\npyvisa.errors.VisaIOError: VI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-07-29T17:41:08.33Z"}}, {"_id": {"$oid": "6888973af43e794f372aa0df"}, "username": "zqq", "sample": "250620-设计验证-102bit-V4.0（Si+Nb120nm+Ta30nm）-Base-6#（Flip-5#-B1）", "env_name": "Y2", "level": 10, "time": "2025-07-29 17:42:26.197166", "message": "Experiment Crash\n--------------------------------------------------\nBUS-6-FindBusCavityFreq-Rough-688897817f92df5525d835d6\n--------------------------------------------------\nVI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 457, in after_parsing\n    self.interface = Vxi11CoreClient(host_address, port, self.open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 361, in __init__\n    rpc.TCPClient.__init__(self, host, prog, vers, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 823, in __init__\n    pmap = TCPPortMapperClient(host, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 803, in __init__\n    RawTCPClient.__init__(self, host, PMAP_PROG, PMAP_VERS, PMAP_PORT, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 454, in __init__\n    self.connect(1e-3 * open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 488, in connect\n    raise RPCError(\"can't connect to server\")\npyvisa_py.protocols.rpc.RPCError: can't connect to server\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\find_cavity_freq.py\", line 124, in _initial\n    super()._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 271, in _initial\n    self.select_net_analyzer(network_analyzer_type)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 127, in select_net_analyzer\n    self.net_analyzer = NetworkAnalyzer(net_analyzer, self.config_path)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 37, in __init__\n    super(NetworkAnalyzer, self).__init__(instrument_model, config_path)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 29, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 75, in _get\n    return self._INST_LST.get(self._type)(self._visa_address)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\structures.py\", line 154, in __call__\n    obj = super().__call__(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\E5071C.py\", line 12, in __init__\n    super().__init__(visa_address)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\visa_inst.py\", line 35, in __init__\n    self.inst_obj = rm.open_resource(self.visa_address)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\highlevel.py\", line 3284, in open_resource\n    res.open(access_mode, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\resources\\resource.py\", line 282, in open\n    self.session, status = self._resource_manager.open_bare_resource(\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\highlevel.py\", line 3209, in open_bare_resource\n    return self.visalib.open(self.session, resource_name, access_mode, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\highlevel.py\", line 168, in open\n    sess = cls(session, resource_name, parsed, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 86, in __new__\n    return newcls(resource_manager_session, resource_name, parsed, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\sessions.py\", line 322, in __init__\n    self.after_parsing()\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 459, in after_parsing\n    raise errors.VisaIOError(constants.VI_ERROR_RSRC_NFOUND)\npyvisa.errors.VisaIOError: VI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-07-29T17:41:14.257Z"}}, {"_id": {"$oid": "688897fc32aee6d0092aa0dc"}, "username": "zqq", "sample": "250620-设计验证-102bit-V4.0（Si+Nb120nm+Ta30nm）-Base-6#（Flip-5#-B1）", "env_name": "Y2", "level": 10, "time": "2025-07-29 17:45:40.830801", "message": "Experiment Crash\n--------------------------------------------------\nBUS1-Channel1-BusS21Collector-68889844871b6c2cc92fec34\n--------------------------------------------------\nVI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 457, in after_parsing\n    self.interface = Vxi11CoreClient(host_address, port, self.open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 361, in __init__\n    rpc.TCPClient.__init__(self, host, prog, vers, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 823, in __init__\n    pmap = TCPPortMapperClient(host, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 803, in __init__\n    RawTCPClient.__init__(self, host, PMAP_PROG, PMAP_VERS, PMAP_PORT, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 454, in __init__\n    self.connect(1e-3 * open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 488, in connect\n    raise RPCError(\"can't connect to server\")\npyvisa_py.protocols.rpc.RPCError: can't connect to server\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 271, in _initial\n    self.select_net_analyzer(network_analyzer_type)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 127, in select_net_analyzer\n    self.net_analyzer = NetworkAnalyzer(net_analyzer, self.config_path)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 37, in __init__\n    super(NetworkAnalyzer, self).__init__(instrument_model, config_path)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 29, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 75, in _get\n    return self._INST_LST.get(self._type)(self._visa_address)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\structures.py\", line 154, in __call__\n    obj = super().__call__(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\E5071C.py\", line 12, in __init__\n    super().__init__(visa_address)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\visa_inst.py\", line 35, in __init__\n    self.inst_obj = rm.open_resource(self.visa_address)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\highlevel.py\", line 3284, in open_resource\n    res.open(access_mode, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\resources\\resource.py\", line 282, in open\n    self.session, status = self._resource_manager.open_bare_resource(\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\highlevel.py\", line 3209, in open_bare_resource\n    return self.visalib.open(self.session, resource_name, access_mode, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\highlevel.py\", line 168, in open\n    sess = cls(session, resource_name, parsed, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 86, in __new__\n    return newcls(resource_manager_session, resource_name, parsed, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\sessions.py\", line 322, in __init__\n    self.after_parsing()\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 459, in after_parsing\n    raise errors.VisaIOError(constants.VI_ERROR_RSRC_NFOUND)\npyvisa.errors.VisaIOError: VI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-07-29T17:44:28.879Z"}}, {"_id": {"$oid": "68889988f6689fe5521f636f"}, "username": "zqq", "sample": "250620-设计验证-102bit-V4.0（Si+Nb120nm+Ta30nm）-Base-6#（Flip-5#-B1）", "env_name": "Y2", "level": 10, "time": "2025-07-29 17:52:16.061323", "message": "Experiment Crash\n--------------------------------------------------\nBUS1-Channel1-BusS21Collector-688899cf4e0989b747fd439c\n--------------------------------------------------\nVI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 457, in after_parsing\n    self.interface = Vxi11CoreClient(host_address, port, self.open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 361, in __init__\n    rpc.TCPClient.__init__(self, host, prog, vers, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 823, in __init__\n    pmap = TCPPortMapperClient(host, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 803, in __init__\n    RawTCPClient.__init__(self, host, PMAP_PROG, PMAP_VERS, PMAP_PORT, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 454, in __init__\n    self.connect(1e-3 * open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\protocols\\rpc.py\", line 488, in connect\n    raise RPCError(\"can't connect to server\")\npyvisa_py.protocols.rpc.RPCError: can't connect to server\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 271, in _initial\n    self.select_net_analyzer(network_analyzer_type)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 127, in select_net_analyzer\n    self.net_analyzer = NetworkAnalyzer(net_analyzer, self.config_path)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 37, in __init__\n    super(NetworkAnalyzer, self).__init__(instrument_model, config_path)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 29, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 75, in _get\n    return self._INST_LST.get(self._type)(self._visa_address)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\structures.py\", line 154, in __call__\n    obj = super().__call__(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\E5071C.py\", line 12, in __init__\n    super().__init__(visa_address)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\visa_inst.py\", line 35, in __init__\n    self.inst_obj = rm.open_resource(self.visa_address)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\highlevel.py\", line 3284, in open_resource\n    res.open(access_mode, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\resources\\resource.py\", line 282, in open\n    self.session, status = self._resource_manager.open_bare_resource(\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\highlevel.py\", line 3209, in open_bare_resource\n    return self.visalib.open(self.session, resource_name, access_mode, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\highlevel.py\", line 168, in open\n    sess = cls(session, resource_name, parsed, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 86, in __new__\n    return newcls(resource_manager_session, resource_name, parsed, open_timeout)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\sessions.py\", line 322, in __init__\n    self.after_parsing()\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\tcpip.py\", line 459, in after_parsing\n    raise errors.VisaIOError(constants.VI_ERROR_RSRC_NFOUND)\npyvisa.errors.VisaIOError: VI_ERROR_RSRC_NFOUND (-1073807343): Insufficient location information or the requested device or resource is not present in the system.\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-07-29T17:51:04.109Z"}}, {"_id": {"$oid": "68955efc26465fe58213b5e7"}, "username": "zqq", "sample": "250620-设计验证-102bit-V4.0（Si+Nb120nm+Ta30nm）-Base-6#（Flip-5#-B1）", "env_name": "Y2", "level": 10, "time": "2025-08-08 10:20:45.860556", "message": "Experiment Crash\n--------------------------------------------------\nq19q20q21q22q23q24-BUS4-Channel4-ImpaGain-68955efa973ee1f9ed36e62a\n--------------------------------------------------\nVI_ERROR_IO (-1073807298): Could not perform operation because of I/O error.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 326, in run\n    self._initial()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 271, in _initial\n    self.select_net_analyzer(network_analyzer_type)\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 127, in select_net_analyzer\n    self.net_analyzer = NetworkAnalyzer(net_analyzer, self.config_path)\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_inst.py\", line 38, in __init__\n    self.inst.outputON()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\instrument\\E5071C.py\", line 16, in outputON\n    self.inst_obj.write(':OUTPUT ON')\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\pyvisa\\resources\\messagebased.py\", line 197, in write\n    count = self.write_raw(message.encode(enco))\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\pyvisa\\resources\\messagebased.py\", line 157, in write_raw\n    return self.visalib.write(self.session, message)[0]\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\pyvisa_py\\highlevel.py\", line 548, in write\n    return written, self.handle_return_value(session, status_code)\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\pyvisa\\highlevel.py\", line 251, in handle_return_value\n    raise errors.VisaIOError(rv)\npyvisa.errors.VisaIOError: VI_ERROR_IO (-1073807298): Could not perform operation because of I/O error.\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-08-08T10:20:44.524Z"}}, {"_id": {"$oid": "68955efc5dd635150013b5dc"}, "username": "zqq", "sample": "250620-设计验证-102bit-V4.0（Si+Nb120nm+Ta30nm）-Base-6#（Flip-5#-B1）", "env_name": "Y2", "level": 10, "time": "2025-08-08 10:22:13.278675", "message": "Experiment Crash\n--------------------------------------------------\nbus-6-ImpaGain-68955f4dc4eedb107ae68e7f\n--------------------------------------------------\nVI_ERROR_IO (-1073807298): Could not perform operation because of I/O error.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 329, in run\n    self._run_experiment()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_gain.py\", line 213, in _run_experiment\n    data = self._run_open_mode()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_gain.py\", line 159, in _run_open_mode\n    _, amp_off, _ = self.net_analyzer.read_result(True, True, True)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 77, in read_result\n    amp = self.inst.getAmpList()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\E5071C.py\", line 162, in getAmpList\n    AmpListStr = self.inst_obj.query(':CALC1:DATA:FDAT?')\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\resources\\messagebased.py\", line 648, in query\n    return self.read()\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\resources\\messagebased.py\", line 486, in read\n    message = self._read_raw().decode(enco)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\resources\\messagebased.py\", line 442, in _read_raw\n    chunk, status = self.visalib.read(self.session, size)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\highlevel.py\", line 520, in read\n    return data, self.handle_return_value(session, status_code)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\highlevel.py\", line 251, in handle_return_value\n    raise errors.VisaIOError(rv)\npyvisa.errors.VisaIOError: VI_ERROR_IO (-1073807298): Could not perform operation because of I/O error.\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-08-08T10:20:44.535Z"}}, {"_id": {"$oid": "68955f4c26465fe58213b5e8"}, "username": "zqq", "sample": "250620-设计验证-102bit-V4.0（Si+Nb120nm+Ta30nm）-Base-6#（Flip-5#-B1）", "env_name": "Y2", "level": 10, "time": "2025-08-08 10:22:05.716604", "message": "Experiment Crash\n--------------------------------------------------\nq19q20q21q22q23q24-BUS4-Channel4-ImpaGain-68955f41973ee1f9ed36e630\n--------------------------------------------------\nVI_ERROR_IO (-1073807298): Could not perform operation because of I/O error.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 329, in run\n    self._run_experiment()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\preliminary\\library\\impa_gain.py\", line 210, in _run_experiment\n    data = self._run_open_mode()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\preliminary\\library\\impa_gain.py\", line 163, in _run_open_mode\n    _, amp_on, _ = self.net_analyzer.read_result(True, True, True)\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_inst.py\", line 79, in read_result\n    phase = self.inst.getExtPhaseList()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\instrument\\E5071C.py\", line 187, in getExtPhaseList\n    ExtPhaseListStr = self.inst_obj.query(':CALC1:DATA:FDAT?')\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\pyvisa\\resources\\messagebased.py\", line 648, in query\n    return self.read()\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\pyvisa\\resources\\messagebased.py\", line 486, in read\n    message = self._read_raw().decode(enco)\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\pyvisa\\resources\\messagebased.py\", line 442, in _read_raw\n    chunk, status = self.visalib.read(self.session, size)\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\pyvisa_py\\highlevel.py\", line 520, in read\n    return data, self.handle_return_value(session, status_code)\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\pyvisa\\highlevel.py\", line 251, in handle_return_value\n    raise errors.VisaIOError(rv)\npyvisa.errors.VisaIOError: VI_ERROR_IO (-1073807298): Could not perform operation because of I/O error.\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-08-08T10:22:04.389Z"}}, {"_id": {"$oid": "68955fe726465fe58213b5ea"}, "username": "zqq", "sample": "250620-设计验证-102bit-V4.0（Si+Nb120nm+Ta30nm）-Base-6#（Flip-5#-B1）", "env_name": "Y2", "level": 10, "time": "2025-08-08 10:26:08.330524", "message": "Experiment Crash\n--------------------------------------------------\nbus-16-ImpaGain-68956039c4eedb107ae68ea0\n--------------------------------------------------\nVI_ERROR_IO (-1073807298): Could not perform operation because of I/O error.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 329, in run\n    self._run_experiment()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_gain.py\", line 213, in _run_experiment\n    data = self._run_open_mode()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\impa_gain.py\", line 159, in _run_open_mode\n    _, amp_off, _ = self.net_analyzer.read_result(True, True, True)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_inst.py\", line 72, in read_result\n    self.inst.singleTrig()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\E5071C.py\", line 148, in singleTrig\n    while self.isMeasuring():\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\instrument\\E5071C.py\", line 135, in isMeasuring\n    runState = self.inst_obj.query(':STAT:OPER:COND?')\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\resources\\messagebased.py\", line 648, in query\n    return self.read()\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\resources\\messagebased.py\", line 486, in read\n    message = self._read_raw().decode(enco)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\resources\\messagebased.py\", line 442, in _read_raw\n    chunk, status = self.visalib.read(self.session, size)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa_py\\highlevel.py\", line 520, in read\n    return data, self.handle_return_value(session, status_code)\n  File \"D:\\miniconda3\\lib\\site-packages\\pyvisa\\highlevel.py\", line 251, in handle_return_value\n    raise errors.VisaIOError(rv)\npyvisa.errors.VisaIOError: VI_ERROR_IO (-1073807298): Could not perform operation because of I/O error.\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-08-08T10:24:39.579Z"}}, {"_id": {"$oid": "68955fe726465fe58213b5eb"}, "username": "zqq", "sample": "250620-设计验证-102bit-V4.0（Si+Nb120nm+Ta30nm）-Base-6#（Flip-5#-B1）", "env_name": "Y2", "level": 10, "time": "2025-08-08 10:24:41.007644", "message": "Experiment Crash\n--------------------------------------------------\nq25q26q27q28q29q30-BUS5-Channel5-ImpaGain-68955fdc973ee1f9ed36e636\n--------------------------------------------------\nVI_ERROR_IO (-1073807298): Could not perform operation because of I/O error.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_models.py\", line 329, in run\n    self._run_experiment()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\preliminary\\library\\impa_gain.py\", line 210, in _run_experiment\n    data = self._run_open_mode()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\preliminary\\library\\impa_gain.py\", line 163, in _run_open_mode\n    _, amp_on, _ = self.net_analyzer.read_result(True, True, True)\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\preliminary\\preliminary_inst.py\", line 79, in read_result\n    phase = self.inst.getExtPhaseList()\n  File \"D:\\code\\project\\lzw\\pyqcat-visage\\pyQCat\\instrument\\E5071C.py\", line 186, in getExtPhaseList\n    self.inst_obj.write(':CALC1:FORM UPH\\n')\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\pyvisa\\resources\\messagebased.py\", line 197, in write\n    count = self.write_raw(message.encode(enco))\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\pyvisa\\resources\\messagebased.py\", line 157, in write_raw\n    return self.visalib.write(self.session, message)[0]\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\pyvisa_py\\highlevel.py\", line 548, in write\n    return written, self.handle_return_value(session, status_code)\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\visage\\lib\\site-packages\\pyvisa\\highlevel.py\", line 251, in handle_return_value\n    raise errors.VisaIOError(rv)\npyvisa.errors.VisaIOError: VI_ERROR_IO (-1073807298): Could not perform operation because of I/O error.\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-08-08T10:24:39.672Z"}}]