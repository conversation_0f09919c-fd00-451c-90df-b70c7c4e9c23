[{"_id": {"$oid": "686df9692ebaa9fee5f729fe"}, "username": "job", "sample": "D8-24bit-2", "env_name": "D8-2-3", "level": 5, "time": "2025-07-09 13:08:56.589847", "message": "Experiment Fail\n--------------------------------------------------\nq17-QubitSpectrumF12-686df968318a0dde21a21ead\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-07-09T13:08:57.089Z"}}, {"_id": {"$oid": "686df969ec42392c4af72a06"}, "username": "job", "sample": "D8-24bit-2", "env_name": "D8-2-3", "level": 5, "time": "2025-07-09 13:08:56.598849", "message": "Experiment Fail\n--------------------------------------------------\nq18-QubitSpectrumF12-686df968318a0dde21a21eae\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-07-09T13:08:57.09Z"}}, {"_id": {"$oid": "686df9692ebaa9fee5f729ff"}, "username": "job", "sample": "D8-24bit-2", "env_name": "D8-2-3", "level": 5, "time": "2025-07-09 13:08:56.607851", "message": "Experiment Fail\n--------------------------------------------------\nq19-QubitSpectrumF12-686df968318a0dde21a21eaf\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-07-09T13:08:57.158Z"}}, {"_id": {"$oid": "686df9692ebaa9fee5f72a00"}, "username": "job", "sample": "D8-24bit-2", "env_name": "D8-2-3", "level": 5, "time": "2025-07-09 13:08:56.623854", "message": "Experiment Fail\n--------------------------------------------------\nq21-QubitSpectrumF12-686df968318a0dde21a21eb1\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-07-09T13:08:57.159Z"}}, {"_id": {"$oid": "686df9692ebaa9fee5f72a01"}, "username": "job", "sample": "D8-24bit-2", "env_name": "D8-2-3", "level": 5, "time": "2025-07-09 13:08:56.637859", "message": "Experiment Fail\n--------------------------------------------------\nq23-QubitSpectrumF12-686df968318a0dde21a21eb3\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-07-09T13:08:57.159Z"}}, {"_id": {"$oid": "686df969ec42392c4af72a07"}, "username": "job", "sample": "D8-24bit-2", "env_name": "D8-2-3", "level": 5, "time": "2025-07-09 13:08:56.616852", "message": "Experiment Fail\n--------------------------------------------------\nq20-QubitSpectrumF12-686df968318a0dde21a21eb0\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-07-09T13:08:57.183Z"}}, {"_id": {"$oid": "686df969ec42392c4af72a08"}, "username": "job", "sample": "D8-24bit-2", "env_name": "D8-2-3", "level": 5, "time": "2025-07-09 13:08:56.630855", "message": "Experiment Fail\n--------------------------------------------------\nq22-QubitSpectrumF12-686df968318a0dde21a21eb2\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-07-09T13:08:57.184Z"}}, {"_id": {"$oid": "686df969ec42392c4af72a09"}, "username": "job", "sample": "D8-24bit-2", "env_name": "D8-2-3", "level": 5, "time": "2025-07-09 13:08:56.645860", "message": "Experiment Fail\n--------------------------------------------------\nq24-QubitSpectrumF12-686df968318a0dde21a21eb4\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-07-09T13:08:57.184Z"}}, {"_id": {"$oid": "686df9692ebaa9fee5f72a02"}, "username": "job", "sample": "D8-24bit-2", "env_name": "D8-2-3", "level": 5, "time": "2025-07-09 13:08:57.347563", "message": "Experiment Fail\n--------------------------------------------------\nq17-QubitSpectrumF12_2D-686df969318a0dde21a21eb7\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-07-09T13:08:57.852Z"}}, {"_id": {"$oid": "686df969ec42392c4af72a0a"}, "username": "job", "sample": "D8-24bit-2", "env_name": "D8-2-3", "level": 5, "time": "2025-07-09 13:08:57.356566", "message": "Experiment Fail\n--------------------------------------------------\nq18-QubitSpectrumF12_2D-686df969318a0dde21a21eb8\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-07-09T13:08:57.853Z"}}, {"_id": {"$oid": "686df969ec42392c4af72a0b"}, "username": "job", "sample": "D8-24bit-2", "env_name": "D8-2-3", "level": 5, "time": "2025-07-09 13:08:57.371569", "message": "Experiment Fail\n--------------------------------------------------\nq20-QubitSpectrumF12_2D-686df969318a0dde21a21eba\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-07-09T13:08:57.947Z"}}, {"_id": {"$oid": "686df969ec42392c4af72a0c"}, "username": "job", "sample": "D8-24bit-2", "env_name": "D8-2-3", "level": 5, "time": "2025-07-09 13:08:57.386573", "message": "Experiment Fail\n--------------------------------------------------\nq22-QubitSpectrumF12_2D-686df969318a0dde21a21ebc\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-07-09T13:08:57.949Z"}}, {"_id": {"$oid": "686df969ec42392c4af72a0d"}, "username": "job", "sample": "D8-24bit-2", "env_name": "D8-2-3", "level": 5, "time": "2025-07-09 13:08:57.401577", "message": "Experiment Fail\n--------------------------------------------------\nq24-QubitSpectrumF12_2D-686df969318a0dde21a21ebe\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-07-09T13:08:57.949Z"}}, {"_id": {"$oid": "686df9692ebaa9fee5f72a03"}, "username": "job", "sample": "D8-24bit-2", "env_name": "D8-2-3", "level": 5, "time": "2025-07-09 13:08:57.363567", "message": "Experiment Fail\n--------------------------------------------------\nq19-QubitSpectrumF12_2D-686df969318a0dde21a21eb9\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-07-09T13:08:57.948Z"}}, {"_id": {"$oid": "686df9692ebaa9fee5f72a04"}, "username": "job", "sample": "D8-24bit-2", "env_name": "D8-2-3", "level": 5, "time": "2025-07-09 13:08:57.379572", "message": "Experiment Fail\n--------------------------------------------------\nq21-QubitSpectrumF12_2D-686df969318a0dde21a21ebb\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-07-09T13:08:57.949Z"}}, {"_id": {"$oid": "686df9692ebaa9fee5f72a05"}, "username": "job", "sample": "D8-24bit-2", "env_name": "D8-2-3", "level": 5, "time": "2025-07-09 13:08:57.393576", "message": "Experiment Fail\n--------------------------------------------------\nq23-QubitSpectrumF12_2D-686df969318a0dde21a21ebd\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-07-09T13:08:57.95Z"}}, {"_id": {"$oid": "6890b1be6946627d159cc35e"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-04 21:12:40.321146", "message": "Experiment Fail\n--------------------------------------------------\nq1-QubitSpectrumF12-6890b1c8c7216026b418df76\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-04T21:12:30.85Z"}}, {"_id": {"$oid": "6890b1be9192f4bbc39cc360"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-04 21:12:40.330146", "message": "Experiment Fail\n--------------------------------------------------\nq2-QubitSpectrumF12-6890b1c8c7216026b418df77\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-04T21:12:30.851Z"}}, {"_id": {"$oid": "6890b1be6946627d159cc35f"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-04 21:12:40.338147", "message": "Experiment Fail\n--------------------------------------------------\nq3-QubitSpectrumF12-6890b1c8c7216026b418df78\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-04T21:12:30.94Z"}}, {"_id": {"$oid": "6890b1be6946627d159cc360"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-04 21:12:40.377149", "message": "Experiment Fail\n--------------------------------------------------\nq7-QubitSpectrumF12-6890b1c8c7216026b418df7c\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-04T21:12:30.941Z"}}, {"_id": {"$oid": "6890b1be9192f4bbc39cc361"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-04 21:12:40.347148", "message": "Experiment Fail\n--------------------------------------------------\nq4-QubitSpectrumF12-6890b1c8c7216026b418df79\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-04T21:12:30.942Z"}}, {"_id": {"$oid": "6890b1be6946627d159cc361"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-04 21:12:40.355149", "message": "Experiment Fail\n--------------------------------------------------\nq5-QubitSpectrumF12-6890b1c8c7216026b418df7a\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-04T21:12:30.942Z"}}, {"_id": {"$oid": "6890b1be9192f4bbc39cc362"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-04 21:12:40.368150", "message": "Experiment Fail\n--------------------------------------------------\nq6-QubitSpectrumF12-6890b1c8c7216026b418df7b\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-04T21:12:30.943Z"}}, {"_id": {"$oid": "6890b1be9192f4bbc39cc363"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-04 21:12:40.385151", "message": "Experiment Fail\n--------------------------------------------------\nq8-QubitSpectrumF12-6890b1c8c7216026b418df7d\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-04T21:12:30.943Z"}}, {"_id": {"$oid": "6890b1bf6946627d159cc362"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-04 21:12:41.295034", "message": "Experiment Fail\n--------------------------------------------------\nq2-QubitSpectrumF12_2D-6890b1c8c7216026b418df81\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-04T21:12:31.812Z"}}, {"_id": {"$oid": "6890b1bf9192f4bbc39cc364"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-04 21:12:41.285987", "message": "Experiment Fail\n--------------------------------------------------\nq1-QubitSpectrumF12_2D-6890b1c8c7216026b418df80\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-04T21:12:31.811Z"}}, {"_id": {"$oid": "6890b1bf6946627d159cc363"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-04 21:12:41.312090", "message": "Experiment Fail\n--------------------------------------------------\nq4-QubitSpectrumF12_2D-6890b1c8c7216026b418df83\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-04T21:12:31.903Z"}}, {"_id": {"$oid": "6890b1bf6946627d159cc364"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-04 21:12:41.329096", "message": "Experiment Fail\n--------------------------------------------------\nq6-QubitSpectrumF12_2D-6890b1c8c7216026b418df85\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-04T21:12:31.904Z"}}, {"_id": {"$oid": "6890b1bf9192f4bbc39cc365"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-04 21:12:41.320101", "message": "Experiment Fail\n--------------------------------------------------\nq5-QubitSpectrumF12_2D-6890b1c8c7216026b418df84\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-04T21:12:31.904Z"}}, {"_id": {"$oid": "6890b1bf6946627d159cc365"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-04 21:12:41.346098", "message": "Experiment Fail\n--------------------------------------------------\nq8-QubitSpectrumF12_2D-6890b1c8c7216026b418df87\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-04T21:12:31.905Z"}}, {"_id": {"$oid": "6890b1bf9192f4bbc39cc366"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-04 21:12:41.337097", "message": "Experiment Fail\n--------------------------------------------------\nq7-QubitSpectrumF12_2D-6890b1c8c7216026b418df86\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-04T21:12:31.905Z"}}, {"_id": {"$oid": "6890b1bf9192f4bbc39cc367"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-04 21:12:41.302547", "message": "Experiment Fail\n--------------------------------------------------\nq3-QubitSpectrumF12_2D-6890b1c8c7216026b418df82\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-04T21:12:31.992Z"}}, {"_id": {"$oid": "6890b90e50c6afe211883df5"}, "username": "yxyY413", "sample": "Reset_LRU_test", "env_name": "Y8", "level": 5, "time": "2025-08-04 21:43:37.185110", "message": "Experiment Fail\n--------------------------------------------------\nq17-QubitSpectrumF12-6890b909a64d966d6103e78b\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\software\\Anaconda\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 140, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than 800 and smaller than 1300 MHz, found: 718.105 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than 800 and smaller than 1300 MHz, found: 718.105 MHz'\n", "version": "monster | 0.23.2 | B", "create_time": {"$date": "2025-08-04T21:43:42.438Z"}}, {"_id": {"$oid": "6890b90e150663897c883a2a"}, "username": "yxyY413", "sample": "Reset_LRU_test", "env_name": "Y8", "level": 5, "time": "2025-08-04 21:43:37.186103", "message": "Experiment Fail\n--------------------------------------------------\nq11-QubitSpectrumF12-6890b909a64d966d6103e78a\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\software\\Anaconda\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 140, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than 800 and smaller than 1300 MHz, found: 683.952 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than 800 and smaller than 1300 MHz, found: 683.952 MHz'\n", "version": "monster | 0.23.2 | B", "create_time": {"$date": "2025-08-04T21:43:42.438Z"}}, {"_id": {"$oid": "6890b90e50c6afe211883df6"}, "username": "yxyY413", "sample": "Reset_LRU_test", "env_name": "Y8", "level": 5, "time": "2025-08-04 21:43:37.189093", "message": "Experiment Fail\n--------------------------------------------------\nq5-QubitSpectrumF12-6890b909a64d966d6103e78d\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\software\\Anaconda\\envs\\visage\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 140, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than 800 and smaller than 1300 MHz, found: 725.829 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than 800 and smaller than 1300 MHz, found: 725.829 MHz'\n", "version": "monster | 0.23.2 | B", "create_time": {"$date": "2025-08-04T21:43:42.534Z"}}, {"_id": {"$oid": "6891672d9192f4bbc39cc62c"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 10:06:47.935263", "message": "Experiment Fail\n--------------------------------------------------\nq1-QubitSpectrumF12-6891673790761059a6ef2c41\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T10:06:37.774Z"}}, {"_id": {"$oid": "6891672d6946627d159cc676"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 10:06:47.945265", "message": "Experiment Fail\n--------------------------------------------------\nq2-QubitSpectrumF12-6891673790761059a6ef2c42\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T10:06:37.775Z"}}, {"_id": {"$oid": "6891672d9192f4bbc39cc62d"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 10:06:47.954266", "message": "Experiment Fail\n--------------------------------------------------\nq3-QubitSpectrumF12-6891673790761059a6ef2c43\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T10:06:37.865Z"}}, {"_id": {"$oid": "6891672d6946627d159cc677"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 10:06:47.962266", "message": "Experiment Fail\n--------------------------------------------------\nq4-QubitSpectrumF12-6891673790761059a6ef2c44\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T10:06:37.866Z"}}, {"_id": {"$oid": "6891672d9192f4bbc39cc62e"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 10:06:47.970266", "message": "Experiment Fail\n--------------------------------------------------\nq5-QubitSpectrumF12-6891673790761059a6ef2c45\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T10:06:37.866Z"}}, {"_id": {"$oid": "6891672d9192f4bbc39cc62f"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 10:06:47.986269", "message": "Experiment Fail\n--------------------------------------------------\nq7-QubitSpectrumF12-6891673790761059a6ef2c47\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T10:06:37.867Z"}}, {"_id": {"$oid": "6891672d6946627d159cc678"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 10:06:47.978268", "message": "Experiment Fail\n--------------------------------------------------\nq6-QubitSpectrumF12-6891673790761059a6ef2c46\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T10:06:37.866Z"}}, {"_id": {"$oid": "6891672d6946627d159cc679"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 10:06:47.995272", "message": "Experiment Fail\n--------------------------------------------------\nq8-QubitSpectrumF12-6891673790761059a6ef2c48\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T10:06:37.867Z"}}, {"_id": {"$oid": "6891672e6946627d159cc67a"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 10:06:48.772578", "message": "Experiment Fail\n--------------------------------------------------\nq1-QubitSpectrumF12_2D-6891673890761059a6ef2c4b\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T10:06:38.6Z"}}, {"_id": {"$oid": "6891672e9192f4bbc39cc630"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 10:06:48.781579", "message": "Experiment Fail\n--------------------------------------------------\nq2-QubitSpectrumF12_2D-6891673890761059a6ef2c4c\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T10:06:38.615Z"}}, {"_id": {"$oid": "6891672e6946627d159cc67b"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 10:06:48.790611", "message": "Experiment Fail\n--------------------------------------------------\nq3-QubitSpectrumF12_2D-6891673890761059a6ef2c4d\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T10:06:38.695Z"}}, {"_id": {"$oid": "6891672e6946627d159cc67c"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 10:06:48.806581", "message": "Experiment Fail\n--------------------------------------------------\nq5-QubitSpectrumF12_2D-6891673890761059a6ef2c4f\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T10:06:38.697Z"}}, {"_id": {"$oid": "6891672e6946627d159cc67d"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 10:06:48.822583", "message": "Experiment Fail\n--------------------------------------------------\nq7-QubitSpectrumF12_2D-6891673890761059a6ef2c51\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T10:06:38.698Z"}}, {"_id": {"$oid": "6891672e9192f4bbc39cc631"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 10:06:48.798584", "message": "Experiment Fail\n--------------------------------------------------\nq4-QubitSpectrumF12_2D-6891673890761059a6ef2c4e\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T10:06:38.705Z"}}, {"_id": {"$oid": "6891672e9192f4bbc39cc632"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 10:06:48.814582", "message": "Experiment Fail\n--------------------------------------------------\nq6-QubitSpectrumF12_2D-6891673890761059a6ef2c50\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T10:06:38.706Z"}}, {"_id": {"$oid": "6891672e9192f4bbc39cc633"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 10:06:48.830584", "message": "Experiment Fail\n--------------------------------------------------\nq8-QubitSpectrumF12_2D-6891673890761059a6ef2c52\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T10:06:38.707Z"}}, {"_id": {"$oid": "6891ae68b84b084e52fb05dc"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 15:10:43.270722", "message": "Experiment Fail\n--------------------------------------------------\nq1-QubitSpectrumF12-6891ae7394a082d218d9ef32\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T15:10:32.124Z"}}, {"_id": {"$oid": "6891ae68890a246c6cfb06ca"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 15:10:43.279719", "message": "Experiment Fail\n--------------------------------------------------\nq2-QubitSpectrumF12-6891ae7394a082d218d9ef33\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T15:10:32.139Z"}}, {"_id": {"$oid": "6891ae68b84b084e52fb05dd"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 15:10:43.288721", "message": "Experiment Fail\n--------------------------------------------------\nq3-QubitSpectrumF12-6891ae7394a082d218d9ef34\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T15:10:32.215Z"}}, {"_id": {"$oid": "6891ae68b84b084e52fb05de"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 15:10:43.306721", "message": "Experiment Fail\n--------------------------------------------------\nq5-QubitSpectrumF12-6891ae7394a082d218d9ef36\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T15:10:32.216Z"}}, {"_id": {"$oid": "6891ae68b84b084e52fb05df"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 15:10:43.325721", "message": "Experiment Fail\n--------------------------------------------------\nq7-QubitSpectrumF12-6891ae7394a082d218d9ef38\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T15:10:32.217Z"}}, {"_id": {"$oid": "6891ae68890a246c6cfb06cb"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 15:10:43.296720", "message": "Experiment Fail\n--------------------------------------------------\nq4-QubitSpectrumF12-6891ae7394a082d218d9ef35\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T15:10:32.231Z"}}, {"_id": {"$oid": "6891ae68890a246c6cfb06cc"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 15:10:43.316723", "message": "Experiment Fail\n--------------------------------------------------\nq6-QubitSpectrumF12-6891ae7394a082d218d9ef37\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T15:10:32.231Z"}}, {"_id": {"$oid": "6891ae68890a246c6cfb06cd"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 15:10:43.335721", "message": "Experiment Fail\n--------------------------------------------------\nq8-QubitSpectrumF12-6891ae7394a082d218d9ef39\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T15:10:32.232Z"}}, {"_id": {"$oid": "6891ae68b84b084e52fb05e0"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 15:10:44.052736", "message": "Experiment Fail\n--------------------------------------------------\nq1-QubitSpectrumF12_2D-6891ae7394a082d218d9ef3c\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T15:10:32.905Z"}}, {"_id": {"$oid": "6891ae68890a246c6cfb06ce"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 15:10:44.060736", "message": "Experiment Fail\n--------------------------------------------------\nq2-QubitSpectrumF12_2D-6891ae7394a082d218d9ef3d\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T15:10:32.919Z"}}, {"_id": {"$oid": "6891ae68b84b084e52fb05e1"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 15:10:44.069738", "message": "Experiment Fail\n--------------------------------------------------\nq3-QubitSpectrumF12_2D-6891ae7394a082d218d9ef3e\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T15:10:32.997Z"}}, {"_id": {"$oid": "6891ae68b84b084e52fb05e2"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 15:10:44.077738", "message": "Experiment Fail\n--------------------------------------------------\nq4-QubitSpectrumF12_2D-6891ae7394a082d218d9ef3f\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T15:10:32.998Z"}}, {"_id": {"$oid": "6891ae69b84b084e52fb05e3"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 15:10:44.110739", "message": "Experiment Fail\n--------------------------------------------------\nq8-QubitSpectrumF12_2D-6891ae7394a082d218d9ef43\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T15:10:32.999Z"}}, {"_id": {"$oid": "6891ae69b84b084e52fb05e4"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 15:10:44.094737", "message": "Experiment Fail\n--------------------------------------------------\nq6-QubitSpectrumF12_2D-6891ae7394a082d218d9ef41\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T15:10:32.999Z"}}, {"_id": {"$oid": "6891ae69890a246c6cfb06cf"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 15:10:44.086736", "message": "Experiment Fail\n--------------------------------------------------\nq5-QubitSpectrumF12_2D-6891ae7394a082d218d9ef40\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T15:10:33.011Z"}}, {"_id": {"$oid": "6891ae69890a246c6cfb06d0"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 5, "time": "2025-08-05 15:10:44.102739", "message": "Experiment Fail\n--------------------------------------------------\nq7-QubitSpectrumF12_2D-6891ae7394a082d218d9ef42\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 135, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 81, in set_xy_pulses\n    xy_pulses = QubitSpectrumF12.get_xy_pulse(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\f12_spectrum.py\", line 132, in get_xy_pulse\n    + new_f12_pulse()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 132, in __call__\n    self._fake_pulse = [self.__class__._from_parameters(self.parameters)]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 147, in _from_parameters\n    return cls(**parameters)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 286, in __init__\n    super().__init__(time, name)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\base_pulse.py\", line 98, in __init__\n    self.validate_parameters()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\pulse\\pulse_lib.py\", line 326, in validate_parameters\n    raise PulseError(\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\npyQCat.errors.PulseError: 'baseband_freq must be greater than -250 and smaller than 250 MHz, found: -500.0 MHz'\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T15:10:33.012Z"}}]