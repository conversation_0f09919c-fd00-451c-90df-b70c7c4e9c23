[{"_id": "682d711ca77f3478825739f7", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(1-41)-Detune=-40\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711ca77f3478825739f8", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(3-41)-Detune=-36\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711ca77f3478825739f9", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(2-41)-Detune=-38\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711ca77f3478825739fa", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(4-41)-Detune=-34\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711ca77f3478825739fb", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(5-41)-Detune=-32\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711ca77f3478825739fc", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(6-41)-Detune=-30\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711ca77f3478825739fd", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(7-41)-Detune=-28\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711ca77f3478825739fe", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(8-41)-Detune=-26\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711ca77f3478825739ff", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(9-41)-Detune=-24\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711ca77f347882573a00", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(10-41)-Detune=-22\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711ca77f347882573a01", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(12-41)-Detune=-18\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711ca77f347882573a02", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(11-41)-Detune=-20\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711ca77f347882573a03", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(13-41)-Detune=-16\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711da77f347882573a04", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(14-41)-Detune=-14\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711da77f347882573a05", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(15-41)-Detune=-12\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711da77f347882573a06", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(16-41)-Detune=-10\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711da77f347882573a07", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(17-41)-Detune=-8\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711da77f347882573a08", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(18-41)-Detune=-6\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711da77f347882573a09", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(19-41)-Detune=-4\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711da77f347882573a0a", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(20-41)-Detune=-2\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711da77f347882573a0b", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(21-41)-Detune=0\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711da77f347882573a0c", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(23-41)-Detune=4\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711da77f347882573a0d", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(22-41)-Detune=2\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711da77f347882573a0e", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(24-41)-Detune=6\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711da77f347882573a0f", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(25-41)-Detune=8\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711da77f347882573a10", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(26-41)-Detune=10\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711da77f347882573a11", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(27-41)-Detune=12\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711da77f347882573a12", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(29-41)-Detune=16\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711da77f347882573a13", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(28-41)-Detune=14\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711da77f347882573a14", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(30-41)-Detune=18\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711da77f347882573a15", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(31-41)-Detune=20\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711ea77f347882573a16", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(32-41)-Detune=22\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711ea77f347882573a17", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(33-41)-Detune=24\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711ea77f347882573a18", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(34-41)-Detune=26\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711ea77f347882573a19", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(36-41)-Detune=30\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711ea77f347882573a1a", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(35-41)-Detune=28\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711ea77f347882573a1b", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(37-41)-Detune=32\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711ea77f347882573a1c", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(38-41)-Detune=34\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711ea77f347882573a1d", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(39-41)-Detune=36\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711ea77f347882573a1e", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(40-41)-Detune=38\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d711ea77f347882573a1f", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(41-41)-Detune=40\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d8395a77f347882573abf", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerRabiScanWidth\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "682d83eba77f347882573ac1", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerRabiScanWidthDetune\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n"}, {"_id": "68382f45f04f1fe4feb73d44", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(SwapOnce) | default | Count-Identity(12)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 242, in _validate_and_merge\n    assert len(set(label_list)) == 1, (\nAssertionError: Different sub experiment labels participating in parallel, details:\n[]\n"}, {"_id": "6839299af04f1fe4feb73e55", "message": "Parallel Me<PERSON>ail\n--------------------------------------------------\nParalle<PERSON>-Child-Group-Exp(RamseyExtend) | default | Count-Identity(1)\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 248, in _validate_and_merge\n    assert len(xy_channel_list) == len(set(xy_channel_list)), (\nAssertionError: Found duplicate xy channels, details:\n[52, 29, 74, 28, 15, 52, 32, 15, 53, 7, 33, 33, 7, 34, 7, 7, 25, 25, 80, 34, 55, 98, 4, 8, 85, 41, 16, 40, 10, 99, 87, 12, 18, 18, 67, 13, 18, 97, 18, 100, 14, 28, 93, 21, 100, 45, 27, 47]\n"}, {"_id": "6839299cf04f1fe4feb73eb6", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(RamseyExtend) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 242, in _validate_and_merge\n    assert len(set(label_list)) == 1, (\nAssertionError: Different sub experiment labels participating in parallel, details:\n[]\n"}, {"_id": "6839299ff04f1fe4feb73eb8", "message": "Parallel Me<PERSON>ail\n--------------------------------------------------\nParalle<PERSON>-Child-Group-Exp(RamseyExtend) | default | Count-Identity(1)\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 248, in _validate_and_merge\n    assert len(xy_channel_list) == len(set(xy_channel_list)), (\nAssertionError: Found duplicate xy channels, details:\n[52, 29, 74, 28, 15, 15, 52, 32, 53, 7, 7, 33, 33, 34, 7, 7, 80, 25, 25, 55, 98, 4, 8, 34, 85, 41, 16, 40, 10, 99, 87, 12, 18, 18, 67, 97, 13, 14, 18, 18, 100, 28, 93, 21, 100, 45, 27, 47]\n"}, {"_id": "683929a1f04f1fe4feb73f19", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(RamseyExtend) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 242, in _validate_and_merge\n    assert len(set(label_list)) == 1, (\nAssertionError: Different sub experiment labels participating in parallel, details:\n[]\n"}, {"_id": "683929a3f04f1fe4feb73f1b", "message": "Parallel Me<PERSON>ail\n--------------------------------------------------\nParalle<PERSON>-Child-Group-Exp(RamseyExtend) | default | Count-Identity(1)\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 248, in _validate_and_merge\n    assert len(xy_channel_list) == len(set(xy_channel_list)), (\nAssertionError: Found duplicate xy channels, details:\n[52, 29, 74, 28, 15, 52, 32, 15, 53, 7, 7, 33, 33, 34, 7, 7, 80, 25, 25, 34, 98, 8, 55, 85, 41, 4, 16, 40, 10, 99, 87, 12, 18, 18, 67, 97, 13, 14, 18, 18, 100, 28, 93, 21, 45, 27, 47, 100]\n"}, {"_id": "683929a6f04f1fe4feb73f7c", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(RamseyExtend) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 242, in _validate_and_merge\n    assert len(set(label_list)) == 1, (\nAssertionError: Different sub experiment labels participating in parallel, details:\n[]\n"}, {"_id": "683940dff04f1fe4feb73f9a", "message": "Parallel Me<PERSON>ail\n--------------------------------------------------\nParalle<PERSON>-Child-Group-Exp(RamseyExtend) | default | Count-Identity(1)\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 248, in _validate_and_merge\n    assert len(xy_channel_list) == len(set(xy_channel_list)), (\nAssertionError: Found duplicate xy channels, details:\n[52, 29, 74, 28, 15, 52, 53, 15, 7, 7, 32, 33, 33, 34, 7, 7, 80, 25, 25, 34, 55, 98, 8, 85, 41, 16, 4, 40, 99, 87, 12, 18, 10, 18, 67, 97, 13, 14, 18, 18, 100, 28, 93, 21, 100, 45, 27, 47]\n"}, {"_id": "683940e2f04f1fe4feb73ffb", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(RamseyExtend) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 242, in _validate_and_merge\n    assert len(set(label_list)) == 1, (\nAssertionError: Different sub experiment labels participating in parallel, details:\n[]\n"}, {"_id": "683940e4f04f1fe4feb73ffd", "message": "Parallel Me<PERSON>ail\n--------------------------------------------------\nParalle<PERSON>-Child-Group-Exp(RamseyExtend) | default | Count-Identity(1)\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 248, in _validate_and_merge\n    assert len(xy_channel_list) == len(set(xy_channel_list)), (\nAssertionError: Found duplicate xy channels, details:\n[52, 29, 74, 28, 15, 15, 52, 32, 53, 7, 7, 33, 33, 34, 7, 80, 25, 25, 7, 34, 55, 98, 4, 8, 85, 41, 16, 40, 10, 99, 87, 18, 18, 67, 12, 97, 13, 14, 18, 18, 100, 28, 93, 21, 100, 45, 27, 47]\n"}, {"_id": "683940e7f04f1fe4feb7405e", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(RamseyExtend) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 242, in _validate_and_merge\n    assert len(set(label_list)) == 1, (\nAssertionError: Different sub experiment labels participating in parallel, details:\n[]\n"}, {"_id": "683940e9f04f1fe4feb74060", "message": "Parallel Me<PERSON>ail\n--------------------------------------------------\nParalle<PERSON>-Child-Group-Exp(RamseyExtend) | default | Count-Identity(1)\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 248, in _validate_and_merge\n    assert len(xy_channel_list) == len(set(xy_channel_list)), (\nAssertionError: Found duplicate xy channels, details:\n[29, 74, 28, 52, 15, 15, 52, 32, 53, 7, 7, 33, 34, 33, 7, 7, 80, 25, 25, 55, 98, 4, 8, 34, 85, 41, 16, 40, 10, 99, 12, 18, 18, 87, 67, 97, 14, 18, 18, 13, 100, 28, 93, 21, 100, 45, 27, 47]\n"}, {"_id": "683940ecf04f1fe4feb740c1", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(RamseyExtend) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 242, in _validate_and_merge\n    assert len(set(label_list)) == 1, (\nAssertionError: Different sub experiment labels participating in parallel, details:\n[]\n"}, {"_id": "683940eef04f1fe4feb740c3", "message": "Parallel Me<PERSON>ail\n--------------------------------------------------\nParalle<PERSON>-Child-Group-Exp(RamseyExtend) | default | Count-Identity(1)\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 248, in _validate_and_merge\n    assert len(xy_channel_list) == len(set(xy_channel_list)), (\nAssertionError: Found duplicate xy channels, details:\n[52, 29, 74, 28, 15, 52, 32, 53, 15, 7, 7, 33, 33, 34, 7, 7, 80, 25, 25, 34, 55, 98, 4, 8, 85, 41, 16, 40, 10, 99, 87, 12, 18, 18, 67, 97, 13, 14, 18, 18, 100, 28, 93, 21, 100, 45, 27, 47]\n"}, {"_id": "683940f1f04f1fe4feb74124", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(RamseyExtend) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 242, in _validate_and_merge\n    assert len(set(label_list)) == 1, (\nAssertionError: Different sub experiment labels participating in parallel, details:\n[]\n"}, {"_id": "683940f3f04f1fe4feb74126", "message": "Parallel Me<PERSON>ail\n--------------------------------------------------\nParalle<PERSON>-Child-Group-Exp(RamseyExtend) | default | Count-Identity(1)\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 248, in _validate_and_merge\n    assert len(xy_channel_list) == len(set(xy_channel_list)), (\nAssertionError: Found duplicate xy channels, details:\n[52, 29, 74, 28, 15, 52, 32, 53, 15, 7, 7, 33, 33, 34, 7, 7, 80, 25, 25, 34, 98, 4, 8, 55, 85, 41, 16, 40, 10, 99, 87, 12, 18, 18, 67, 97, 13, 14, 18, 18, 100, 28, 93, 21, 100, 45, 27, 47]\n"}, {"_id": "683940f6f04f1fe4feb74187", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(RamseyExtend) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 242, in _validate_and_merge\n    assert len(set(label_list)) == 1, (\nAssertionError: Different sub experiment labels participating in parallel, details:\n[]\n"}, {"_id": "683961d8f04f1fe4feb741fb", "message": "Parallel Me<PERSON>ail\n--------------------------------------------------\nParalle<PERSON>-Child-Group-Exp(RamseyExtend) | default | Count-Identity(1)\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 248, in _validate_and_merge\n    assert len(xy_channel_list) == len(set(xy_channel_list)), (\nAssertionError: Found duplicate xy channels, details:\n[52, 29, 74, 28, 15, 15, 52, 32, 53, 7, 7, 33, 34, 7, 7, 33, 25, 25, 80, 34, 55, 98, 4, 8, 85, 41, 16, 40, 10, 99, 87, 12, 18, 18, 67, 97, 13, 14, 18, 18, 100, 28, 93, 21, 100, 45, 47, 27]\n"}, {"_id": "683961dbf04f1fe4feb7425c", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(RamseyExtend) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 242, in _validate_and_merge\n    assert len(set(label_list)) == 1, (\nAssertionError: Different sub experiment labels participating in parallel, details:\n[]\n"}, {"_id": "683961def04f1fe4feb7425e", "message": "Parallel Me<PERSON>ail\n--------------------------------------------------\nParalle<PERSON>-Child-Group-Exp(RamseyExtend) | default | Count-Identity(1)\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 248, in _validate_and_merge\n    assert len(xy_channel_list) == len(set(xy_channel_list)), (\nAssertionError: Found duplicate xy channels, details:\n[52, 29, 74, 28, 15, 15, 52, 32, 53, 7, 7, 33, 7, 7, 33, 34, 25, 25, 80, 34, 55, 98, 4, 8, 85, 41, 16, 40, 10, 99, 87, 12, 18, 18, 67, 97, 13, 14, 18, 18, 93, 21, 100, 45, 27, 100, 28, 47]\n"}, {"_id": "683961e1f04f1fe4feb742bf", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(RamseyExtend) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 242, in _validate_and_merge\n    assert len(set(label_list)) == 1, (\nAssertionError: Different sub experiment labels participating in parallel, details:\n[]\n"}, {"_id": "683961e4f04f1fe4feb742c1", "message": "Parallel Me<PERSON>ail\n--------------------------------------------------\nParalle<PERSON>-Child-Group-Exp(RamseyExtend) | default | Count-Identity(1)\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 248, in _validate_and_merge\n    assert len(xy_channel_list) == len(set(xy_channel_list)), (\nAssertionError: Found duplicate xy channels, details:\n[52, 29, 74, 28, 15, 15, 52, 32, 53, 7, 7, 33, 34, 7, 7, 33, 80, 25, 25, 34, 55, 98, 4, 8, 85, 41, 16, 40, 10, 99, 12, 87, 18, 18, 67, 97, 13, 14, 18, 18, 100, 28, 93, 21, 100, 45, 27, 47]\n"}, {"_id": "683961e7f04f1fe4feb74322", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(RamseyExtend) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 242, in _validate_and_merge\n    assert len(set(label_list)) == 1, (\nAssertionError: Different sub experiment labels participating in parallel, details:\n[]\n"}, {"_id": "68398972f04f1fe4feb74342", "message": "Parallel Me<PERSON>ail\n--------------------------------------------------\nParalle<PERSON>-Child-Group-Exp(RamseyExtend) | default | Count-Identity(1)\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 248, in _validate_and_merge\n    assert len(xy_channel_list) == len(set(xy_channel_list)), (\nAssertionError: Found duplicate xy channels, details:\n[52, 29, 74, 15, 15, 52, 32, 28, 53, 7, 33, 30, 7, 33, 7, 7, 34, 80, 25, 34, 55, 25, 98, 4, 8, 85, 41, 16, 40, 10, 99, 87, 12, 18, 18, 67, 97, 13, 14, 18, 18, 100, 28, 93, 21, 100, 45, 27, 47]\n"}, {"_id": "68398975f04f1fe4feb743a5", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(RamseyExtend) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 242, in _validate_and_merge\n    assert len(set(label_list)) == 1, (\nAssertionError: Different sub experiment labels participating in parallel, details:\n[]\n"}, {"_id": "68398977f04f1fe4feb743a6", "message": "Parallel Me<PERSON>ail\n--------------------------------------------------\nParalle<PERSON>-Child-Group-Exp(RamseyExtend) | default | Count-Identity(1)\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 248, in _validate_and_merge\n    assert len(xy_channel_list) == len(set(xy_channel_list)), (\nAssertionError: Found duplicate xy channels, details:\n[52, 29, 74, 28, 15, 52, 32, 53, 7, 15, 7, 33, 33, 34, 7, 7, 30, 80, 25, 34, 55, 98, 25, 4, 8, 85, 41, 16, 40, 10, 99, 87, 12, 18, 18, 67, 97, 13, 14, 18, 18, 100, 28, 93, 21, 45, 27, 47, 100]\n"}, {"_id": "6839897af04f1fe4feb74409", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(RamseyExtend) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 242, in _validate_and_merge\n    assert len(set(label_list)) == 1, (\nAssertionError: Different sub experiment labels participating in parallel, details:\n[]\n"}, {"_id": "6839897cf04f1fe4feb7440a", "message": "Parallel Me<PERSON>ail\n--------------------------------------------------\nParalle<PERSON>-Child-Group-Exp(RamseyExtend) | default | Count-Identity(1)\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 248, in _validate_and_merge\n    assert len(xy_channel_list) == len(set(xy_channel_list)), (\nAssertionError: Found duplicate xy channels, details:\n[52, 29, 74, 28, 15, 15, 52, 32, 53, 7, 7, 33, 33, 30, 34, 7, 7, 80, 25, 25, 34, 55, 98, 4, 8, 85, 41, 16, 40, 10, 99, 87, 12, 18, 67, 97, 18, 13, 14, 18, 100, 28, 18, 93, 21, 100, 45, 27, 47]\n"}, {"_id": "6839897ff04f1fe4feb7446d", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(RamseyExtend) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 242, in _validate_and_merge\n    assert len(set(label_list)) == 1, (\nAssertionError: Different sub experiment labels participating in parallel, details:\n[]\n"}, {"_id": "68398981f04f1fe4feb7446e", "message": "Parallel Me<PERSON>ail\n--------------------------------------------------\nParalle<PERSON>-Child-Group-Exp(RamseyExtend) | default | Count-Identity(1)\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 248, in _validate_and_merge\n    assert len(xy_channel_list) == len(set(xy_channel_list)), (\nAssertionError: Found duplicate xy channels, details:\n[29, 74, 28, 52, 15, 15, 52, 32, 53, 7, 7, 33, 33, 30, 34, 7, 7, 80, 25, 25, 34, 55, 4, 8, 85, 98, 41, 16, 40, 10, 99, 87, 12, 18, 18, 67, 97, 13, 14, 18, 100, 28, 18, 93, 21, 100, 45, 27, 47]\n"}, {"_id": "68398983f04f1fe4feb744d1", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(RamseyExtend) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 242, in _validate_and_merge\n    assert len(set(label_list)) == 1, (\nAssertionError: Different sub experiment labels participating in parallel, details:\n[]\n"}, {"_id": "68398986f04f1fe4feb744d2", "message": "Parallel Me<PERSON>ail\n--------------------------------------------------\nParalle<PERSON>-Child-Group-Exp(RamseyExtend) | default | Count-Identity(1)\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 248, in _validate_and_merge\n    assert len(xy_channel_list) == len(set(xy_channel_list)), (\nAssertionError: Found duplicate xy channels, details:\n[52, 29, 74, 28, 15, 15, 52, 32, 53, 7, 7, 33, 33, 30, 34, 7, 25, 25, 7, 34, 55, 80, 98, 4, 8, 85, 41, 16, 40, 10, 99, 87, 12, 18, 18, 67, 97, 13, 14, 18, 18, 100, 28, 93, 21, 100, 45, 27, 47]\n"}, {"_id": "68398988f04f1fe4feb74535", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(RamseyExtend) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 242, in _validate_and_merge\n    assert len(set(label_list)) == 1, (\nAssertionError: Different sub experiment labels participating in parallel, details:\n[]\n"}, {"_id": "6839898bf04f1fe4feb74536", "message": "Parallel Me<PERSON>ail\n--------------------------------------------------\nParalle<PERSON>-Child-Group-Exp(RamseyExtend) | default | Count-Identity(1)\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 248, in _validate_and_merge\n    assert len(xy_channel_list) == len(set(xy_channel_list)), (\nAssertionError: Found duplicate xy channels, details:\n[52, 29, 74, 28, 15, 15, 52, 32, 53, 7, 7, 33, 33, 30, 34, 7, 7, 80, 25, 25, 34, 55, 4, 8, 85, 41, 98, 16, 40, 10, 99, 87, 12, 18, 18, 67, 97, 13, 14, 18, 18, 100, 28, 93, 21, 100, 45, 27, 47]\n"}, {"_id": "6839898df04f1fe4feb74599", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(RamseyExtend) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 242, in _validate_and_merge\n    assert len(set(label_list)) == 1, (\nAssertionError: Different sub experiment labels participating in parallel, details:\n[]\n"}, {"_id": "6839b61bf04f1fe4feb745c2", "message": "Parallel Me<PERSON>ail\n--------------------------------------------------\nParalle<PERSON>-Child-Group-Exp(RamseyExtend) | default | Count-Identity(1)\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 248, in _validate_and_merge\n    assert len(xy_channel_list) == len(set(xy_channel_list)), (\nAssertionError: Found duplicate xy channels, details:\n[52, 29, 74, 28, 15, 52, 32, 53, 15, 7, 33, 33, 7, 30, 34, 7, 7, 80, 25, 25, 34, 55, 98, 4, 8, 41, 16, 40, 85, 10, 99, 87, 12, 18, 18, 67, 97, 13, 14, 18, 100, 18, 28, 93, 21, 100, 45, 27, 47]\n"}, {"_id": "6839b61ef04f1fe4feb74625", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(RamseyExtend) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 242, in _validate_and_merge\n    assert len(set(label_list)) == 1, (\nAssertionError: Different sub experiment labels participating in parallel, details:\n[]\n"}, {"_id": "6839b620f04f1fe4feb74626", "message": "Parallel Me<PERSON>ail\n--------------------------------------------------\nParalle<PERSON>-Child-Group-Exp(RamseyExtend) | default | Count-Identity(1)\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 248, in _validate_and_merge\n    assert len(xy_channel_list) == len(set(xy_channel_list)), (\nAssertionError: Found duplicate xy channels, details:\n[52, 29, 74, 28, 15, 15, 52, 32, 53, 7, 7, 33, 33, 30, 34, 7, 80, 25, 7, 25, 34, 55, 98, 4, 8, 85, 41, 16, 40, 99, 87, 18, 10, 18, 67, 12, 97, 13, 14, 18, 18, 100, 28, 93, 21, 100, 45, 27, 47]\n"}, {"_id": "6839b623f04f1fe4feb74689", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(RamseyExtend) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 242, in _validate_and_merge\n    assert len(set(label_list)) == 1, (\nAssertionError: Different sub experiment labels participating in parallel, details:\n[]\n"}, {"_id": "683fdc31f04f1fe4feb746b5", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParalle<PERSON>-Child-Group-Exp(RamseyExtend) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 248, in _validate_and_merge\n    assert len(xy_channel_list) == len(set(xy_channel_list)), (\nAssertionError: Found duplicate xy channels, details:\n[74, 28, 15, 52, 53, 7, 15, 7, 33, 33, 7, 80, 25, 25, 34, 85, 40, 10, 18, 18, 97, 13, 18, 18, 100, 93, 100, 45, 27, 47]\n"}, {"_id": "683fdc33f04f1fe4feb746f2", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(RamseyExtend) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 242, in _validate_and_merge\n    assert len(set(label_list)) == 1, (\nAssertionError: Different sub experiment labels participating in parallel, details:\n[]\n"}, {"_id": "683fdc34f04f1fe4feb746f3", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParalle<PERSON>-Child-Group-Exp(RamseyExtend) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 248, in _validate_and_merge\n    assert len(xy_channel_list) == len(set(xy_channel_list)), (\nAssertionError: Found duplicate xy channels, details:\n[74, 28, 52, 53, 7, 15, 7, 15, 33, 80, 33, 7, 25, 25, 34, 85, 40, 10, 18, 97, 13, 18, 18, 100, 18, 93, 100, 45, 27, 47]\n"}, {"_id": "683fdc37f04f1fe4feb74730", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(RamseyExtend) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 242, in _validate_and_merge\n    assert len(set(label_list)) == 1, (\nAssertionError: Different sub experiment labels participating in parallel, details:\n[]\n"}, {"_id": "683fee15f04f1fe4feb7473c", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParalle<PERSON>-Child-Group-Exp(RamseyExtend) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 248, in _validate_and_merge\n    assert len(xy_channel_list) == len(set(xy_channel_list)), (\nAssertionError: Found duplicate xy channels, details:\n[74, 28, 15, 15, 53, 7, 33, 33, 52, 7, 80, 7, 25, 25, 34, 85, 40, 10, 18, 18, 97, 13, 18, 18, 100, 100, 45, 27, 47, 93]\n"}, {"_id": "683fee17f04f1fe4feb74779", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(RamseyExtend) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 242, in _validate_and_merge\n    assert len(set(label_list)) == 1, (\nAssertionError: Different sub experiment labels participating in parallel, details:\n[]\n"}, {"_id": "683fee18f04f1fe4feb7477a", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParalle<PERSON>-Child-Group-Exp(RamseyExtend) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 248, in _validate_and_merge\n    assert len(xy_channel_list) == len(set(xy_channel_list)), (\nAssertionError: Found duplicate xy channels, details:\n[15, 15, 52, 53, 74, 7, 28, 7, 33, 33, 7, 80, 25, 25, 34, 85, 40, 10, 18, 18, 13, 18, 18, 100, 97, 93, 100, 45, 27, 47]\n"}, {"_id": "683fee1bf04f1fe4feb747b7", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(RamseyExtend) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 242, in _validate_and_merge\n    assert len(set(label_list)) == 1, (\nAssertionError: Different sub experiment labels participating in parallel, details:\n[]\n"}, {"_id": "683fee1cf04f1fe4feb747b8", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParalle<PERSON>-Child-Group-Exp(RamseyExtend) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 248, in _validate_and_merge\n    assert len(xy_channel_list) == len(set(xy_channel_list)), (\nAssertionError: Found duplicate xy channels, details:\n[74, 28, 15, 15, 52, 53, 7, 7, 33, 33, 7, 80, 25, 25, 34, 85, 40, 10, 18, 18, 97, 13, 18, 18, 100, 93, 100, 45, 27, 47]\n"}, {"_id": "683fee1ef04f1fe4feb747f5", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(RamseyExtend) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 242, in _validate_and_merge\n    assert len(set(label_list)) == 1, (\nAssertionError: Different sub experiment labels participating in parallel, details:\n[]\n"}, {"_id": "683fee20f04f1fe4feb747f6", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParalle<PERSON>-Child-Group-Exp(RamseyExtend) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 248, in _validate_and_merge\n    assert len(xy_channel_list) == len(set(xy_channel_list)), (\nAssertionError: Found duplicate xy channels, details:\n[74, 28, 15, 15, 52, 53, 7, 7, 33, 33, 7, 80, 25, 25, 34, 85, 40, 18, 18, 97, 13, 18, 10, 18, 100, 93, 100, 45, 27, 47]\n"}, {"_id": "683fee22f04f1fe4feb74833", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(RamseyExtend) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 242, in _validate_and_merge\n    assert len(set(label_list)) == 1, (\nAssertionError: Different sub experiment labels participating in parallel, details:\n[]\n"}, {"_id": "683fee23f04f1fe4feb74834", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParalle<PERSON>-Child-Group-Exp(RamseyExtend) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 248, in _validate_and_merge\n    assert len(xy_channel_list) == len(set(xy_channel_list)), (\nAssertionError: Found duplicate xy channels, details:\n[74, 28, 15, 15, 52, 53, 7, 7, 33, 33, 7, 80, 25, 25, 34, 85, 40, 10, 18, 18, 97, 13, 18, 18, 100, 93, 100, 45, 27, 47]\n"}, {"_id": "683fee25f04f1fe4feb74871", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(RamseyExtend) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"E:\\lzw\\code\\GitProject\\0.23.1\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 242, in _validate_and_merge\n    assert len(set(label_list)) == 1, (\nAssertionError: Different sub experiment labels participating in parallel, details:\n[]\n"}]