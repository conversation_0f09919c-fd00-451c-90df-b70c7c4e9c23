_id	message
687759021b17e2c4f88ce0fd	Experiment Crash
--------------------------------------------------
q6-QubitSpectrum-687758cd7f9480cbefd9f0d3
--------------------------------------------------
[Errno 22] Invalid argument
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\lzf\pyqcat-visage\pyQCat\experiments\_wrappers.py", line 73, in wrapper
    await func(*args, **kwargs)
  File "D:\code\lzf\pyqcat-visage\pyQCat\experiments\top_experiment_v1.py", line 610, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\lzf\pyqcat-visage\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\lzf\pyqcat-visage\pyQCat\concurrent\worker\analysis_interface.py", line 171, in top_experiment_analysis
    return base_analysis_process(
  File "D:\code\lzf\pyqcat-visage\pyQCat\concurrent\worker\analysis_interface.py", line 214, in base_analysis_process
    return run_analysis_process(
  File "D:\code\lzf\pyqcat-visage\pyQCat\concurrent\worker\analysis_interface.py", line 254, in run_analysis_process
    analysis_obj.run_analysis()
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\curve_analysis.py", line 422, in run_analysis
    self._extract_result(better_data_key)
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\library\qubit_spectrum_analysis.py", line 107, in _extract_result
    print(f"Background noise checked: SNR={self.quality.value} "
OSError: [Errno 22] Invalid argument

68775ab31b17e2c4f88ce100	Experiment Crash
--------------------------------------------------
q6-ReadoutFreqSSCalibrate-SingleShot_01(5-31)-probe_freq=7141.4-68775a867f9480cbefd9f111
--------------------------------------------------
[Errno 22] Invalid argument
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\lzf\pyqcat-visage\pyQCat\experiments\_wrappers.py", line 73, in wrapper
    await func(*args, **kwargs)
  File "D:\code\lzf\pyqcat-visage\pyQCat\experiments\top_experiment_v1.py", line 610, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\lzf\pyqcat-visage\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\lzf\pyqcat-visage\pyQCat\concurrent\worker\analysis_interface.py", line 171, in top_experiment_analysis
    return base_analysis_process(
  File "D:\code\lzf\pyqcat-visage\pyQCat\concurrent\worker\analysis_interface.py", line 214, in base_analysis_process
    return run_analysis_process(
  File "D:\code\lzf\pyqcat-visage\pyQCat\concurrent\worker\analysis_interface.py", line 254, in run_analysis_process
    analysis_obj.run_analysis()
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\library\single_shot_analysis.py", line 196, in run_analysis
    self._run_training()
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\library\single_shot_analysis.py", line 124, in _run_training
    dcm.train(n_multiple=self.options.n_multiple)
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\algorithms\iqprobability.py", line 646, in train
    self._distinguish(fit_label, outlier_index)
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\algorithms\iqprobability.py", line 1110, in _distinguish
    if self._check_repeat(label, true_label):
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\algorithms\iqprobability.py", line 1004, in _check_repeat
    print(
OSError: [Errno 22] Invalid argument

68775ab7814a76608c8ce004	Experiment Crash
--------------------------------------------------
q6-ReadoutFreqSSCalibrate-SingleShot_01(8-31)-probe_freq=7141.7-68775a867f9480cbefd9f114
--------------------------------------------------
[Errno 22] Invalid argument
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\lzf\pyqcat-visage\pyQCat\experiments\_wrappers.py", line 73, in wrapper
    await func(*args, **kwargs)
  File "D:\code\lzf\pyqcat-visage\pyQCat\experiments\top_experiment_v1.py", line 610, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\lzf\pyqcat-visage\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\lzf\pyqcat-visage\pyQCat\concurrent\worker\analysis_interface.py", line 171, in top_experiment_analysis
    return base_analysis_process(
  File "D:\code\lzf\pyqcat-visage\pyQCat\concurrent\worker\analysis_interface.py", line 214, in base_analysis_process
    return run_analysis_process(
  File "D:\code\lzf\pyqcat-visage\pyQCat\concurrent\worker\analysis_interface.py", line 254, in run_analysis_process
    analysis_obj.run_analysis()
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\library\single_shot_analysis.py", line 196, in run_analysis
    self._run_training()
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\library\single_shot_analysis.py", line 124, in _run_training
    dcm.train(n_multiple=self.options.n_multiple)
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\algorithms\iqprobability.py", line 646, in train
    self._distinguish(fit_label, outlier_index)
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\algorithms\iqprobability.py", line 1110, in _distinguish
    if self._check_repeat(label, true_label):
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\algorithms\iqprobability.py", line 1004, in _check_repeat
    print(
OSError: [Errno 22] Invalid argument

68775ab8814a76608c8ce005	Experiment Crash
--------------------------------------------------
q6-ReadoutFreqSSCalibrate-SingleShot_01(9-31)-probe_freq=7141.8-68775a867f9480cbefd9f115
--------------------------------------------------
[Errno 22] Invalid argument
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\lzf\pyqcat-visage\pyQCat\experiments\_wrappers.py", line 73, in wrapper
    await func(*args, **kwargs)
  File "D:\code\lzf\pyqcat-visage\pyQCat\experiments\top_experiment_v1.py", line 610, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\lzf\pyqcat-visage\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\lzf\pyqcat-visage\pyQCat\concurrent\worker\analysis_interface.py", line 171, in top_experiment_analysis
    return base_analysis_process(
  File "D:\code\lzf\pyqcat-visage\pyQCat\concurrent\worker\analysis_interface.py", line 214, in base_analysis_process
    return run_analysis_process(
  File "D:\code\lzf\pyqcat-visage\pyQCat\concurrent\worker\analysis_interface.py", line 254, in run_analysis_process
    analysis_obj.run_analysis()
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\library\single_shot_analysis.py", line 196, in run_analysis
    self._run_training()
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\library\single_shot_analysis.py", line 124, in _run_training
    dcm.train(n_multiple=self.options.n_multiple)
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\algorithms\iqprobability.py", line 646, in train
    self._distinguish(fit_label, outlier_index)
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\algorithms\iqprobability.py", line 1110, in _distinguish
    if self._check_repeat(label, true_label):
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\algorithms\iqprobability.py", line 1004, in _check_repeat
    print(
OSError: [Errno 22] Invalid argument

68775aba814a76608c8ce006	Experiment Crash
--------------------------------------------------
q6-ReadoutFreqSSCalibrate-SingleShot_01(10-31)-probe_freq=7141.9-68775a867f9480cbefd9f116
--------------------------------------------------
[Errno 22] Invalid argument
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\lzf\pyqcat-visage\pyQCat\experiments\_wrappers.py", line 73, in wrapper
    await func(*args, **kwargs)
  File "D:\code\lzf\pyqcat-visage\pyQCat\experiments\top_experiment_v1.py", line 610, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\lzf\pyqcat-visage\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\lzf\pyqcat-visage\pyQCat\concurrent\worker\analysis_interface.py", line 171, in top_experiment_analysis
    return base_analysis_process(
  File "D:\code\lzf\pyqcat-visage\pyQCat\concurrent\worker\analysis_interface.py", line 214, in base_analysis_process
    return run_analysis_process(
  File "D:\code\lzf\pyqcat-visage\pyQCat\concurrent\worker\analysis_interface.py", line 254, in run_analysis_process
    analysis_obj.run_analysis()
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\library\single_shot_analysis.py", line 196, in run_analysis
    self._run_training()
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\library\single_shot_analysis.py", line 124, in _run_training
    dcm.train(n_multiple=self.options.n_multiple)
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\algorithms\iqprobability.py", line 646, in train
    self._distinguish(fit_label, outlier_index)
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\algorithms\iqprobability.py", line 1110, in _distinguish
    if self._check_repeat(label, true_label):
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\algorithms\iqprobability.py", line 1004, in _check_repeat
    print(
OSError: [Errno 22] Invalid argument

68775abc1b17e2c4f88ce101	Experiment Crash
--------------------------------------------------
q6-ReadoutFreqSSCalibrate-SingleShot_01(11-31)-probe_freq=7142.0-68775a867f9480cbefd9f117
--------------------------------------------------
[Errno 22] Invalid argument
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\lzf\pyqcat-visage\pyQCat\experiments\_wrappers.py", line 73, in wrapper
    await func(*args, **kwargs)
  File "D:\code\lzf\pyqcat-visage\pyQCat\experiments\top_experiment_v1.py", line 610, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\lzf\pyqcat-visage\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\lzf\pyqcat-visage\pyQCat\concurrent\worker\analysis_interface.py", line 171, in top_experiment_analysis
    return base_analysis_process(
  File "D:\code\lzf\pyqcat-visage\pyQCat\concurrent\worker\analysis_interface.py", line 214, in base_analysis_process
    return run_analysis_process(
  File "D:\code\lzf\pyqcat-visage\pyQCat\concurrent\worker\analysis_interface.py", line 254, in run_analysis_process
    analysis_obj.run_analysis()
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\library\single_shot_analysis.py", line 196, in run_analysis
    self._run_training()
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\library\single_shot_analysis.py", line 124, in _run_training
    dcm.train(n_multiple=self.options.n_multiple)
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\algorithms\iqprobability.py", line 646, in train
    self._distinguish(fit_label, outlier_index)
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\algorithms\iqprobability.py", line 1110, in _distinguish
    if self._check_repeat(label, true_label):
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\algorithms\iqprobability.py", line 1004, in _check_repeat
    print(
OSError: [Errno 22] Invalid argument

68775abe814a76608c8ce007	Experiment Crash
--------------------------------------------------
q6-ReadoutFreqSSCalibrate-SingleShot_01(13-31)-probe_freq=7142.2-68775a867f9480cbefd9f119
--------------------------------------------------
[Errno 22] Invalid argument
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\lzf\pyqcat-visage\pyQCat\experiments\_wrappers.py", line 73, in wrapper
    await func(*args, **kwargs)
  File "D:\code\lzf\pyqcat-visage\pyQCat\experiments\top_experiment_v1.py", line 610, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\lzf\pyqcat-visage\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\lzf\pyqcat-visage\pyQCat\concurrent\worker\analysis_interface.py", line 171, in top_experiment_analysis
    return base_analysis_process(
  File "D:\code\lzf\pyqcat-visage\pyQCat\concurrent\worker\analysis_interface.py", line 214, in base_analysis_process
    return run_analysis_process(
  File "D:\code\lzf\pyqcat-visage\pyQCat\concurrent\worker\analysis_interface.py", line 254, in run_analysis_process
    analysis_obj.run_analysis()
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\library\single_shot_analysis.py", line 196, in run_analysis
    self._run_training()
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\library\single_shot_analysis.py", line 124, in _run_training
    dcm.train(n_multiple=self.options.n_multiple)
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\algorithms\iqprobability.py", line 646, in train
    self._distinguish(fit_label, outlier_index)
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\algorithms\iqprobability.py", line 1110, in _distinguish
    if self._check_repeat(label, true_label):
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\algorithms\iqprobability.py", line 1004, in _check_repeat
    print(
OSError: [Errno 22] Invalid argument

68775ac01b17e2c4f88ce102	Experiment Crash
--------------------------------------------------
q6-ReadoutFreqSSCalibrate-SingleShot_01(14-31)-probe_freq=7142.3-68775a867f9480cbefd9f11a
--------------------------------------------------
[Errno 22] Invalid argument
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\lzf\pyqcat-visage\pyQCat\experiments\_wrappers.py", line 73, in wrapper
    await func(*args, **kwargs)
  File "D:\code\lzf\pyqcat-visage\pyQCat\experiments\top_experiment_v1.py", line 610, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\lzf\pyqcat-visage\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\lzf\pyqcat-visage\pyQCat\concurrent\worker\analysis_interface.py", line 171, in top_experiment_analysis
    return base_analysis_process(
  File "D:\code\lzf\pyqcat-visage\pyQCat\concurrent\worker\analysis_interface.py", line 214, in base_analysis_process
    return run_analysis_process(
  File "D:\code\lzf\pyqcat-visage\pyQCat\concurrent\worker\analysis_interface.py", line 254, in run_analysis_process
    analysis_obj.run_analysis()
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\library\single_shot_analysis.py", line 196, in run_analysis
    self._run_training()
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\library\single_shot_analysis.py", line 124, in _run_training
    dcm.train(n_multiple=self.options.n_multiple)
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\algorithms\iqprobability.py", line 646, in train
    self._distinguish(fit_label, outlier_index)
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\algorithms\iqprobability.py", line 1110, in _distinguish
    if self._check_repeat(label, true_label):
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\algorithms\iqprobability.py", line 1004, in _check_repeat
    print(
OSError: [Errno 22] Invalid argument

68775acd1b17e2c4f88ce103	Experiment Crash
--------------------------------------------------
q6-ReadoutFreqSSCalibrate-SingleShot_01(12-31)-probe_freq=7142.1-68775a867f9480cbefd9f118
--------------------------------------------------
[Errno 22] Invalid argument
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\lzf\pyqcat-visage\pyQCat\experiments\_wrappers.py", line 73, in wrapper
    await func(*args, **kwargs)
  File "D:\code\lzf\pyqcat-visage\pyQCat\experiments\top_experiment_v1.py", line 610, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\lzf\pyqcat-visage\pyQCat\experiments\top_experiment_v1.py", line 512, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\lzf\pyqcat-visage\pyQCat\concurrent\worker\analysis_interface.py", line 171, in top_experiment_analysis
    return base_analysis_process(
  File "D:\code\lzf\pyqcat-visage\pyQCat\concurrent\worker\analysis_interface.py", line 214, in base_analysis_process
    return run_analysis_process(
  File "D:\code\lzf\pyqcat-visage\pyQCat\concurrent\worker\analysis_interface.py", line 254, in run_analysis_process
    analysis_obj.run_analysis()
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\library\single_shot_analysis.py", line 196, in run_analysis
    self._run_training()
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\library\single_shot_analysis.py", line 124, in _run_training
    dcm.train(n_multiple=self.options.n_multiple)
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\algorithms\iqprobability.py", line 646, in train
    self._distinguish(fit_label, outlier_index)
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\algorithms\iqprobability.py", line 1110, in _distinguish
    if self._check_repeat(label, true_label):
  File "D:\code\lzf\pyqcat-visage\pyQCat\analysis\algorithms\iqprobability.py", line 1004, in _check_repeat
    print(
OSError: [Errno 22] Invalid argument

68779cc9814a76608c8ce021	Experiment Crash
--------------------------------------------------
q5q6c5-6-CouplerSpectrumZAmpDynamic-68779c9f65bea5c7de595f64
--------------------------------------------------
[WinError 123] 文件名、目录名或卷标语法不正确。: 'Z:\\Y8\\yxy\\Reset_LRU_test\\CouplerSpectrumZAmpDynamic\\q5q6c5-6\x825-07-16\x019.55.24\\CouplerSpectrum'
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\yxy\naga\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 73, in wrapper
    await func(*args, **kwargs)
  File "D:\code\yxy\naga\pyqcat-apps\pyQCat\experiments\composite_experiment.py", line 513, in run_experiment
    self.run()
  File "D:\code\yxy\naga\pyqcat-apps\pyQCat\experiments\composite_experiment.py", line 198, in run
    self._validate_options()
  File "D:\code\yxy\naga\pyqcat-apps\pyQCat\experiments\base_experiment.py", line 931, in _validate_options
    paths = rocket_optimize(self.experiment_options.simulator_data_path)
  File "D:\code\yxy\naga\pyqcat-apps\pyQCat\tools\utilities.py", line 772, in rocket_optimize
    for f in os.listdir(root):
OSError: [WinError 123] 文件名、目录名或卷标语法不正确。: 'Z:\\Y8\\yxy\\Reset_LRU_test\\CouplerSpectrumZAmpDynamic\\q5q6c5-6\x825-07-16\x019.55.24\\CouplerSpectrum'

68848d01f4b30d9991446dfe	Experiment Fail
--------------------------------------------------
q79q85-FixedPointCalibration-RamseyExtend(1-unknow)-idx=0, z_amp=0.00949Vtarget_freq_osc=4384.044289_25MHz,-68848db250123b220cdfab0b
--------------------------------------------------
<Acq Data Tackle Error> | Traceback (most recent call last):
  File "E:\lzw\code\GitProject\0.23.2\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 695, in async_execute_loop
    progress = tq(self.sweep_points)
  File "C:\ProgramData\Miniconda3\lib\site-packages\tqdm\std.py", line 1109, in __init__
    self.refresh(lock_args=self.lock_args)
  File "C:\ProgramData\Miniconda3\lib\site-packages\tqdm\std.py", line 1361, in refresh
    self.display()
  File "C:\ProgramData\Miniconda3\lib\site-packages\tqdm\std.py", line 1509, in display
    self.sp(self.__str__() if msg is None else msg)
  File "C:\ProgramData\Miniconda3\lib\site-packages\tqdm\std.py", line 350, in print_status
    fp_write('\r' + s + (' ' * max(last_len[0] - len_s, 0)))
  File "C:\ProgramData\Miniconda3\lib\site-packages\tqdm\std.py", line 343, in fp_write
    fp.write(_unicode(s))
  File "C:\ProgramData\Miniconda3\lib\site-packages\tqdm\utils.py", line 145, in inner
    return func(*args, **kwargs)
OSError: [Errno 22] Invalid argument

68848d90f4b30d9991446dff	Experiment Fail
--------------------------------------------------
q79q85-FixedPointCalibration-RamseyExtend(1-unknow)-idx=0, z_amp=0.04713Vtarget_freq_osc=4414.044289_25MHz,-68848e2a50123b220cdfab25
--------------------------------------------------
<Acq Data Tackle Error> | Traceback (most recent call last):
  File "E:\lzw\code\GitProject\0.23.2\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 695, in async_execute_loop
    progress = tq(self.sweep_points)
  File "C:\ProgramData\Miniconda3\lib\site-packages\tqdm\std.py", line 1109, in __init__
    self.refresh(lock_args=self.lock_args)
  File "C:\ProgramData\Miniconda3\lib\site-packages\tqdm\std.py", line 1361, in refresh
    self.display()
  File "C:\ProgramData\Miniconda3\lib\site-packages\tqdm\std.py", line 1509, in display
    self.sp(self.__str__() if msg is None else msg)
  File "C:\ProgramData\Miniconda3\lib\site-packages\tqdm\std.py", line 350, in print_status
    fp_write('\r' + s + (' ' * max(last_len[0] - len_s, 0)))
  File "C:\ProgramData\Miniconda3\lib\site-packages\tqdm\std.py", line 343, in fp_write
    fp.write(_unicode(s))
  File "C:\ProgramData\Miniconda3\lib\site-packages\tqdm\utils.py", line 145, in inner
    return func(*args, **kwargs)
OSError: [Errno 22] Invalid argument

6888261ff4b30d9991446ec1	Experiment Crash
--------------------------------------------------
q12-CavityPowerScan-CavityFreqSpectrum(2-31)-power=-39dBm-688826aac1c9e18c7070e85b
--------------------------------------------------
[WinError 123] 文件名、目录名或卷标语法不正确。: ':'
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 605, in run_experiment
    self._validate_options()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 362, in _validate_options
    super()._validate_options()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\base_experiment.py", line 942, in _validate_options
    paths = rocket_optimize(self.experiment_options.simulator_data_path)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\tools\utilities.py", line 770, in rocket_optimize
    for f in os.listdir(root):
OSError: [WinError 123] 文件名、目录名或卷标语法不正确。: ':'

68882672f340c94b3b446ff1	Experiment Crash
--------------------------------------------------
q12-CavityPowerScan-CavityFreqSpectrum(2-31)-power=-39dBm-688826fdf13d5118f9313222
--------------------------------------------------
[WinError 123] 文件名、目录名或卷标语法不正确。: ':'
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 605, in run_experiment
    self._validate_options()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 362, in _validate_options
    super()._validate_options()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\base_experiment.py", line 942, in _validate_options
    paths = rocket_optimize(self.experiment_options.simulator_data_path)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\tools\utilities.py", line 770, in rocket_optimize
    for f in os.listdir(root):
OSError: [WinError 123] 文件名、目录名或卷标语法不正确。: ':'

6888272df340c94b3b447000	Experiment Crash
--------------------------------------------------
q12-CavityPowerScan-CavityFreqSpectrum(2-31)-power=-39dBm-688827b87cc95b5f160570ee
--------------------------------------------------
[WinError 123] 文件名、目录名或卷标语法不正确。: ':'
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 605, in run_experiment
    self._validate_options()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 362, in _validate_options
    super()._validate_options()
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\experiments\base_experiment.py", line 942, in _validate_options
    paths = rocket_optimize(self.experiment_options.simulator_data_path)
  File "D:\code\Y3-Online\Monitor\0.23.2\pyqcat-apps\pyQCat\tools\utilities.py", line 770, in rocket_optimize
    for f in os.listdir(root):
OSError: [WinError 123] 文件名、目录名或卷标语法不正确。: ':'
