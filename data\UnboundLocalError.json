[{"_id": {"$oid": "6888261ff340c94b3b446fd5"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-29 09:40:58.752292", "message": "Experiment Crash\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(1-31)-power=-40dBm-688826aac1c9e18c7070e85a\n--------------------------------------------------\nlocal variable 'visage_plot' referenced before assignment\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 614, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 164, in top_experiment_analysis\n    data_acquisition.execute_loop()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 898, in execute_loop\n    if visage_plot:\nUnboundLocalError: local variable 'visage_plot' referenced before assignment\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:38:39.905Z"}}, {"_id": {"$oid": "6888261ff340c94b3b446fd6"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-29 09:40:58.787143", "message": "Experiment Crash\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(3-31)-power=-38dBm-688826aac1c9e18c7070e85c\n--------------------------------------------------\nlocal variable 'visage_plot' referenced before assignment\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 614, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 164, in top_experiment_analysis\n    data_acquisition.execute_loop()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 898, in execute_loop\n    if visage_plot:\nUnboundLocalError: local variable 'visage_plot' referenced before assignment\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:38:39.954Z"}}, {"_id": {"$oid": "6888261ff4b30d9991446ec2"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-29 09:40:58.816066", "message": "Experiment Crash\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(4-31)-power=-37dBm-688826aac1c9e18c7070e85d\n--------------------------------------------------\nlocal variable 'visage_plot' referenced before assignment\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 614, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 164, in top_experiment_analysis\n    data_acquisition.execute_loop()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 898, in execute_loop\n    if visage_plot:\nUnboundLocalError: local variable 'visage_plot' referenced before assignment\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:38:39.984Z"}}, {"_id": {"$oid": "68882620f340c94b3b446fd7"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-29 09:40:58.847041", "message": "Experiment Crash\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(5-31)-power=-36dBm-688826aac1c9e18c7070e85e\n--------------------------------------------------\nlocal variable 'visage_plot' referenced before assignment\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 614, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 164, in top_experiment_analysis\n    data_acquisition.execute_loop()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 898, in execute_loop\n    if visage_plot:\nUnboundLocalError: local variable 'visage_plot' referenced before assignment\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:38:40.02Z"}}, {"_id": {"$oid": "68882620f4b30d9991446ec3"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-29 09:40:58.882887", "message": "Experiment Crash\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(6-31)-power=-35dBm-688826aac1c9e18c7070e85f\n--------------------------------------------------\nlocal variable 'visage_plot' referenced before assignment\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 614, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 164, in top_experiment_analysis\n    data_acquisition.execute_loop()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 898, in execute_loop\n    if visage_plot:\nUnboundLocalError: local variable 'visage_plot' referenced before assignment\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:38:40.042Z"}}, {"_id": {"$oid": "68882620f4b30d9991446ec4"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-29 09:40:58.922781", "message": "Experiment Crash\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(7-31)-power=-34dBm-688826aac1c9e18c7070e860\n--------------------------------------------------\nlocal variable 'visage_plot' referenced before assignment\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 614, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 164, in top_experiment_analysis\n    data_acquisition.execute_loop()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 898, in execute_loop\n    if visage_plot:\nUnboundLocalError: local variable 'visage_plot' referenced before assignment\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:38:40.106Z"}}, {"_id": {"$oid": "68882620f340c94b3b446fd8"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-29 09:40:58.961678", "message": "Experiment Crash\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(8-31)-power=-33dBm-688826aac1c9e18c7070e861\n--------------------------------------------------\nlocal variable 'visage_plot' referenced before assignment\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 614, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 164, in top_experiment_analysis\n    data_acquisition.execute_loop()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 898, in execute_loop\n    if visage_plot:\nUnboundLocalError: local variable 'visage_plot' referenced before assignment\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:38:40.113Z"}}, {"_id": {"$oid": "68882620f4b30d9991446ec5"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-29 09:40:58.994590", "message": "Experiment Crash\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(9-31)-power=-32dBm-688826aac1c9e18c7070e862\n--------------------------------------------------\nlocal variable 'visage_plot' referenced before assignment\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 614, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 164, in top_experiment_analysis\n    data_acquisition.execute_loop()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 898, in execute_loop\n    if visage_plot:\nUnboundLocalError: local variable 'visage_plot' referenced before assignment\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:38:40.15Z"}}, {"_id": {"$oid": "68882620f340c94b3b446fd9"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-29 09:40:59.027501", "message": "Experiment Crash\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(10-31)-power=-31dBm-688826aac1c9e18c7070e863\n--------------------------------------------------\nlocal variable 'visage_plot' referenced before assignment\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 614, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 164, in top_experiment_analysis\n    data_acquisition.execute_loop()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 898, in execute_loop\n    if visage_plot:\nUnboundLocalError: local variable 'visage_plot' referenced before assignment\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:38:40.178Z"}}, {"_id": {"$oid": "68882620f4b30d9991446ec6"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-29 09:40:59.069389", "message": "Experiment Crash\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(11-31)-power=-30dBm-688826abc1c9e18c7070e864\n--------------------------------------------------\nlocal variable 'visage_plot' referenced before assignment\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 614, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 164, in top_experiment_analysis\n    data_acquisition.execute_loop()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 898, in execute_loop\n    if visage_plot:\nUnboundLocalError: local variable 'visage_plot' referenced before assignment\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:38:40.222Z"}}, {"_id": {"$oid": "68882620f340c94b3b446fda"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-29 09:40:59.115266", "message": "Experiment Crash\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(12-31)-power=-29dBm-688826abc1c9e18c7070e865\n--------------------------------------------------\nlocal variable 'visage_plot' referenced before assignment\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 614, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 164, in top_experiment_analysis\n    data_acquisition.execute_loop()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 898, in execute_loop\n    if visage_plot:\nUnboundLocalError: local variable 'visage_plot' referenced before assignment\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:38:40.27Z"}}, {"_id": {"$oid": "68882620f340c94b3b446fdb"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-29 09:40:59.151226", "message": "Experiment Crash\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(13-31)-power=-28dBm-688826abc1c9e18c7070e866\n--------------------------------------------------\nlocal variable 'visage_plot' referenced before assignment\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 614, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 164, in top_experiment_analysis\n    data_acquisition.execute_loop()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 898, in execute_loop\n    if visage_plot:\nUnboundLocalError: local variable 'visage_plot' referenced before assignment\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:38:40.323Z"}}, {"_id": {"$oid": "68882620f340c94b3b446fdc"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-29 09:40:59.185771", "message": "Experiment Crash\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(14-31)-power=-27dBm-688826abc1c9e18c7070e867\n--------------------------------------------------\nlocal variable 'visage_plot' referenced before assignment\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 614, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 164, in top_experiment_analysis\n    data_acquisition.execute_loop()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 898, in execute_loop\n    if visage_plot:\nUnboundLocalError: local variable 'visage_plot' referenced before assignment\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:38:40.357Z"}}, {"_id": {"$oid": "68882620f340c94b3b446fdd"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-29 09:40:59.219680", "message": "Experiment Crash\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(15-31)-power=-26dBm-688826abc1c9e18c7070e868\n--------------------------------------------------\nlocal variable 'visage_plot' referenced before assignment\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 614, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 164, in top_experiment_analysis\n    data_acquisition.execute_loop()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 898, in execute_loop\n    if visage_plot:\nUnboundLocalError: local variable 'visage_plot' referenced before assignment\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:38:40.391Z"}}, {"_id": {"$oid": "68882620f340c94b3b446fde"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-29 09:40:59.253644", "message": "Experiment Crash\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(16-31)-power=-25dBm-688826abc1c9e18c7070e869\n--------------------------------------------------\nlocal variable 'visage_plot' referenced before assignment\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 614, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 164, in top_experiment_analysis\n    data_acquisition.execute_loop()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 898, in execute_loop\n    if visage_plot:\nUnboundLocalError: local variable 'visage_plot' referenced before assignment\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:38:40.426Z"}}, {"_id": {"$oid": "68882620f340c94b3b446fdf"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-29 09:40:59.287556", "message": "Experiment Crash\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(17-31)-power=-24dBm-688826abc1c9e18c7070e86a\n--------------------------------------------------\nlocal variable 'visage_plot' referenced before assignment\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 614, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 164, in top_experiment_analysis\n    data_acquisition.execute_loop()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 898, in execute_loop\n    if visage_plot:\nUnboundLocalError: local variable 'visage_plot' referenced before assignment\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:38:40.46Z"}}, {"_id": {"$oid": "68882620f340c94b3b446fe0"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-29 09:40:59.322462", "message": "Experiment Crash\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(18-31)-power=-23dBm-688826abc1c9e18c7070e86b\n--------------------------------------------------\nlocal variable 'visage_plot' referenced before assignment\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 614, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 164, in top_experiment_analysis\n    data_acquisition.execute_loop()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 898, in execute_loop\n    if visage_plot:\nUnboundLocalError: local variable 'visage_plot' referenced before assignment\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:38:40.494Z"}}, {"_id": {"$oid": "68882620f340c94b3b446fe1"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-29 09:40:59.363297", "message": "Experiment Crash\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(19-31)-power=-22dBm-688826abc1c9e18c7070e86c\n--------------------------------------------------\nlocal variable 'visage_plot' referenced before assignment\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 614, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 164, in top_experiment_analysis\n    data_acquisition.execute_loop()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 898, in execute_loop\n    if visage_plot:\nUnboundLocalError: local variable 'visage_plot' referenced before assignment\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:38:40.528Z"}}, {"_id": {"$oid": "68882620f340c94b3b446fe2"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-29 09:40:59.399258", "message": "Experiment Crash\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(20-31)-power=-21dBm-688826abc1c9e18c7070e86d\n--------------------------------------------------\nlocal variable 'visage_plot' referenced before assignment\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 614, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 164, in top_experiment_analysis\n    data_acquisition.execute_loop()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 898, in execute_loop\n    if visage_plot:\nUnboundLocalError: local variable 'visage_plot' referenced before assignment\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:38:40.572Z"}}, {"_id": {"$oid": "68882620f340c94b3b446fe3"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-29 09:40:59.434165", "message": "Experiment Crash\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(21-31)-power=-20dBm-688826abc1c9e18c7070e86e\n--------------------------------------------------\nlocal variable 'visage_plot' referenced before assignment\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 614, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 164, in top_experiment_analysis\n    data_acquisition.execute_loop()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 898, in execute_loop\n    if visage_plot:\nUnboundLocalError: local variable 'visage_plot' referenced before assignment\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:38:40.606Z"}}, {"_id": {"$oid": "68882620f340c94b3b446fe4"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-29 09:40:59.469072", "message": "Experiment Crash\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(22-31)-power=-19dBm-688826abc1c9e18c7070e86f\n--------------------------------------------------\nlocal variable 'visage_plot' referenced before assignment\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 614, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 164, in top_experiment_analysis\n    data_acquisition.execute_loop()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 898, in execute_loop\n    if visage_plot:\nUnboundLocalError: local variable 'visage_plot' referenced before assignment\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:38:40.641Z"}}, {"_id": {"$oid": "68882620f340c94b3b446fe5"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-29 09:40:59.504974", "message": "Experiment Crash\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(23-31)-power=-18dBm-688826abc1c9e18c7070e870\n--------------------------------------------------\nlocal variable 'visage_plot' referenced before assignment\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 614, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 164, in top_experiment_analysis\n    data_acquisition.execute_loop()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 898, in execute_loop\n    if visage_plot:\nUnboundLocalError: local variable 'visage_plot' referenced before assignment\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:38:40.675Z"}}, {"_id": {"$oid": "68882620f340c94b3b446fe6"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-29 09:40:59.538827", "message": "Experiment Crash\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(24-31)-power=-17dBm-688826abc1c9e18c7070e871\n--------------------------------------------------\nlocal variable 'visage_plot' referenced before assignment\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 614, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 164, in top_experiment_analysis\n    data_acquisition.execute_loop()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 898, in execute_loop\n    if visage_plot:\nUnboundLocalError: local variable 'visage_plot' referenced before assignment\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:38:40.71Z"}}, {"_id": {"$oid": "68882620f340c94b3b446fe7"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-29 09:40:59.598668", "message": "Experiment Crash\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(25-31)-power=-16dBm-688826abc1c9e18c7070e872\n--------------------------------------------------\nlocal variable 'visage_plot' referenced before assignment\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 614, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 164, in top_experiment_analysis\n    data_acquisition.execute_loop()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 898, in execute_loop\n    if visage_plot:\nUnboundLocalError: local variable 'visage_plot' referenced before assignment\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:38:40.748Z"}}, {"_id": {"$oid": "68882620f340c94b3b446fe8"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-29 09:40:59.632372", "message": "Experiment Crash\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(26-31)-power=-15dBm-688826abc1c9e18c7070e873\n--------------------------------------------------\nlocal variable 'visage_plot' referenced before assignment\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 614, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 164, in top_experiment_analysis\n    data_acquisition.execute_loop()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 898, in execute_loop\n    if visage_plot:\nUnboundLocalError: local variable 'visage_plot' referenced before assignment\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:38:40.782Z"}}, {"_id": {"$oid": "68882620f340c94b3b446fe9"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-29 09:40:59.667277", "message": "Experiment Crash\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(27-31)-power=-14dBm-688826abc1c9e18c7070e874\n--------------------------------------------------\nlocal variable 'visage_plot' referenced before assignment\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 614, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 164, in top_experiment_analysis\n    data_acquisition.execute_loop()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 898, in execute_loop\n    if visage_plot:\nUnboundLocalError: local variable 'visage_plot' referenced before assignment\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:38:40.821Z"}}, {"_id": {"$oid": "68882620f340c94b3b446fea"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-29 09:40:59.701129", "message": "Experiment Crash\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(28-31)-power=-13dBm-688826abc1c9e18c7070e875\n--------------------------------------------------\nlocal variable 'visage_plot' referenced before assignment\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 614, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 164, in top_experiment_analysis\n    data_acquisition.execute_loop()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 898, in execute_loop\n    if visage_plot:\nUnboundLocalError: local variable 'visage_plot' referenced before assignment\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:38:40.873Z"}}, {"_id": {"$oid": "68882620f340c94b3b446feb"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-29 09:40:59.736096", "message": "Experiment Crash\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(29-31)-power=-12dBm-688826abc1c9e18c7070e876\n--------------------------------------------------\nlocal variable 'visage_plot' referenced before assignment\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 614, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 164, in top_experiment_analysis\n    data_acquisition.execute_loop()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 898, in execute_loop\n    if visage_plot:\nUnboundLocalError: local variable 'visage_plot' referenced before assignment\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:38:40.912Z"}}, {"_id": {"$oid": "68882620f340c94b3b446fec"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-29 09:40:59.770947", "message": "Experiment Crash\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(30-31)-power=-11dBm-688826abc1c9e18c7070e877\n--------------------------------------------------\nlocal variable 'visage_plot' referenced before assignment\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 614, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 164, in top_experiment_analysis\n    data_acquisition.execute_loop()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 898, in execute_loop\n    if visage_plot:\nUnboundLocalError: local variable 'visage_plot' referenced before assignment\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:38:40.946Z"}}, {"_id": {"$oid": "68882620f340c94b3b446fed"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-29 09:40:59.803859", "message": "Experiment Crash\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(31-31)-power=-10dBm-688826abc1c9e18c7070e878\n--------------------------------------------------\nlocal variable 'visage_plot' referenced before assignment\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 614, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 164, in top_experiment_analysis\n    data_acquisition.execute_loop()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 898, in execute_loop\n    if visage_plot:\nUnboundLocalError: local variable 'visage_plot' referenced before assignment\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:38:40.98Z"}}]