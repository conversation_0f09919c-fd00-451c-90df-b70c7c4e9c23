#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MongoDB错误日志导出脚本
自动导出不同类型的错误日志到JSON文件
"""

import os
import subprocess
import json
from datetime import datetime

# MongoDB连接配置
MONGO_CONFIG = {
    "host": "***********",
    "port": "27017",
    "username": "bylz",
    "password": "fjsaoJOIjiojj28hjj",
    "auth_db": "admin",
    "database": "UserData",
    "collection": "ServerMessage",
}

# 截止时间
CUTOFF_TIME = "2025-08-12 14:20:09.733912"

# 错误类型列表
ERROR_TYPES = [
    "AttributeError",
    "Timeout",
    "AssertionError",
    "AcquisitionDataTackleError",
    "AnalysisFitDataError",
    "TypeError",
    "ValueError",
    "BaseQubitTypeError",
    "InstrumentInvalidValueError",
    "IndexError",
    "KeyboardInterrupt",
    "AcquisitionTaskStateError",
    "KeyError",
    "ExperimentOptionsError",
    "PulseError",
    "FileNotFoundError",
    "NoSectionError",
    "TimeoutError",
    "UnboundLocalError",
    "LinAlgError",
    "OSError",
    "RPCError",
    "WinError",
    "ExperimentError",
    "VisaIOError",
    "MemoryError",
    "_ArrayMemoryError",
    "ExperimentFieldError",
    "OverflowError",
    "ZeroDivisionError",
]


def create_output_directory():
    """创建输出目录"""
    output_dir = "./data"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建目录: {output_dir}")
    return output_dir


def create_query_file(error_type, output_dir):
    """创建查询文件"""
    query = {
        "message": {"$regex": error_type, "$options": "i"},
        "time": {"$lte": CUTOFF_TIME},
    }

    query_file = os.path.join(output_dir, f"query_{error_type}.json")
    with open(query_file, "w", encoding="utf-8") as f:
        json.dump(query, f, indent=2)

    return query_file


def build_mongoexport_command(query_file, output_file):
    """构建mongoexport命令"""
    cmd = [
        "mongoexport",
        "--host",
        MONGO_CONFIG["host"],
        "--port",
        MONGO_CONFIG["port"],
        "--username",
        MONGO_CONFIG["username"],
        "--password",
        MONGO_CONFIG["password"],
        "--authenticationDatabase",
        MONGO_CONFIG["auth_db"],
        "--db",
        MONGO_CONFIG["database"],
        "--collection",
        MONGO_CONFIG["collection"],
        "--jsonArray",
        "--pretty",
        "--queryFile",
        query_file,
        "--out",
        output_file,
    ]

    return cmd


def export_error_data(error_type, output_dir):
    """导出指定错误类型的数据"""
    output_file = os.path.join(output_dir, f"{error_type}.json")

    # 创建查询文件
    query_file = create_query_file(error_type, output_dir)

    # 构建命令
    cmd = build_mongoexport_command(query_file, output_file)

    try:
        print(f"正在导出 {error_type} 数据...")
        print(f"查询文件: {query_file}")
        print(f"输出文件: {output_file}")

        # 执行命令
        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            # 检查文件是否存在且有内容
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                if file_size > 2:  # 大于2字节（空JSON数组为[]，2字节）
                    print(f"✓ {error_type} 导出成功，文件大小: {file_size} 字节")

                    # 删除临时查询文件
                    if os.path.exists(query_file):
                        os.remove(query_file)
                else:
                    print(f"⚠ {error_type} 导出完成，但没有找到匹配的数据")
            else:
                print(f"✗ {error_type} 导出失败，文件未创建")
        else:
            print(f"✗ {error_type} 导出失败")
            print(f"错误信息: {result.stderr}")
            if result.stdout:
                print(f"输出信息: {result.stdout}")

    except Exception as e:
        print(f"✗ {error_type} 导出异常: {str(e)}")

    # 清理临时查询文件
    if os.path.exists(query_file):
        try:
            os.remove(query_file)
        except:
            pass


def main():
    """主函数"""
    print("=" * 60)
    print("MongoDB错误日志导出工具")
    print("=" * 60)
    print(f"MongoDB服务器: {MONGO_CONFIG['host']}:{MONGO_CONFIG['port']}")
    print(f"数据库: {MONGO_CONFIG['database']}")
    print(f"集合: {MONGO_CONFIG['collection']}")
    print(f"截止时间: {CUTOFF_TIME}")
    print(f"错误类型数量: {len(ERROR_TYPES)}")
    print("=" * 60)

    # 创建输出目录
    output_dir = create_output_directory()

    # 记录开始时间
    start_time = datetime.now()
    success_count = 0

    # 逐个导出错误类型
    for i, error_type in enumerate(ERROR_TYPES, 1):
        print(f"\n[{i}/{len(ERROR_TYPES)}] 处理: {error_type}")
        try:
            export_error_data(error_type, output_dir)
            success_count += 1
        except KeyboardInterrupt:
            print("\n用户中断操作")
            break
        except Exception as e:
            print(f"处理 {error_type} 时发生异常: {str(e)}")

    # 统计结果
    end_time = datetime.now()
    duration = end_time - start_time

    print("\n" + "=" * 60)
    print("导出完成!")
    print(f"总耗时: {duration}")
    print(f"成功处理: {success_count}/{len(ERROR_TYPES)}")
    print(f"输出目录: {os.path.abspath(output_dir)}")

    # 列出生成的文件
    if os.path.exists(output_dir):
        files = [
            f
            for f in os.listdir(output_dir)
            if f.endswith(".json") and not f.startswith("query_")
        ]
        if files:
            print(f"\n生成的文件 ({len(files)}个):")
            for file in sorted(files):
                file_path = os.path.join(output_dir, file)
                size = os.path.getsize(file_path)
                print(f"  - {file} ({size} 字节)")

        # 清理残留的查询文件
        query_files = [
            f
            for f in os.listdir(output_dir)
            if f.startswith("query_") and f.endswith(".json")
        ]
        for query_file in query_files:
            try:
                os.remove(os.path.join(output_dir, query_file))
            except:
                pass


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"\n程序执行出错: {str(e)}")

    input("\n按回车键退出...")
