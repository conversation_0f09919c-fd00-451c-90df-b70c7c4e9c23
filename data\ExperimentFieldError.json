[{"_id": {"$oid": "682d838fa77f347882573abe"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 15:44:00.882570", "message": "Experiment Fail\n--------------------------------------------------\nq61-ProcessTomographyV2\n--------------------------------------------------\n<Exp(ProcessTomographyV2) field error> | IQ discriminator is empty!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 602, in run_experiment\n    self._check_options()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\single\\error_quantification\\new_process_tomogrphy.py\", line 125, in _check_options\n    raise ExperimentFieldError(self, msg=\"IQ discriminator is empty!\")\npyQCat.errors.ExperimentFieldError: <Exp(ProcessTomographyV2) field error> | IQ discriminator is empty!\n", "version": "monster | 0.23.1.9 | B", "tackle": true, "create_time": {"$date": "2025-05-21T15:41:03.016Z"}}, {"_id": {"$oid": "68341148f04f1fe4feb7389b"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 15:00:41.265147", "message": "Experiment Fail\n--------------------------------------------------\nq1-T1Spectrum\n--------------------------------------------------\n<Exp(validate_ac_spectrum) field error> | qubit=Qubit(name=q1, coord=(0.0, 0.0)), spectrum_list=[[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]], freq=4600.0 is out of scope!\n--------------------------------------------------\nTrace<PERSON> (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 195, in run\n    self._check_options()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/parameters.py\", line 610, in _freq2amp_check_options\n    z_amp_list = freq_list_to_amp(freq_list, physical_bit, branch)\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/parameters.py\", line 748, in freq_list_to_amp\n    z_amp = freq_to_amp(physical_unit, float(freq), branch=branch)\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/tools/utilities.py\", line 1297, in freq_to_amp\n    raise ExperimentFieldError(\npyQCat.errors.ExperimentFieldError: <Exp(validate_ac_spectrum) field error> | qubit=Qubit(name=q1, coord=(0.0, 0.0)), spectrum_list=[[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]], freq=4600.0 is out of scope!\n", "version": "monster | 0.23.1.9 | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:59:20.374Z"}}]