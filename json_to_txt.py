import json
import os
import glob
from pathlib import Path


def convert_json_files(input_dir="./filtered_json_results", output_dir="./txt_data"):
    """
    处理指定目录下的所有JSON文件，将结果保存到txt_data目录

    参数:
        input_dir: JSON文件所在目录，默认为'./json_data'
        output_dir: 处理后TXT文件保存目录，默认为'./txt_data'
    """
    input_path = Path(input_dir)
    output_path = Path(output_dir)

    # 检查输入目录是否存在
    if not input_path.exists():
        print(f"错误: 输入目录 '{input_dir}' 不存在")
        return

    # 创建输出目录（如果不存在）
    output_path.mkdir(parents=True, exist_ok=True)

    # 批量获取所有JSON文件
    json_files = glob.glob(str(input_path / "*.json"))

    if not json_files:
        print(f"提示: 在 '{input_dir}' 目录下未找到任何JSON文件")
        return

    print(f"发现 {len(json_files)} 个JSON文件，开始处理...")

    # 批量处理文件
    for json_file in json_files:
        try:
            # 获取文件名（不含路径）
            filename = os.path.basename(json_file)
            # 构建输出文件名（替换扩展名为txt）
            txt_filename = os.path.splitext(filename)[0] + ".txt"
            # 完整输出路径
            txt_file = str(output_path / txt_filename)

            # 读取并解析JSON
            with open(json_file, "r", encoding="utf-8") as f:
                data = json.load(f)

            # 写入结果到TXT文件
            with open(txt_file, "w", encoding="utf-8") as out_f:
                for item in data:
                    # 提取oid和message
                    oid = item.get("_id", {}).get("$oid", "未知ID")
                    message = item.get("message", "无消息内容")

                    # 写入转换后的内容
                    out_f.write(f'ObjectId("{oid}")\t{message}\n\n')

            # 打印处理进度
            print(f"已处理: {filename} -> {txt_filename}")

        except json.JSONDecodeError:
            print(f"错误: 文件 '{filename}' 不是有效的JSON格式")
        except Exception as e:
            print(f"处理文件 '{filename}' 时出错: {str(e)}")

    print(f"所有文件处理完成! 结果保存在: {output_dir}")


if __name__ == "__main__":
    convert_json_files()
