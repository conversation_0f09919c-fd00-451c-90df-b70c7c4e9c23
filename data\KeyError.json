[{"_id": {"$oid": "682edf58f04f1fe4feb73698"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-22 16:27:54.593341", "message": "Experiment Fail\n--------------------------------------------------\nq97~c18-24-ZExp\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 490, in run\n    self._build_chimera_data()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 406, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 490, in run\n    self._build_chimera_data()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 406, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-22T16:24:56.829Z"}}, {"_id": {"$oid": "682edfcaf04f1fe4feb73699"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-22 16:29:48.647565", "message": "Experiment Fail\n--------------------------------------------------\nq97~c18-24-ZExp\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 490, in run\n    self._build_chimera_data()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 406, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 490, in run\n    self._build_chimera_data()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 406, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-22T16:26:50.886Z"}}, {"_id": {"$oid": "682ee01ef04f1fe4feb7369a"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-22 16:31:12.353159", "message": "Experiment Fail\n--------------------------------------------------\nq97~c18-24-ZExp\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 490, in run\n    self._build_chimera_data()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 406, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'c102-107'\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 490, in run\n    self._build_chimera_data()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 406, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'c102-107'\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-22T16:28:14.58Z"}}, {"_id": {"$oid": "685e6d74a0b5e01b01b32576"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:08:57.737113", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(4)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 404, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:48.279Z"}}, {"_id": {"$oid": "685e6d74952d8dd2e7b3242c"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:08:57.817898", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(7)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 404, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:48.357Z"}}, {"_id": {"$oid": "685e6d74a0b5e01b01b32577"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:08:57.849812", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(5)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 404, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:48.386Z"}}, {"_id": {"$oid": "685e6d74952d8dd2e7b3242d"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:08:57.861780", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 404, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:48.403Z"}}, {"_id": {"$oid": "685e6d74a0b5e01b01b32578"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:08:57.895690", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 404, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:48.436Z"}}, {"_id": {"$oid": "685e6d74952d8dd2e7b3242e"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:08:57.926606", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(10)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 404, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:48.453Z"}}, {"_id": {"$oid": "685e6d74a0b5e01b01b32579"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:08:57.942564", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(3)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 404, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:48.483Z"}}, {"_id": {"$oid": "685e6d74952d8dd2e7b3242f"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:08:58.076856", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(8)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 404, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:48.611Z"}}, {"_id": {"$oid": "685e6d74a0b5e01b01b3257a"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:08:58.110765", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(6)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 404, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:48.63Z"}}, {"_id": {"$oid": "685e6d74952d8dd2e7b32430"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:08:58.126722", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(9)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 404, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:48.673Z"}}, {"_id": {"$oid": "685e6d75952d8dd2e7b32431"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:08:59.177570", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(11)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 404, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:49.7Z"}}, {"_id": {"$oid": "685e6d75952d8dd2e7b32432"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:08:59.239406", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(12)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 404, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:49.765Z"}}, {"_id": {"$oid": "685e6d76a0b5e01b01b3257b"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:08:59.521650", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(13)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 404, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:50.049Z"}}, {"_id": {"$oid": "685e6d76952d8dd2e7b32433"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:08:59.582487", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(14)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 404, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:50.1Z"}}, {"_id": {"$oid": "685e6d76a0b5e01b01b3257c"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:08:59.596449", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(15)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 404, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:50.113Z"}}, {"_id": {"$oid": "685e6d76952d8dd2e7b32434"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:08:59.643324", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(16)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 404, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:50.156Z"}}, {"_id": {"$oid": "685e6d76a0b5e01b01b3257d"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:08:59.732088", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(17)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 404, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:50.257Z"}}, {"_id": {"$oid": "685e6d76952d8dd2e7b32435"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:08:59.756023", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(18)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 404, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:50.275Z"}}, {"_id": {"$oid": "685e6d76a0b5e01b01b3257e"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:08:59.801901", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(20)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 404, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:50.325Z"}}, {"_id": {"$oid": "685e6d76952d8dd2e7b32436"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:08:59.810877", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(19)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 404, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:50.329Z"}}, {"_id": {"$oid": "685e6d76a0b5e01b01b3257f"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:00.317198", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(21)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 404, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:50.831Z"}}, {"_id": {"$oid": "685e6d76952d8dd2e7b32437"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:00.379034", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(22)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 404, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:50.893Z"}}, {"_id": {"$oid": "685e6d77a0b5e01b01b32580"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:00.865732", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(23)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 404, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:51.384Z"}}, {"_id": {"$oid": "685e6d77a0b5e01b01b32581"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:01.098724", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(24)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 404, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:51.61Z"}}, {"_id": {"$oid": "685e6d77a0b5e01b01b32582"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:01.196022", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(25)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 404, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:51.707Z"}}, {"_id": {"$oid": "685e6d77952d8dd2e7b32438"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:01.237911", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(26)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 404, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:51.768Z"}}, {"_id": {"$oid": "685e6d77a0b5e01b01b32583"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:01.299745", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(28)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 404, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:51.815Z"}}, {"_id": {"$oid": "685e6d77952d8dd2e7b32439"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:01.272818", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(27)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 404, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:51.844Z"}}, {"_id": {"$oid": "685e6d77a0b5e01b01b32584"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:01.440370", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(29)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 404, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:51.946Z"}}, {"_id": {"$oid": "685e6d77952d8dd2e7b3243a"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:01.472285", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(30)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 404, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:51.982Z"}}, {"_id": {"$oid": "685e6d78952d8dd2e7b3243b"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:01.505197", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(31)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 404, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:52.033Z"}}, {"_id": {"$oid": "685e6d78952d8dd2e7b3243c"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:01.940200", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(32)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 404, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:52.451Z"}}, {"_id": {"$oid": "685e6d78a0b5e01b01b32585"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:02.033950", "message": "Parallel Merge <PERSON>ail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(33)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 404, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:52.54Z"}}, {"_id": {"$oid": "685e6d7ca0b5e01b01b3258b"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:05.477851", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:56.009Z"}}, {"_id": {"$oid": "685e6d7c952d8dd2e7b32441"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:05.600524", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(4)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:56.13Z"}}, {"_id": {"$oid": "685e6d7ca0b5e01b01b3258c"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:05.675323", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(9)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:56.208Z"}}, {"_id": {"$oid": "685e6d7c952d8dd2e7b32442"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:05.729180", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(5)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:56.259Z"}}, {"_id": {"$oid": "685e6d7ca0b5e01b01b3258d"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:05.739153", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(7)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:56.278Z"}}, {"_id": {"$oid": "685e6d7c952d8dd2e7b32443"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:05.752119", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:56.311Z"}}, {"_id": {"$oid": "685e6d7ca0b5e01b01b3258e"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:05.786028", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(3)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:56.334Z"}}, {"_id": {"$oid": "685e6d7c952d8dd2e7b32444"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:05.836891", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(8)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:56.355Z"}}, {"_id": {"$oid": "685e6d7ca0b5e01b01b3258f"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:05.847862", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(6)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:56.381Z"}}, {"_id": {"$oid": "685e6d7c952d8dd2e7b32445"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:05.865814", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(10)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:56.389Z"}}, {"_id": {"$oid": "685e6d7d952d8dd2e7b32446"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:06.881875", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(11)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:57.397Z"}}, {"_id": {"$oid": "685e6d7d952d8dd2e7b32447"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:07.350860", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(12)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:57.873Z"}}, {"_id": {"$oid": "685e6d7da0b5e01b01b32590"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:07.360834", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(13)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:57.875Z"}}, {"_id": {"$oid": "685e6d7d952d8dd2e7b32448"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:07.414689", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(14)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:57.934Z"}}, {"_id": {"$oid": "685e6d7da0b5e01b01b32591"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:07.473532", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(15)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:57.989Z"}}, {"_id": {"$oid": "685e6d7e952d8dd2e7b32449"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:07.510435", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(16)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:58.031Z"}}, {"_id": {"$oid": "685e6d7ea0b5e01b01b32592"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:07.660034", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(17)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:58.178Z"}}, {"_id": {"$oid": "685e6d7e952d8dd2e7b3244a"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:07.677988", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(18)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:58.2Z"}}, {"_id": {"$oid": "685e6d7ea0b5e01b01b32593"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:07.723864", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(19)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:58.247Z"}}, {"_id": {"$oid": "685e6d7e952d8dd2e7b3244b"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:07.744807", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(20)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:58.26Z"}}, {"_id": {"$oid": "685e6d7e952d8dd2e7b3244c"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:08.175828", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(21)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:58.694Z"}}, {"_id": {"$oid": "685e6d7fa0b5e01b01b32594"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:08.990671", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(22)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:59.507Z"}}, {"_id": {"$oid": "685e6d7f952d8dd2e7b3244d"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:09.002639", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(23)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:59.521Z"}}, {"_id": {"$oid": "685e6d7f952d8dd2e7b3244e"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:09.051509", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(24)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:59.566Z"}}, {"_id": {"$oid": "685e6d7fa0b5e01b01b32595"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:09.144261", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(25)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:59.664Z"}}, {"_id": {"$oid": "685e6d7fa0b5e01b01b32596"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:09.175179", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(26)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:59.714Z"}}, {"_id": {"$oid": "685e6d7f952d8dd2e7b3244f"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:09.224048", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(27)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:59.732Z"}}, {"_id": {"$oid": "685e6d7fa0b5e01b01b32597"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:09.316461", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(28)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:59.835Z"}}, {"_id": {"$oid": "685e6d7f952d8dd2e7b32450"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:09.319449", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(29)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:59.837Z"}}, {"_id": {"$oid": "685e6d7fa0b5e01b01b32598"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:09.348373", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(30)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:07:59.882Z"}}, {"_id": {"$oid": "685e6d80952d8dd2e7b32451"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:09.784398", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(31)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:00.292Z"}}, {"_id": {"$oid": "685e6d81952d8dd2e7b32452"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:10.597049", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(32)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:01.105Z"}}, {"_id": {"$oid": "685e6d81952d8dd2e7b32453"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:10.658884", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(33)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:01.172Z"}}, {"_id": {"$oid": "685e6d84952d8dd2e7b32458"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:14.217163", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(3)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:04.755Z"}}, {"_id": {"$oid": "685e6d84952d8dd2e7b32459"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:14.316896", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(4)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:04.848Z"}}, {"_id": {"$oid": "685e6d84a0b5e01b01b3259d"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:14.364769", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:04.896Z"}}, {"_id": {"$oid": "685e6d84952d8dd2e7b3245a"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:14.381724", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(9)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:04.908Z"}}, {"_id": {"$oid": "685e6d84a0b5e01b01b3259e"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:14.400672", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(7)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:04.943Z"}}, {"_id": {"$oid": "685e6d84952d8dd2e7b3245b"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:14.428598", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:04.961Z"}}, {"_id": {"$oid": "685e6d84a0b5e01b01b3259f"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:14.439569", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(5)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:04.978Z"}}, {"_id": {"$oid": "685e6d85952d8dd2e7b3245c"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:14.546517", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(10)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:05.078Z"}}, {"_id": {"$oid": "685e6d85a0b5e01b01b325a0"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:14.571450", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(8)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:05.109Z"}}, {"_id": {"$oid": "685e6d85952d8dd2e7b3245d"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:14.580426", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(6)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:05.126Z"}}, {"_id": {"$oid": "685e6d85a0b5e01b01b325a1"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:15.332416", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(11)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:05.843Z"}}, {"_id": {"$oid": "685e6d86a0b5e01b01b325a2"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:15.661292", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(12)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:06.174Z"}}, {"_id": {"$oid": "685e6d86952d8dd2e7b3245e"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:16.019982", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(13)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:06.543Z"}}, {"_id": {"$oid": "685e6d86a0b5e01b01b325a3"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:16.066856", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(14)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:06.586Z"}}, {"_id": {"$oid": "685e6d86952d8dd2e7b3245f"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:16.099768", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(16)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:06.608Z"}}, {"_id": {"$oid": "685e6d86a0b5e01b01b325a4"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:16.104753", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(15)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:06.641Z"}}, {"_id": {"$oid": "685e6d86952d8dd2e7b32460"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:16.114727", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(17)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:06.645Z"}}, {"_id": {"$oid": "685e6d86a0b5e01b01b325a5"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:16.236402", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(18)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:06.764Z"}}, {"_id": {"$oid": "685e6d86952d8dd2e7b32461"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:16.302226", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(19)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:06.823Z"}}, {"_id": {"$oid": "685e6d86a0b5e01b01b325a6"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:16.354088", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(20)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:06.871Z"}}, {"_id": {"$oid": "685e6d87a0b5e01b01b325a7"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:16.565174", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(21)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:07.077Z"}}, {"_id": {"$oid": "685e6d87952d8dd2e7b32462"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:16.932335", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(22)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:07.446Z"}}, {"_id": {"$oid": "685e6d87952d8dd2e7b32463"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:17.330616", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(23)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:07.843Z"}}, {"_id": {"$oid": "685e6d88952d8dd2e7b32464"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:17.628470", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(24)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:08.14Z"}}, {"_id": {"$oid": "685e6d88952d8dd2e7b32465"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:17.722219", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(25)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:08.229Z"}}, {"_id": {"$oid": "685e6d88952d8dd2e7b32466"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:17.784054", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(28)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:08.299Z"}}, {"_id": {"$oid": "685e6d88a0b5e01b01b325a8"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:17.800011", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(26)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:08.314Z"}}, {"_id": {"$oid": "685e6d88952d8dd2e7b32467"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:17.805997", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(27)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:08.336Z"}}, {"_id": {"$oid": "685e6d88a0b5e01b01b325a9"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:17.816966", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(29)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:08.363Z"}}, {"_id": {"$oid": "685e6d88a0b5e01b01b325aa"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:17.894758", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(30)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:08.401Z"}}, {"_id": {"$oid": "685e6d88a0b5e01b01b325ab"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:18.065303", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(31)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:08.574Z"}}, {"_id": {"$oid": "685e6d88a0b5e01b01b325ac"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:18.270753", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(32)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:08.776Z"}}, {"_id": {"$oid": "685e6d89952d8dd2e7b32468"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:18.768581", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(33)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:09.283Z"}}, {"_id": {"$oid": "685e6d8da0b5e01b01b325b2"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:22.686695", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:13.24Z"}}, {"_id": {"$oid": "685e6d8d952d8dd2e7b3246c"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:22.714621", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:13.254Z"}}, {"_id": {"$oid": "685e6d8da0b5e01b01b325b3"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:22.826323", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(3)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:13.359Z"}}, {"_id": {"$oid": "685e6d8d952d8dd2e7b3246d"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:22.950989", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(8)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:13.497Z"}}, {"_id": {"$oid": "685e6d8da0b5e01b01b325b4"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:22.966947", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(6)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:13.519Z"}}, {"_id": {"$oid": "685e6d8d952d8dd2e7b3246e"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:22.986893", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(10)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:13.549Z"}}, {"_id": {"$oid": "685e6d8da0b5e01b01b325b5"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:23.020804", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(4)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:13.575Z"}}, {"_id": {"$oid": "685e6d8d952d8dd2e7b3246f"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:23.037757", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(5)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:13.584Z"}}, {"_id": {"$oid": "685e6d8da0b5e01b01b325b6"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:23.067677", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(7)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:13.61Z"}}, {"_id": {"$oid": "685e6d8d952d8dd2e7b32470"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:23.082638", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(9)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:13.618Z"}}, {"_id": {"$oid": "685e6d8e952d8dd2e7b32471"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:24.004990", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(11)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:14.537Z"}}, {"_id": {"$oid": "685e6d8e952d8dd2e7b32472"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:24.069816", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(12)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:14.591Z"}}, {"_id": {"$oid": "685e6d8fa0b5e01b01b325b7"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:24.509640", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(14)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:15.024Z"}}, {"_id": {"$oid": "685e6d8f952d8dd2e7b32473"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:24.542553", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(13)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:15.066Z"}}, {"_id": {"$oid": "685e6d8fa0b5e01b01b325b8"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:24.708243", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(15)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:15.229Z"}}, {"_id": {"$oid": "685e6d8f952d8dd2e7b32474"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:24.768083", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(16)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:15.28Z"}}, {"_id": {"$oid": "685e6d8fa0b5e01b01b325b9"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:24.788030", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(17)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:15.302Z"}}, {"_id": {"$oid": "685e6d8f952d8dd2e7b32475"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:24.816952", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(18)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:15.344Z"}}, {"_id": {"$oid": "685e6d8fa0b5e01b01b325ba"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:24.847870", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(20)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:15.369Z"}}, {"_id": {"$oid": "685e6d8f952d8dd2e7b32476"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:24.869812", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(19)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:15.384Z"}}, {"_id": {"$oid": "685e6d8fa0b5e01b01b325bb"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:25.299831", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(21)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:15.815Z"}}, {"_id": {"$oid": "685e6d8f952d8dd2e7b32477"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:25.347703", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(22)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:15.859Z"}}, {"_id": {"$oid": "685e6d90a0b5e01b01b325bc"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:25.846496", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(23)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:16.36Z"}}, {"_id": {"$oid": "685e6d90952d8dd2e7b32478"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:25.898357", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(24)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:16.423Z"}}, {"_id": {"$oid": "685e6d90a0b5e01b01b325bd"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:25.941242", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(25)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:16.453Z"}}, {"_id": {"$oid": "685e6d90952d8dd2e7b32479"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:26.065909", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(26)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:16.575Z"}}, {"_id": {"$oid": "685e6d90a0b5e01b01b325be"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:26.180603", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(27)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:16.7Z"}}, {"_id": {"$oid": "685e6d90952d8dd2e7b3247a"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:26.331201", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(28)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:16.836Z"}}, {"_id": {"$oid": "685e6d90952d8dd2e7b3247b"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:26.408993", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(29)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:16.924Z"}}, {"_id": {"$oid": "685e6d90952d8dd2e7b3247c"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:26.472822", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(30)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:16.982Z"}}, {"_id": {"$oid": "685e6d91952d8dd2e7b3247d"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:26.643023", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(31)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:17.149Z"}}, {"_id": {"$oid": "685e6d91a0b5e01b01b325bf"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:26.658980", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(32)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:17.173Z"}}, {"_id": {"$oid": "685e6d91952d8dd2e7b3247e"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:26.863434", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(33)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:17.378Z"}}, {"_id": {"$oid": "685e6d95952d8dd2e7b32484"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:30.774797", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:21.316Z"}}, {"_id": {"$oid": "685e6d95a0b5e01b01b325c4"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:30.792748", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:21.333Z"}}, {"_id": {"$oid": "685e6d95952d8dd2e7b32485"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:30.815687", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(4)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:21.37Z"}}, {"_id": {"$oid": "685e6d95a0b5e01b01b325c5"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:30.893479", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(5)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:21.419Z"}}, {"_id": {"$oid": "685e6d95952d8dd2e7b32486"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:30.938359", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(7)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:21.453Z"}}, {"_id": {"$oid": "685e6d95a0b5e01b01b325c6"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:30.962295", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(3)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:21.483Z"}}, {"_id": {"$oid": "685e6d95952d8dd2e7b32487"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:31.034102", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(10)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:21.553Z"}}, {"_id": {"$oid": "685e6d95a0b5e01b01b325c7"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:31.060033", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(8)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:21.574Z"}}, {"_id": {"$oid": "685e6d95952d8dd2e7b32488"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:31.069010", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(6)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:21.606Z"}}, {"_id": {"$oid": "685e6d95a0b5e01b01b325c8"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:31.153062", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(9)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:21.678Z"}}, {"_id": {"$oid": "685e6d96a0b5e01b01b325c9"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:31.893392", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(11)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:22.407Z"}}, {"_id": {"$oid": "685e6d96952d8dd2e7b32489"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:31.928299", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(12)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:22.461Z"}}, {"_id": {"$oid": "685e6d97952d8dd2e7b3248a"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:32.490307", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(13)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:23.014Z"}}, {"_id": {"$oid": "685e6d97a0b5e01b01b325ca"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:32.518232", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(14)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:23.042Z"}}, {"_id": {"$oid": "685e6d97952d8dd2e7b3248b"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:32.552141", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(15)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:23.072Z"}}, {"_id": {"$oid": "685e6d97a0b5e01b01b325cb"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:32.598019", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(17)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:23.114Z"}}, {"_id": {"$oid": "685e6d97a0b5e01b01b325cc"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:32.600013", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(16)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:23.156Z"}}, {"_id": {"$oid": "685e6d97a0b5e01b01b325cd"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:32.720339", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(18)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:23.24Z"}}, {"_id": {"$oid": "685e6d97952d8dd2e7b3248c"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:32.738291", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(19)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:23.259Z"}}, {"_id": {"$oid": "685e6d97a0b5e01b01b325ce"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:32.769209", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(20)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:23.291Z"}}, {"_id": {"$oid": "685e6d97a0b5e01b01b325cf"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:32.988622", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(21)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:23.521Z"}}, {"_id": {"$oid": "685e6d97a0b5e01b01b325d0"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:33.364617", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(22)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:23.879Z"}}, {"_id": {"$oid": "685e6d98a0b5e01b01b325d1"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:33.550121", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(23)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:24.065Z"}}, {"_id": {"$oid": "685e6d98a0b5e01b01b325d2"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:33.644867", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(24)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:24.155Z"}}, {"_id": {"$oid": "685e6d98952d8dd2e7b3248d"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:33.647860", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(25)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:24.164Z"}}, {"_id": {"$oid": "685e6d98a0b5e01b01b325d3"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:33.816540", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(26)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:24.332Z"}}, {"_id": {"$oid": "685e6d98952d8dd2e7b3248e"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:33.973121", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(27)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:24.488Z"}}, {"_id": {"$oid": "685e6d98952d8dd2e7b3248f"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:34.008028", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(28)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:24.536Z"}}, {"_id": {"$oid": "685e6d98952d8dd2e7b32490"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:34.223098", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(29)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:24.731Z"}}, {"_id": {"$oid": "685e6d98952d8dd2e7b32491"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:34.283934", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(30)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:24.802Z"}}, {"_id": {"$oid": "685e6d98a0b5e01b01b325d4"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:34.424559", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(31)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:24.932Z"}}, {"_id": {"$oid": "685e6d98952d8dd2e7b32492"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:34.440516", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(32)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:24.945Z"}}, {"_id": {"$oid": "685e6d99952d8dd2e7b32493"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:34.550224", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(33)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:25.057Z"}}, {"_id": {"$oid": "685e6d9c952d8dd2e7b3249a"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:38.367556", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:28.91Z"}}, {"_id": {"$oid": "685e6d9ca0b5e01b01b325d8"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:38.428393", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(4)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:28.959Z"}}, {"_id": {"$oid": "685e6d9d952d8dd2e7b3249b"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:38.567023", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(7)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:29.092Z"}}, {"_id": {"$oid": "685e6d9da0b5e01b01b325d9"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:38.582980", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(5)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:29.111Z"}}, {"_id": {"$oid": "685e6d9d952d8dd2e7b3249c"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:38.616890", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(3)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:29.15Z"}}, {"_id": {"$oid": "685e6d9da0b5e01b01b325da"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:38.645811", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:29.172Z"}}, {"_id": {"$oid": "685e6d9d952d8dd2e7b3249d"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:38.736570", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(8)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:29.267Z"}}, {"_id": {"$oid": "685e6d9da0b5e01b01b325db"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:38.760505", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(10)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:29.301Z"}}, {"_id": {"$oid": "685e6d9d952d8dd2e7b3249e"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:38.803391", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(9)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:29.321Z"}}, {"_id": {"$oid": "685e6d9d952d8dd2e7b3249f"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:38.770479", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(6)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:29.321Z"}}, {"_id": {"$oid": "685e6d9e952d8dd2e7b324a0"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:39.565504", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(11)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:30.085Z"}}, {"_id": {"$oid": "685e6d9e952d8dd2e7b324a1"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:39.788907", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(12)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:30.323Z"}}, {"_id": {"$oid": "685e6d9e952d8dd2e7b324a2"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:39.832442", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(13)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:30.376Z"}}, {"_id": {"$oid": "685e6d9ea0b5e01b01b325dc"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:40.081776", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(14)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:30.601Z"}}, {"_id": {"$oid": "685e6d9ea0b5e01b01b325dd"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:40.299435", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(15)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:30.817Z"}}, {"_id": {"$oid": "685e6d9e952d8dd2e7b324a3"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:40.299435", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(16)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:30.82Z"}}, {"_id": {"$oid": "685e6d9ea0b5e01b01b325de"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:40.349304", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(17)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:30.863Z"}}, {"_id": {"$oid": "685e6d9e952d8dd2e7b324a4"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:40.457015", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(18)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:30.987Z"}}, {"_id": {"$oid": "685e6d9f952d8dd2e7b324a5"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:40.519847", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(20)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:31.042Z"}}, {"_id": {"$oid": "685e6d9f952d8dd2e7b324a6"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:40.535804", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(19)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:31.079Z"}}, {"_id": {"$oid": "685e6d9f952d8dd2e7b324a7"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:40.987758", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(21)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:31.507Z"}}, {"_id": {"$oid": "685e6d9f952d8dd2e7b324a8"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:41.176254", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(22)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:31.695Z"}}, {"_id": {"$oid": "685e6d9fa0b5e01b01b325df"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:41.206173", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(23)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:31.724Z"}}, {"_id": {"$oid": "685e6d9fa0b5e01b01b325e0"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:41.349303", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(24)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:31.87Z"}}, {"_id": {"$oid": "685e6d9f952d8dd2e7b324a9"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:41.428092", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(25)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:31.935Z"}}, {"_id": {"$oid": "685e6da0952d8dd2e7b324aa"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:41.752878", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(26)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:32.269Z"}}, {"_id": {"$oid": "685e6da0a0b5e01b01b325e1"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:41.971951", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(27)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:32.479Z"}}, {"_id": {"$oid": "685e6da0952d8dd2e7b324ab"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:42.002869", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(29)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:32.516Z"}}, {"_id": {"$oid": "685e6da0a0b5e01b01b325e2"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:41.986911", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(28)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:32.534Z"}}, {"_id": {"$oid": "685e6da0a0b5e01b01b325e3"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:42.033786", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(30)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:32.574Z"}}, {"_id": {"$oid": "685e6da0952d8dd2e7b324ac"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:42.113573", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(31)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:32.618Z"}}, {"_id": {"$oid": "685e6da0952d8dd2e7b324ad"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:42.160447", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(32)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:32.667Z"}}, {"_id": {"$oid": "685e6da0952d8dd2e7b324ae"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:42.393880", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(33)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:32.909Z"}}, {"_id": {"$oid": "685e6da4a0b5e01b01b325e9"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:46.301976", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(4)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:36.83Z"}}, {"_id": {"$oid": "685e6da4952d8dd2e7b324b3"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:46.315938", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:36.841Z"}}, {"_id": {"$oid": "685e6da4952d8dd2e7b324b4"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:46.444594", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(5)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:36.967Z"}}, {"_id": {"$oid": "685e6da4a0b5e01b01b325ea"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:46.458557", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(7)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:36.978Z"}}, {"_id": {"$oid": "685e6da5a0b5e01b01b325eb"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:46.470525", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(3)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:37.024Z"}}, {"_id": {"$oid": "685e6da5952d8dd2e7b324b5"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:46.481496", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(9)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:37.024Z"}}, {"_id": {"$oid": "685e6da5952d8dd2e7b324b6"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:46.550312", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:37.079Z"}}, {"_id": {"$oid": "685e6da5a0b5e01b01b325ec"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:46.626109", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(10)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:37.152Z"}}, {"_id": {"$oid": "685e6da5952d8dd2e7b324b7"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:46.640072", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(6)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:37.156Z"}}, {"_id": {"$oid": "685e6da5a0b5e01b01b325ed"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:46.663011", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(8)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:37.226Z"}}, {"_id": {"$oid": "685e6da5a0b5e01b01b325ee"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:47.456540", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(11)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:37.972Z"}}, {"_id": {"$oid": "685e6da5952d8dd2e7b324b8"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:47.474492", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(12)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:37.993Z"}}, {"_id": {"$oid": "685e6da6952d8dd2e7b324b9"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:47.535330", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(13)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:38.047Z"}}, {"_id": {"$oid": "685e6da6a0b5e01b01b325ef"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:47.711858", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(14)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:38.237Z"}}, {"_id": {"$oid": "685e6da6a0b5e01b01b325f0"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:47.991227", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(15)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:38.506Z"}}, {"_id": {"$oid": "685e6da6952d8dd2e7b324ba"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:48.035430", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(16)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:38.544Z"}}, {"_id": {"$oid": "685e6da6952d8dd2e7b324bb"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:48.195003", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(17)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:38.718Z"}}, {"_id": {"$oid": "685e6da6a0b5e01b01b325f1"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:48.222573", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(18)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:38.734Z"}}, {"_id": {"$oid": "685e6da6952d8dd2e7b324bc"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:48.287400", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(19)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:38.794Z"}}, {"_id": {"$oid": "685e6da6952d8dd2e7b324bd"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:48.395112", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(20)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:38.916Z"}}, {"_id": {"$oid": "685e6da7a0b5e01b01b325f2"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:48.488862", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(21)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:39.002Z"}}, {"_id": {"$oid": "685e6da7952d8dd2e7b324be"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:48.519780", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(22)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:39.027Z"}}, {"_id": {"$oid": "685e6da7a0b5e01b01b325f3"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:48.846904", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(23)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:39.356Z"}}, {"_id": {"$oid": "685e6da7a0b5e01b01b325f4"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:49.036047", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(24)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:39.546Z"}}, {"_id": {"$oid": "685e6da7952d8dd2e7b324bf"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:49.158720", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(25)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:39.676Z"}}, {"_id": {"$oid": "685e6da7a0b5e01b01b325f5"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:49.252469", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(26)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:39.78Z"}}, {"_id": {"$oid": "685e6da7a0b5e01b01b325f6"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:49.300340", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(27)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:39.829Z"}}, {"_id": {"$oid": "685e6da7a0b5e01b01b325f7"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:49.317295", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(28)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:39.866Z"}}, {"_id": {"$oid": "685e6da8952d8dd2e7b324c0"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:49.643424", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(29)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:40.152Z"}}, {"_id": {"$oid": "685e6da8a0b5e01b01b325f8"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:49.846192", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(30)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:40.357Z"}}, {"_id": {"$oid": "685e6da8a0b5e01b01b325f9"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:49.878108", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(31)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:40.408Z"}}, {"_id": {"$oid": "685e6da8952d8dd2e7b324c1"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:49.924982", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(32)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:40.439Z"}}, {"_id": {"$oid": "685e6da8a0b5e01b01b325fa"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:50.018732", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(33)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:40.535Z"}}, {"_id": {"$oid": "685e6daca0b5e01b01b325ff"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:53.832697", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(4)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:44.366Z"}}, {"_id": {"$oid": "685e6dac952d8dd2e7b324c7"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:53.846660", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(6)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:44.377Z"}}, {"_id": {"$oid": "685e6daca0b5e01b01b32600"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:53.911486", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(9)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:44.435Z"}}, {"_id": {"$oid": "685e6dac952d8dd2e7b324c8"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:53.986286", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(2)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:44.521Z"}}, {"_id": {"$oid": "685e6daca0b5e01b01b32601"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:53.999251", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(7)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:44.538Z"}}, {"_id": {"$oid": "685e6dac952d8dd2e7b324c9"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:54.014211", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:44.57Z"}}, {"_id": {"$oid": "685e6daca0b5e01b01b32602"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:54.044131", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(5)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:44.593Z"}}, {"_id": {"$oid": "685e6dac952d8dd2e7b324ca"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:54.052109", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(3)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:44.605Z"}}, {"_id": {"$oid": "685e6daca0b5e01b01b32603"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:54.068067", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(10)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:44.631Z"}}, {"_id": {"$oid": "685e6dac952d8dd2e7b324cb"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:54.085077", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(8)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:44.64Z"}}, {"_id": {"$oid": "685e6dada0b5e01b01b32604"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:55.021048", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(11)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:45.548Z"}}, {"_id": {"$oid": "685e6dada0b5e01b01b32605"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:55.255556", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(12)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:45.799Z"}}, {"_id": {"$oid": "685e6dae952d8dd2e7b324cc"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:55.573705", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(13)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:46.097Z"}}, {"_id": {"$oid": "685e6daea0b5e01b01b32606"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:55.592655", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(14)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:46.12Z"}}, {"_id": {"$oid": "685e6daea0b5e01b01b32607"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:55.706624", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(15)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:46.219Z"}}, {"_id": {"$oid": "685e6dae952d8dd2e7b324cd"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:55.740533", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(16)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:46.257Z"}}, {"_id": {"$oid": "685e6daea0b5e01b01b32608"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:55.770453", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(17)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:46.28Z"}}, {"_id": {"$oid": "685e6dae952d8dd2e7b324ce"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:55.791397", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(18)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:46.314Z"}}, {"_id": {"$oid": "685e6daea0b5e01b01b32609"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:55.832942", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(19)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:46.359Z"}}, {"_id": {"$oid": "685e6dae952d8dd2e7b324cf"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:55.872835", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(20)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:46.395Z"}}, {"_id": {"$oid": "685e6dae952d8dd2e7b324d0"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:56.394836", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(21)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:46.914Z"}}, {"_id": {"$oid": "685e6dafa0b5e01b01b3260a"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:56.721962", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(22)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:47.232Z"}}, {"_id": {"$oid": "685e6daf952d8dd2e7b324d1"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:56.977414", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(23)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:47.488Z"}}, {"_id": {"$oid": "685e6daf952d8dd2e7b324d2"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:57.255314", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(24)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:47.777Z"}}, {"_id": {"$oid": "685e6daf952d8dd2e7b324d3"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:57.268280", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(25)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:47.832Z"}}, {"_id": {"$oid": "685e6daf952d8dd2e7b324d4"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:57.364023", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(26)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:47.875Z"}}, {"_id": {"$oid": "685e6daf952d8dd2e7b324d5"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:57.408903", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(27)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:47.915Z"}}, {"_id": {"$oid": "685e6daf952d8dd2e7b324d6"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:57.424373", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(28)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:47.95Z"}}, {"_id": {"$oid": "685e6dafa0b5e01b01b3260b"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:57.441327", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(29)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:47.959Z"}}, {"_id": {"$oid": "685e6daf952d8dd2e7b324d7"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:57.487204", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(30)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:47.997Z"}}, {"_id": {"$oid": "685e6db0a0b5e01b01b3260c"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:57.768452", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(31)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:48.277Z"}}, {"_id": {"$oid": "685e6db0a0b5e01b01b3260d"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:58.050223", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(32)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:48.556Z"}}, {"_id": {"$oid": "685e6db0a0b5e01b01b3260e"}, "username": "<PERSON><PERSON><PERSON><PERSON>", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-27 18:09:58.190847", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(33)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\shenxiang\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-06-27T18:08:48.701Z"}}, {"_id": {"$oid": "68620bf19c3e8a30651e9823"}, "username": "wxy-Y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-06-30 12:02:05.000037", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(SingleShot_01) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\Code\\wxy\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\Code\\wxy\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | 0.23.2 | B", "create_time": {"$date": "2025-06-30T12:00:49.545Z"}}, {"_id": {"$oid": "68651249a47f89e2f61ea526"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-02 19:06:24.176176", "message": "Experiment Crash\n--------------------------------------------------\nCircuit-CircuitComposite-Circuit(1-1)-None-686512b080915fdf628a86d6\n--------------------------------------------------\n'q'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 608, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 541, in _async_compile\n    experiment_file: ExperimentFile = build_experiment_message(exp_env)\n                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 142, in build_experiment_message\n    exp_env.set_xy_pulses(builder)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/single/undetermine/circuit.py\", line 603, in set_xy_pulses\n    builder.play_pulse(\"XY\", unit_map[qubit_name], pulse)\n                             ~~~~~~~~^^^^^^^^^^^^\nKeyError: 'q'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-02T19:04:41.422Z"}}, {"_id": {"$oid": "68687ebe9c3e8a30651ea737"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:25:40.859829", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:24:14.643Z"}}, {"_id": {"$oid": "68687ec0a47f89e2f61eab9c"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:25:42.922780", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:24:16.706Z"}}, {"_id": {"$oid": "68687ec29c3e8a30651ea738"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:25:44.984948", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:24:18.775Z"}}, {"_id": {"$oid": "68687ec4a47f89e2f61eab9d"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:25:47.047853", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:24:20.84Z"}}, {"_id": {"$oid": "68687ec69c3e8a30651ea739"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:25:49.110009", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:24:22.902Z"}}, {"_id": {"$oid": "68687ec8a47f89e2f61eab9e"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:25:51.157216", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:24:24.946Z"}}, {"_id": {"$oid": "68687eca9c3e8a30651ea73a"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:25:53.172055", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:24:26.958Z"}}, {"_id": {"$oid": "68687ecca47f89e2f61eab9f"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:25:55.172410", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:24:28.955Z"}}, {"_id": {"$oid": "68687ece9c3e8a30651ea73b"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:25:57.204167", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:24:30.986Z"}}, {"_id": {"$oid": "68687ed0a47f89e2f61eaba0"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:25:59.203481", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:24:32.985Z"}}, {"_id": {"$oid": "68687ed29c3e8a30651ea73c"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:26:01.125597", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:24:34.911Z"}}, {"_id": {"$oid": "68687ed4a47f89e2f61eaba1"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:26:03.063074", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:24:36.853Z"}}, {"_id": {"$oid": "68687ed69c3e8a30651ea73d"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:26:05.000776", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:24:38.785Z"}}, {"_id": {"$oid": "68687ed8a47f89e2f61eaba2"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:26:06.922567", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:24:40.708Z"}}, {"_id": {"$oid": "68687eda9c3e8a30651ea73e"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:26:08.859556", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:24:42.649Z"}}, {"_id": {"$oid": "68687edca47f89e2f61eaba3"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:26:10.797972", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:24:44.58Z"}}, {"_id": {"$oid": "68687ede9c3e8a30651ea73f"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:26:12.703326", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:24:46.488Z"}}, {"_id": {"$oid": "68687ee0a47f89e2f61eaba4"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:26:14.609652", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:24:48.395Z"}}, {"_id": {"$oid": "68687ee29c3e8a30651ea740"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:26:16.610486", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:24:50.393Z"}}, {"_id": {"$oid": "68687ee4a47f89e2f61eaba5"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:26:18.516058", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:24:52.305Z"}}, {"_id": {"$oid": "68687ee69c3e8a30651ea741"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:26:20.453815", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:24:54.245Z"}}, {"_id": {"$oid": "68687ee8a47f89e2f61eaba6"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:26:22.391644", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:24:56.176Z"}}, {"_id": {"$oid": "68687eea9c3e8a30651ea742"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:26:24.281813", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:24:58.068Z"}}, {"_id": {"$oid": "68687eeba47f89e2f61eaba7"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:26:26.203469", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:24:59.989Z"}}, {"_id": {"$oid": "68687eed9c3e8a30651ea743"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:26:28.125779", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:25:01.915Z"}}, {"_id": {"$oid": "68687eefa47f89e2f61eaba8"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:26:30.063542", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:25:03.85Z"}}, {"_id": {"$oid": "68687ef19c3e8a30651ea744"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:26:31.969149", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:25:05.76Z"}}, {"_id": {"$oid": "68687ef3a47f89e2f61eaba9"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:26:33.969038", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:25:07.754Z"}}, {"_id": {"$oid": "68687ef59c3e8a30651ea745"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:26:35.907309", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:25:09.69Z"}}, {"_id": {"$oid": "68687ef7a47f89e2f61eabaa"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:26:37.844573", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:25:11.633Z"}}, {"_id": {"$oid": "68687ef99c3e8a30651ea746"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:26:39.781847", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:25:13.566Z"}}, {"_id": {"$oid": "68687efba47f89e2f61eabab"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:26:41.735101", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:25:15.522Z"}}, {"_id": {"$oid": "68687efd9c3e8a30651ea747"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:26:43.672885", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:25:17.46Z"}}, {"_id": {"$oid": "68687effa47f89e2f61eabac"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:26:45.563137", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:25:19.351Z"}}, {"_id": {"$oid": "68687f019c3e8a30651ea748"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:26:47.485316", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:25:21.274Z"}}, {"_id": {"$oid": "68687f03a47f89e2f61eabad"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:26:49.391663", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:25:23.175Z"}}, {"_id": {"$oid": "68687f059c3e8a30651ea749"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:26:51.391314", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:25:25.176Z"}}, {"_id": {"$oid": "68687f07a47f89e2f61eabae"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:26:53.328863", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:25:27.115Z"}}, {"_id": {"$oid": "68687f099c3e8a30651ea74a"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:26:55.281969", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:25:29.072Z"}}, {"_id": {"$oid": "68687f0ba47f89e2f61eabaf"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:26:57.219829", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:25:31.01Z"}}, {"_id": {"$oid": "68687f0c9c3e8a30651ea74b"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:26:59.173069", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:25:32.959Z"}}, {"_id": {"$oid": "68687f0ea47f89e2f61eabb0"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:27:01.109991", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:25:34.894Z"}}, {"_id": {"$oid": "68687f109c3e8a30651ea74c"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-05 09:27:03.016004", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-05T09:25:36.799Z"}}, {"_id": {"$oid": "686c7e635f0e2b5dc933f2ab"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-08 10:13:20.038668", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-08T10:11:47.187Z"}}, {"_id": {"$oid": "686c7e6559c875935d33f2a6"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-08 10:13:22.132406", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-08T10:11:49.279Z"}}, {"_id": {"$oid": "686c7e675f0e2b5dc933f2ac"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-08 10:13:24.210827", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-08T10:11:51.359Z"}}, {"_id": {"$oid": "686c7e6959c875935d33f2a7"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-08 10:13:26.304021", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-08T10:11:53.454Z"}}, {"_id": {"$oid": "686c7e6b5f0e2b5dc933f2ad"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-08 10:13:28.335656", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-08T10:11:55.484Z"}}, {"_id": {"$oid": "686c7e6d59c875935d33f2a8"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-08 10:13:30.350724", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-08T10:11:57.496Z"}}, {"_id": {"$oid": "686c7e6f5f0e2b5dc933f2ae"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-08 10:13:32.335036", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-08T10:11:59.484Z"}}, {"_id": {"$oid": "686c7e7159c875935d33f2a9"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-08 10:13:34.320269", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-08T10:12:01.462Z"}}, {"_id": {"$oid": "686c7e735f0e2b5dc933f2af"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-08 10:13:36.351111", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-08T10:12:03.492Z"}}, {"_id": {"$oid": "686c7e7559c875935d33f2aa"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-08 10:13:38.367039", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-08T10:12:05.51Z"}}, {"_id": {"$oid": "686c7e775f0e2b5dc933f2b0"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-08 10:13:40.304705", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-08T10:12:07.452Z"}}, {"_id": {"$oid": "686c7e7959c875935d33f2ab"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-08 10:13:42.242059", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-08T10:12:09.393Z"}}, {"_id": {"$oid": "686c7e7b5f0e2b5dc933f2b1"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-08 10:13:44.163891", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-08T10:12:11.306Z"}}, {"_id": {"$oid": "686c7e7d59c875935d33f2ac"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-08 10:13:46.085200", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-08T10:12:13.232Z"}}, {"_id": {"$oid": "686c7e7f5f0e2b5dc933f2b2"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-08 10:13:48.054109", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-08T10:12:15.202Z"}}, {"_id": {"$oid": "686c7e8159c875935d33f2ad"}, "username": "robot_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-08 10:13:49.961104", "message": "Parallel Merge Fail\n--------------------------------------------------\nParallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\code\\ycf\\Y3project\\Git-Project\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-08T10:12:17.11Z"}}, {"_id": {"$oid": "68886dfdf6e8e052d6d3fca0"}, "username": "gkk", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 14:47:36.080675", "message": "Experiment Fail\n--------------------------------------------------\nq7c1-7c2-7-T1SpectrumWithBiasCoupler-T1WithBiasCoupler(1-11)-z_amp=-1.0-68886e87fbe3cdc544b7b3fb\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"D:\\work\\pyqcat\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\work\\pyqcat\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ********* | B", "create_time": {"$date": "2025-07-29T14:45:17.193Z"}}, {"_id": {"$oid": "68886dfdb242e0e00cd3f94e"}, "username": "gkk", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 14:47:36.457358", "message": "Experiment Fail\n--------------------------------------------------\nq7c1-7c2-7-T1SpectrumWithBiasCoupler-T1WithBiasCoupler(2-11)-z_amp=-0.8-68886e88fbe3cdc544b7b3fc\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"D:\\work\\pyqcat\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\work\\pyqcat\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ********* | B", "create_time": {"$date": "2025-07-29T14:45:17.561Z"}}, {"_id": {"$oid": "68886dfef6e8e052d6d3fca1"}, "username": "gkk", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 14:47:37.269686", "message": "Experiment Fail\n--------------------------------------------------\nq7c1-7c2-7-T1SpectrumWithBiasCoupler-T1With<PERSON>iasCoupler(3-11)-z_amp=-0.6-68886e88fbe3cdc544b7b3fd\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"D:\\work\\pyqcat\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\work\\pyqcat\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ********* | B", "create_time": {"$date": "2025-07-29T14:45:18.35Z"}}, {"_id": {"$oid": "68886dfeb242e0e00cd3f94f"}, "username": "gkk", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 14:47:37.748695", "message": "Experiment Fail\n--------------------------------------------------\nq7c1-7c2-7-T1SpectrumWithBiasCoupler-T1WithBiasCoupler(4-11)-z_amp=-0.4-68886e89fbe3cdc544b7b3fe\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"D:\\work\\pyqcat\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\work\\pyqcat\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ********* | B", "create_time": {"$date": "2025-07-29T14:45:18.869Z"}}, {"_id": {"$oid": "68886dfff6e8e052d6d3fca2"}, "username": "gkk", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 14:47:38.256680", "message": "Experiment Fail\n--------------------------------------------------\nq7c1-7c2-7-T1SpectrumWithBiasCoupler-T1WithBiasCoupler(5-11)-z_amp=-0.2-68886e89fbe3cdc544b7b3ff\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"D:\\work\\pyqcat\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\work\\pyqcat\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ********* | B", "create_time": {"$date": "2025-07-29T14:45:19.389Z"}}, {"_id": {"$oid": "68886dffb242e0e00cd3f950"}, "username": "gkk", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 14:47:38.787713", "message": "Experiment Fail\n--------------------------------------------------\nq7c1-7c2-7-T1SpectrumWith<PERSON>iasCoupler-T1With<PERSON>iasCoupler(6-11)-z_amp=0.0-68886e8afbe3cdc544b7b400\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"D:\\work\\pyqcat\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\work\\pyqcat\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ********* | B", "create_time": {"$date": "2025-07-29T14:45:19.936Z"}}, {"_id": {"$oid": "68886e00f6e8e052d6d3fca3"}, "username": "gkk", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 14:47:39.253994", "message": "Experiment Fail\n--------------------------------------------------\nq7c1-7c2-7-T1SpectrumWith<PERSON>iasCoupler-T1With<PERSON>iasCoupler(7-11)-z_amp=0.2-68886e8afbe3cdc544b7b401\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"D:\\work\\pyqcat\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\work\\pyqcat\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ********* | B", "create_time": {"$date": "2025-07-29T14:45:20.333Z"}}, {"_id": {"$oid": "68886e01b242e0e00cd3f951"}, "username": "gkk", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 14:47:40.169249", "message": "Experiment Fail\n--------------------------------------------------\nq7c1-7c2-7-T1SpectrumWithBiasCoupler-T1With<PERSON>iasCoupler(8-11)-z_amp=0.4-68886e8bfbe3cdc544b7b402\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"D:\\work\\pyqcat\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\work\\pyqcat\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ********* | B", "create_time": {"$date": "2025-07-29T14:45:21.281Z"}}, {"_id": {"$oid": "68886e01f6e8e052d6d3fca4"}, "username": "gkk", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 14:47:40.687774", "message": "Experiment Fail\n--------------------------------------------------\nq7c1-7c2-7-T1SpectrumWith<PERSON>iasCoupler-T1W<PERSON><PERSON>iasCoupler(9-11)-z_amp=0.6-68886e8cfbe3cdc544b7b403\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"D:\\work\\pyqcat\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\work\\pyqcat\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ********* | B", "create_time": {"$date": "2025-07-29T14:45:21.784Z"}}, {"_id": {"$oid": "68886e02b242e0e00cd3f952"}, "username": "gkk", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 14:47:41.188581", "message": "Experiment Fail\n--------------------------------------------------\nq7c1-7c2-7-T1SpectrumWithBiasCoupler-T1With<PERSON>iasCoupler(10-11)-z_amp=0.8-68886e8cfbe3cdc544b7b404\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"D:\\work\\pyqcat\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\work\\pyqcat\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ********* | B", "create_time": {"$date": "2025-07-29T14:45:22.29Z"}}, {"_id": {"$oid": "68886e02f6e8e052d6d3fca5"}, "username": "gkk", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 14:47:41.659788", "message": "Experiment Fail\n--------------------------------------------------\nq7c1-7c2-7-T1SpectrumWithBiasCoupler-T1With<PERSON>iasCoupler(11-11)-z_amp=1.0-68886e8dfbe3cdc544b7b405\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"D:\\work\\pyqcat\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 493, in run\n    self._build_chimera_data()\n  File \"D:\\work\\pyqcat\\pyqcat-visage\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 409, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n", "version": "monster | ********* | B", "create_time": {"$date": "2025-07-29T14:45:22.717Z"}}, {"_id": {"$oid": "6889ef33f6689fe5521f661d"}, "username": "zqq", "sample": "250620-设计验证-102bit-V4.0（Si+Nb120nm+Ta30nm）-Base-6#（Flip-5#-B1）", "env_name": "Y2", "level": 10, "time": "2025-07-30 18:10:03.316791", "message": "Experiment Crash\n--------------------------------------------------\nBUS-5-FindBusCavityFreq-Segm-6889ef71424c4e831609a583\n--------------------------------------------------\n'chi_square'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 337, in run_experiment\n    self.run()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\preliminary_models.py\", line 329, in run\n    self._run_experiment()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\find_cavity_freq.py\", line 524, in _run_experiment\n    self._check_bus()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\find_cavity_freq.py\", line 313, in _check_bus\n    self._check_fit_q()\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\library\\find_cavity_freq.py\", line 358, in _check_fit_q\n    res = Q_fit(\n  File \"D:\\code\\PYQCAT\\lzw\\0.23.2\\pyqcat-apps\\pyQCat\\preliminary\\s21_circle_fit_triton.py\", line 540, in Q_fit\n    chi_square = 'chi_square=%.2f' % port1.fit_result['chi_square']\nKeyError: 'chi_square'\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-07-30T18:08:51.68Z"}}, {"_id": {"$oid": "6896b830150663897c883b31"}, "username": "yxyY413", "sample": "Reset_LRU_test", "env_name": "Y8", "level": 10, "time": "2025-08-09 10:53:24.828710", "message": "Experiment Crash\n--------------------------------------------------\nq4-DetuneCalibration-APEComposite(1-2)-RoughScan-6896b8201d282846de26080a\n--------------------------------------------------\n'P0'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 518, in run_experiment\n    await self._async_composite_run_base()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 469, in _async_composite_run_base\n    await self._async_run_analysis()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 503, in _async_run_analysis\n    self._analysis = run_analysis_process(*args)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 257, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\analysis\\library\\ape_composite_analysis.py\", line 123, in _extract_result\n    cur_x_gap, cur_y_gap = _calculate_gap(point_coin)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\analysis\\library\\ape_composite_analysis.py\", line 86, in _calculate_gap\n    y_data = child_data.y_data[self.options.y_label][x_idx]\nKeyError: 'P0'\n", "version": "monster | 0.23.2 | B", "create_time": {"$date": "2025-08-09T10:53:36.156Z"}}, {"_id": {"$oid": "6896b849150663897c883b33"}, "username": "yxyY413", "sample": "Reset_LRU_test", "env_name": "Y8", "level": 10, "time": "2025-08-09 10:53:50.347475", "message": "Experiment Crash\n--------------------------------------------------\nq4-DetuneCalibration-APEComposite(1-2)-RoughScan-6896b8361d282846de260814\n--------------------------------------------------\n'P0'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 518, in run_experiment\n    await self._async_composite_run_base()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 469, in _async_composite_run_base\n    await self._async_run_analysis()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 503, in _async_run_analysis\n    self._analysis = run_analysis_process(*args)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 257, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\analysis\\library\\ape_composite_analysis.py\", line 123, in _extract_result\n    cur_x_gap, cur_y_gap = _calculate_gap(point_coin)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\analysis\\library\\ape_composite_analysis.py\", line 86, in _calculate_gap\n    y_data = child_data.y_data[self.options.y_label][x_idx]\nKeyError: 'P0'\n", "version": "monster | 0.23.2 | B", "create_time": {"$date": "2025-08-09T10:54:01.665Z"}}]