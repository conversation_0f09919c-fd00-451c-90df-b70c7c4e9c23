[{"_id": {"$oid": "687759021b17e2c4f88ce0fd"}, "username": "lzf", "sample": "Reset_LRU_test", "env_name": "Y8", "level": 10, "time": "2025-07-16 15:46:33.294797", "message": "Experiment Crash\n--------------------------------------------------\nq6-QubitSpectrum-687758cd7f9480cbefd9f0d3\n--------------------------------------------------\n[Errno 22] Invalid argument\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\top_experiment_v1.py\", line 610, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 171, in top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\curve_analysis.py\", line 422, in run_analysis\n    self._extract_result(better_data_key)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\library\\qubit_spectrum_analysis.py\", line 107, in _extract_result\n    print(f\"Background noise checked: SNR={self.quality.value} \"\nOSError: [Errno 22] Invalid argument\n", "version": "monster | 0.23.2 | B", "create_time": {"$date": "2025-07-16T15:47:14.021Z"}}, {"_id": {"$oid": "68775ab31b17e2c4f88ce100"}, "username": "lzf", "sample": "Reset_LRU_test", "env_name": "Y8", "level": 10, "time": "2025-07-16 15:53:46.833308", "message": "Experiment Crash\n--------------------------------------------------\nq6-ReadoutFreqSSCalibrate-SingleShot_01(5-31)-probe_freq=7141.4-68775a867f9480cbefd9f111\n--------------------------------------------------\n[Errno 22] Invalid argument\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\top_experiment_v1.py\", line 610, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 171, in top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\library\\single_shot_analysis.py\", line 196, in run_analysis\n    self._run_training()\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\library\\single_shot_analysis.py\", line 124, in _run_training\n    dcm.train(n_multiple=self.options.n_multiple)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\algorithms\\iqprobability.py\", line 646, in train\n    self._distinguish(fit_label, outlier_index)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\algorithms\\iqprobability.py\", line 1110, in _distinguish\n    if self._check_repeat(label, true_label):\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\algorithms\\iqprobability.py\", line 1004, in _check_repeat\n    print(\nOSError: [Errno 22] Invalid argument\n", "version": "monster | 0.23.2 | B", "create_time": {"$date": "2025-07-16T15:54:27.561Z"}}, {"_id": {"$oid": "68775ab7814a76608c8ce004"}, "username": "lzf", "sample": "Reset_LRU_test", "env_name": "Y8", "level": 10, "time": "2025-07-16 15:53:51.140896", "message": "Experiment Crash\n--------------------------------------------------\nq6-ReadoutFreqSSCalibrate-SingleShot_01(8-31)-probe_freq=7141.7-68775a867f9480cbefd9f114\n--------------------------------------------------\n[Errno 22] Invalid argument\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\top_experiment_v1.py\", line 610, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 171, in top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\library\\single_shot_analysis.py\", line 196, in run_analysis\n    self._run_training()\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\library\\single_shot_analysis.py\", line 124, in _run_training\n    dcm.train(n_multiple=self.options.n_multiple)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\algorithms\\iqprobability.py\", line 646, in train\n    self._distinguish(fit_label, outlier_index)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\algorithms\\iqprobability.py\", line 1110, in _distinguish\n    if self._check_repeat(label, true_label):\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\algorithms\\iqprobability.py\", line 1004, in _check_repeat\n    print(\nOSError: [Errno 22] Invalid argument\n", "version": "monster | 0.23.2 | B", "create_time": {"$date": "2025-07-16T15:54:31.868Z"}}, {"_id": {"$oid": "68775ab8814a76608c8ce005"}, "username": "lzf", "sample": "Reset_LRU_test", "env_name": "Y8", "level": 10, "time": "2025-07-16 15:53:52.247194", "message": "Experiment Crash\n--------------------------------------------------\nq6-ReadoutFreqSSCalibrate-SingleShot_01(9-31)-probe_freq=7141.8-68775a867f9480cbefd9f115\n--------------------------------------------------\n[Errno 22] Invalid argument\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\top_experiment_v1.py\", line 610, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 171, in top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\library\\single_shot_analysis.py\", line 196, in run_analysis\n    self._run_training()\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\library\\single_shot_analysis.py\", line 124, in _run_training\n    dcm.train(n_multiple=self.options.n_multiple)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\algorithms\\iqprobability.py\", line 646, in train\n    self._distinguish(fit_label, outlier_index)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\algorithms\\iqprobability.py\", line 1110, in _distinguish\n    if self._check_repeat(label, true_label):\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\algorithms\\iqprobability.py\", line 1004, in _check_repeat\n    print(\nOSError: [Errno 22] Invalid argument\n", "version": "monster | 0.23.2 | B", "create_time": {"$date": "2025-07-16T15:54:32.972Z"}}, {"_id": {"$oid": "68775aba814a76608c8ce006"}, "username": "lzf", "sample": "Reset_LRU_test", "env_name": "Y8", "level": 10, "time": "2025-07-16 15:53:53.310635", "message": "Experiment Crash\n--------------------------------------------------\nq6-ReadoutFreqSSCalibrate-SingleShot_01(10-31)-probe_freq=7141.9-68775a867f9480cbefd9f116\n--------------------------------------------------\n[Errno 22] Invalid argument\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\top_experiment_v1.py\", line 610, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 171, in top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\library\\single_shot_analysis.py\", line 196, in run_analysis\n    self._run_training()\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\library\\single_shot_analysis.py\", line 124, in _run_training\n    dcm.train(n_multiple=self.options.n_multiple)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\algorithms\\iqprobability.py\", line 646, in train\n    self._distinguish(fit_label, outlier_index)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\algorithms\\iqprobability.py\", line 1110, in _distinguish\n    if self._check_repeat(label, true_label):\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\algorithms\\iqprobability.py\", line 1004, in _check_repeat\n    print(\nOSError: [Errno 22] Invalid argument\n", "version": "monster | 0.23.2 | B", "create_time": {"$date": "2025-07-16T15:54:34.038Z"}}, {"_id": {"$oid": "68775abc1b17e2c4f88ce101"}, "username": "lzf", "sample": "Reset_LRU_test", "env_name": "Y8", "level": 10, "time": "2025-07-16 15:53:55.464434", "message": "Experiment Crash\n--------------------------------------------------\nq6-ReadoutFreqSSCalibrate-SingleShot_01(11-31)-probe_freq=7142.0-68775a867f9480cbefd9f117\n--------------------------------------------------\n[Errno 22] Invalid argument\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\top_experiment_v1.py\", line 610, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 171, in top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\library\\single_shot_analysis.py\", line 196, in run_analysis\n    self._run_training()\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\library\\single_shot_analysis.py\", line 124, in _run_training\n    dcm.train(n_multiple=self.options.n_multiple)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\algorithms\\iqprobability.py\", line 646, in train\n    self._distinguish(fit_label, outlier_index)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\algorithms\\iqprobability.py\", line 1110, in _distinguish\n    if self._check_repeat(label, true_label):\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\algorithms\\iqprobability.py\", line 1004, in _check_repeat\n    print(\nOSError: [Errno 22] Invalid argument\n", "version": "monster | 0.23.2 | B", "create_time": {"$date": "2025-07-16T15:54:36.188Z"}}, {"_id": {"$oid": "68775abe814a76608c8ce007"}, "username": "lzf", "sample": "Reset_LRU_test", "env_name": "Y8", "level": 10, "time": "2025-07-16 15:53:57.632182", "message": "Experiment Crash\n--------------------------------------------------\nq6-ReadoutFreqSSCalibrate-SingleShot_01(13-31)-probe_freq=7142.2-68775a867f9480cbefd9f119\n--------------------------------------------------\n[Errno 22] Invalid argument\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\top_experiment_v1.py\", line 610, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 171, in top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\library\\single_shot_analysis.py\", line 196, in run_analysis\n    self._run_training()\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\library\\single_shot_analysis.py\", line 124, in _run_training\n    dcm.train(n_multiple=self.options.n_multiple)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\algorithms\\iqprobability.py\", line 646, in train\n    self._distinguish(fit_label, outlier_index)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\algorithms\\iqprobability.py\", line 1110, in _distinguish\n    if self._check_repeat(label, true_label):\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\algorithms\\iqprobability.py\", line 1004, in _check_repeat\n    print(\nOSError: [Errno 22] Invalid argument\n", "version": "monster | 0.23.2 | B", "create_time": {"$date": "2025-07-16T15:54:38.36Z"}}, {"_id": {"$oid": "68775ac01b17e2c4f88ce102"}, "username": "lzf", "sample": "Reset_LRU_test", "env_name": "Y8", "level": 10, "time": "2025-07-16 15:53:59.797935", "message": "Experiment Crash\n--------------------------------------------------\nq6-ReadoutFreqSSCalibrate-SingleShot_01(14-31)-probe_freq=7142.3-68775a867f9480cbefd9f11a\n--------------------------------------------------\n[Errno 22] Invalid argument\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\top_experiment_v1.py\", line 610, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 171, in top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\library\\single_shot_analysis.py\", line 196, in run_analysis\n    self._run_training()\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\library\\single_shot_analysis.py\", line 124, in _run_training\n    dcm.train(n_multiple=self.options.n_multiple)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\algorithms\\iqprobability.py\", line 646, in train\n    self._distinguish(fit_label, outlier_index)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\algorithms\\iqprobability.py\", line 1110, in _distinguish\n    if self._check_repeat(label, true_label):\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\algorithms\\iqprobability.py\", line 1004, in _check_repeat\n    print(\nOSError: [Errno 22] Invalid argument\n", "version": "monster | 0.23.2 | B", "create_time": {"$date": "2025-07-16T15:54:40.527Z"}}, {"_id": {"$oid": "68775acd1b17e2c4f88ce103"}, "username": "lzf", "sample": "Reset_LRU_test", "env_name": "Y8", "level": 10, "time": "2025-07-16 15:54:13.103703", "message": "Experiment Crash\n--------------------------------------------------\nq6-ReadoutFreqSSCalibrate-SingleShot_01(12-31)-probe_freq=7142.1-68775a867f9480cbefd9f118\n--------------------------------------------------\n[Errno 22] Invalid argument\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\top_experiment_v1.py\", line 610, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 171, in top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 214, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 254, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\library\\single_shot_analysis.py\", line 196, in run_analysis\n    self._run_training()\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\library\\single_shot_analysis.py\", line 124, in _run_training\n    dcm.train(n_multiple=self.options.n_multiple)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\algorithms\\iqprobability.py\", line 646, in train\n    self._distinguish(fit_label, outlier_index)\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\algorithms\\iqprobability.py\", line 1110, in _distinguish\n    if self._check_repeat(label, true_label):\n  File \"D:\\code\\lzf\\pyqcat-visage\\pyQCat\\analysis\\algorithms\\iqprobability.py\", line 1004, in _check_repeat\n    print(\nOSError: [Errno 22] Invalid argument\n", "version": "monster | 0.23.2 | B", "create_time": {"$date": "2025-07-16T15:54:53.834Z"}}, {"_id": {"$oid": "68779cc9814a76608c8ce021"}, "username": "yxyY413", "sample": "Reset_LRU_test", "env_name": "Y8", "level": 10, "time": "2025-07-16 20:35:44.369781", "message": "Experiment Crash\n--------------------------------------------------\nq5q6c5-6-CouplerSpectrumZAmpDynamic-68779c9f65bea5c7de595f64\n--------------------------------------------------\n[WinError 123] 文件名、目录名或卷标语法不正确。: 'Z:\\\\Y8\\\\yxy\\\\Reset_LRU_test\\\\CouplerSpectrumZAmpDynamic\\\\q5q6c5-6\\x825-07-16\\x019.55.24\\\\CouplerSpectrum'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 198, in run\n    self._validate_options()\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 931, in _validate_options\n    paths = rocket_optimize(self.experiment_options.simulator_data_path)\n  File \"D:\\code\\yxy\\naga\\pyqcat-apps\\pyQCat\\tools\\utilities.py\", line 772, in rocket_optimize\n    for f in os.listdir(root):\nOSError: [WinError 123] 文件名、目录名或卷标语法不正确。: 'Z:\\\\Y8\\\\yxy\\\\Reset_LRU_test\\\\CouplerSpectrumZAmpDynamic\\\\q5q6c5-6\\x825-07-16\\x019.55.24\\\\CouplerSpectrum'\n", "version": "monster | 0.23.2 | B", "create_time": {"$date": "2025-07-16T20:36:25.381Z"}}, {"_id": {"$oid": "68848d01f4b30d9991446dfe"}, "username": "by240046", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-26 16:11:31.195397", "message": "Experiment Fail\n--------------------------------------------------\nq79q85-FixedPointCalibration-RamseyExtend(1-unknow)-idx=0, z_amp=0.00949Vtarget_freq_osc=4384.044289_25MHz,-68848db250123b220cdfab0b\n--------------------------------------------------\n<Acq Data Tackle Error> | Traceback (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 695, in async_execute_loop\n    progress = tq(self.sweep_points)\n  File \"C:\\ProgramData\\Miniconda3\\lib\\site-packages\\tqdm\\std.py\", line 1109, in __init__\n    self.refresh(lock_args=self.lock_args)\n  File \"C:\\ProgramData\\Miniconda3\\lib\\site-packages\\tqdm\\std.py\", line 1361, in refresh\n    self.display()\n  File \"C:\\ProgramData\\Miniconda3\\lib\\site-packages\\tqdm\\std.py\", line 1509, in display\n    self.sp(self.__str__() if msg is None else msg)\n  File \"C:\\ProgramData\\Miniconda3\\lib\\site-packages\\tqdm\\std.py\", line 350, in print_status\n    fp_write('\\r' + s + (' ' * max(last_len[0] - len_s, 0)))\n  File \"C:\\ProgramData\\Miniconda3\\lib\\site-packages\\tqdm\\std.py\", line 343, in fp_write\n    fp.write(_unicode(s))\n  File \"C:\\ProgramData\\Miniconda3\\lib\\site-packages\\tqdm\\utils.py\", line 145, in inner\n    return func(*args, **kwargs)\nOSError: [Errno 22] Invalid argument\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-26T16:08:33.016Z"}}, {"_id": {"$oid": "68848d90f4b30d9991446dff"}, "username": "by240046", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-26 16:13:54.702686", "message": "Experiment Fail\n--------------------------------------------------\nq79q85-FixedPointCalibration-RamseyExtend(1-unknow)-idx=0, z_amp=0.04713Vtarget_freq_osc=4414.044289_25MHz,-68848e2a50123b220cdfab25\n--------------------------------------------------\n<Acq Data Tackle Error> | Traceback (most recent call last):\n  File \"E:\\lzw\\code\\GitProject\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 695, in async_execute_loop\n    progress = tq(self.sweep_points)\n  File \"C:\\ProgramData\\Miniconda3\\lib\\site-packages\\tqdm\\std.py\", line 1109, in __init__\n    self.refresh(lock_args=self.lock_args)\n  File \"C:\\ProgramData\\Miniconda3\\lib\\site-packages\\tqdm\\std.py\", line 1361, in refresh\n    self.display()\n  File \"C:\\ProgramData\\Miniconda3\\lib\\site-packages\\tqdm\\std.py\", line 1509, in display\n    self.sp(self.__str__() if msg is None else msg)\n  File \"C:\\ProgramData\\Miniconda3\\lib\\site-packages\\tqdm\\std.py\", line 350, in print_status\n    fp_write('\\r' + s + (' ' * max(last_len[0] - len_s, 0)))\n  File \"C:\\ProgramData\\Miniconda3\\lib\\site-packages\\tqdm\\std.py\", line 343, in fp_write\n    fp.write(_unicode(s))\n  File \"C:\\ProgramData\\Miniconda3\\lib\\site-packages\\tqdm\\utils.py\", line 145, in inner\n    return func(*args, **kwargs)\nOSError: [Errno 22] Invalid argument\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-26T16:10:56.521Z"}}, {"_id": {"$oid": "6888261ff4b30d9991446ec1"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-29 09:40:58.757223", "message": "Experiment Crash\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(2-31)-power=-39dBm-688826aac1c9e18c7070e85b\n--------------------------------------------------\n[WinError 123] 文件名、目录名或卷标语法不正确。: ':'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 605, in run_experiment\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 362, in _validate_options\n    super()._validate_options()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 942, in _validate_options\n    paths = rocket_optimize(self.experiment_options.simulator_data_path)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\tools\\utilities.py\", line 770, in rocket_optimize\n    for f in os.listdir(root):\nOSError: [WinError 123] 文件名、目录名或卷标语法不正确。: ':'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:38:39.928Z"}}, {"_id": {"$oid": "68882672f340c94b3b446ff1"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-29 09:42:21.523040", "message": "Experiment Crash\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(2-31)-power=-39dBm-688826fdf13d5118f9313222\n--------------------------------------------------\n[WinError 123] 文件名、目录名或卷标语法不正确。: ':'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 605, in run_experiment\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 362, in _validate_options\n    super()._validate_options()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 942, in _validate_options\n    paths = rocket_optimize(self.experiment_options.simulator_data_path)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\tools\\utilities.py\", line 770, in rocket_optimize\n    for f in os.listdir(root):\nOSError: [WinError 123] 文件名、目录名或卷标语法不正确。: ':'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:40:02.691Z"}}, {"_id": {"$oid": "6888272df340c94b3b447000"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-29 09:45:28.058822", "message": "Experiment Crash\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(2-31)-power=-39dBm-688827b87cc95b5f160570ee\n--------------------------------------------------\n[WinError 123] 文件名、目录名或卷标语法不正确。: ':'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 605, in run_experiment\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 362, in _validate_options\n    super()._validate_options()\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 942, in _validate_options\n    paths = rocket_optimize(self.experiment_options.simulator_data_path)\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\tools\\utilities.py\", line 770, in rocket_optimize\n    for f in os.listdir(root):\nOSError: [WinError 123] 文件名、目录名或卷标语法不正确。: ':'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:43:09.21Z"}}]