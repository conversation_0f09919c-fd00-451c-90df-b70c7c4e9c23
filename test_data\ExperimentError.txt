_id	message
682d711ca77f3478825739f7	Experiment Fail
--------------------------------------------------
q1q7c1-7-Co<PERSON><PERSON>SweepDetuneRabiWidth-CouplerRabiScanWidthDetune(1-41)-Detune=-40
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711ca77f3478825739f8	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(3-41)-Detune=-36
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711ca77f3478825739f9	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(2-41)-Detune=-38
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711ca77f3478825739fa	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(4-41)-Detune=-34
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711ca77f3478825739fb	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(5-41)-Detune=-32
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711ca77f3478825739fc	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(6-41)-Detune=-30
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711ca77f3478825739fd	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(7-41)-Detune=-28
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711ca77f3478825739fe	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(8-41)-Detune=-26
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711ca77f3478825739ff	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(9-41)-Detune=-24
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711ca77f347882573a00	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(10-41)-Detune=-22
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711ca77f347882573a01	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(12-41)-Detune=-18
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711ca77f347882573a02	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(11-41)-Detune=-20
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711ca77f347882573a03	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(13-41)-Detune=-16
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711da77f347882573a04	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(14-41)-Detune=-14
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711da77f347882573a05	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(15-41)-Detune=-12
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711da77f347882573a06	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(16-41)-Detune=-10
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711da77f347882573a07	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(17-41)-Detune=-8
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711da77f347882573a08	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(18-41)-Detune=-6
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711da77f347882573a09	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(19-41)-Detune=-4
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711da77f347882573a0a	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(20-41)-Detune=-2
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711da77f347882573a0b	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(21-41)-Detune=0
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711da77f347882573a0c	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(23-41)-Detune=4
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711da77f347882573a0d	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(22-41)-Detune=2
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711da77f347882573a0e	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(24-41)-Detune=6
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711da77f347882573a0f	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(25-41)-Detune=8
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711da77f347882573a10	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(26-41)-Detune=10
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711da77f347882573a11	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(27-41)-Detune=12
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711da77f347882573a12	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(29-41)-Detune=16
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711da77f347882573a13	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(28-41)-Detune=14
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711da77f347882573a14	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(30-41)-Detune=18
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711da77f347882573a15	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(31-41)-Detune=20
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711ea77f347882573a16	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(32-41)-Detune=22
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711ea77f347882573a17	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(33-41)-Detune=24
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711ea77f347882573a18	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(34-41)-Detune=26
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711ea77f347882573a19	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(36-41)-Detune=30
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711ea77f347882573a1a	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(35-41)-Detune=28
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711ea77f347882573a1b	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(37-41)-Detune=32
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711ea77f347882573a1c	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(38-41)-Detune=34
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711ea77f347882573a1d	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(39-41)-Detune=36
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711ea77f347882573a1e	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(40-41)-Detune=38
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d711ea77f347882573a1f	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(41-41)-Detune=40
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d8395a77f347882573abf	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerRabiScanWidth
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682d83eba77f347882573ac1	Experiment Fail
--------------------------------------------------
q1q7c1-7-CouplerRabiScanWidthDetune
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 488, in run
    self._validate_and_merge()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 269, in _validate_and_merge
    assert len(set(same_lo_gaps)) == 1, (
AssertionError: Same lo validate error, details:
lo: xy-1
value:[4950, 3450]
unit: ['q1', 'q7']


682edf58f04f1fe4feb73698	Experiment Fail
--------------------------------------------------
q97~c18-24-ZExp
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 490, in run
    self._build_chimera_data()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 406, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 490, in run
    self._build_chimera_data()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 406, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'


682edfcaf04f1fe4feb73699	Experiment Fail
--------------------------------------------------
q97~c18-24-ZExp
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 490, in run
    self._build_chimera_data()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 406, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 490, in run
    self._build_chimera_data()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 406, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'


682ee01ef04f1fe4feb7369a	Experiment Fail
--------------------------------------------------
q97~c18-24-ZExp
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 490, in run
    self._build_chimera_data()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 406, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'c102-107'

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 490, in run
    self._build_chimera_data()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 406, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'c102-107'

