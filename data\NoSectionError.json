[{"_id": {"$oid": "686e016f2ebaa9fee5f72a9d"}, "username": "job", "sample": "D8-24bit-2", "env_name": "D8-2-3", "level": 10, "time": "2025-07-09 13:43:10.576323", "message": "Experiment Crash\n--------------------------------------------------\nq17-MicSourceRamseySpectrum-686e016e318a0dde21a2285f\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-07-09T13:43:11.057Z"}}, {"_id": {"$oid": "686e016fec42392c4af72aa3"}, "username": "job", "sample": "D8-24bit-2", "env_name": "D8-2-3", "level": 10, "time": "2025-07-09 13:43:10.621335", "message": "Experiment Crash\n--------------------------------------------------\nq18-MicSourceRamseySpectrum-686e016e318a0dde21a22861\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-07-09T13:43:11.126Z"}}, {"_id": {"$oid": "686e016f2ebaa9fee5f72a9e"}, "username": "job", "sample": "D8-24bit-2", "env_name": "D8-2-3", "level": 10, "time": "2025-07-09 13:43:10.669345", "message": "Experiment Crash\n--------------------------------------------------\nq19-MicSourceRamseySpectrum-686e016e318a0dde21a22863\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-07-09T13:43:11.15Z"}}, {"_id": {"$oid": "686e016fec42392c4af72aa4"}, "username": "job", "sample": "D8-24bit-2", "env_name": "D8-2-3", "level": 10, "time": "2025-07-09 13:43:10.720358", "message": "Experiment Crash\n--------------------------------------------------\nq20-MicSourceRamseySpectrum-686e016e318a0dde21a22865\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-07-09T13:43:11.217Z"}}, {"_id": {"$oid": "686e016fec42392c4af72aa5"}, "username": "job", "sample": "D8-24bit-2", "env_name": "D8-2-3", "level": 10, "time": "2025-07-09 13:43:10.766368", "message": "Experiment Crash\n--------------------------------------------------\nq21-MicSourceRamseySpectrum-686e016e318a0dde21a22867\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-07-09T13:43:11.268Z"}}, {"_id": {"$oid": "686e016f2ebaa9fee5f72a9f"}, "username": "job", "sample": "D8-24bit-2", "env_name": "D8-2-3", "level": 10, "time": "2025-07-09 13:43:10.820381", "message": "Experiment Crash\n--------------------------------------------------\nq22-MicSourceRamseySpectrum-686e016e318a0dde21a22869\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-07-09T13:43:11.296Z"}}, {"_id": {"$oid": "686e016fec42392c4af72aa6"}, "username": "job", "sample": "D8-24bit-2", "env_name": "D8-2-3", "level": 10, "time": "2025-07-09 13:43:10.871392", "message": "Experiment Crash\n--------------------------------------------------\nq23-MicSourceRamseySpectrum-686e016e318a0dde21a2286b\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-07-09T13:43:11.356Z"}}, {"_id": {"$oid": "686e016f2ebaa9fee5f72aa0"}, "username": "job", "sample": "D8-24bit-2", "env_name": "D8-2-3", "level": 10, "time": "2025-07-09 13:43:10.922405", "message": "Experiment Crash\n--------------------------------------------------\nq24-MicSourceRamseySpectrum-686e016e318a0dde21a2286d\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-07-09T13:43:11.4Z"}}, {"_id": {"$oid": "6890baee9192f4bbc39cc582"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-04 21:51:52.453718", "message": "Experiment Crash\n--------------------------------------------------\nq1-MicSourceRamseySpectrum-6890baf7c7216026b418e931\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-08-04T21:51:42.332Z"}}, {"_id": {"$oid": "6890baee6946627d159cc5b9"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-04 21:51:52.502733", "message": "Experiment Crash\n--------------------------------------------------\nq2-MicSourceRamseySpectrum-6890baf7c7216026b418e933\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-08-04T21:51:42.39Z"}}, {"_id": {"$oid": "6890baee9192f4bbc39cc583"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-04 21:51:52.560420", "message": "Experiment Crash\n--------------------------------------------------\nq3-MicSourceRamseySpectrum-6890baf7c7216026b418e935\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-08-04T21:51:42.448Z"}}, {"_id": {"$oid": "6890baee6946627d159cc5ba"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-04 21:51:52.607421", "message": "Experiment Crash\n--------------------------------------------------\nq4-MicSourceRamseySpectrum-6890baf7c7216026b418e937\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-08-04T21:51:42.508Z"}}, {"_id": {"$oid": "6890baee9192f4bbc39cc584"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-04 21:51:52.655424", "message": "Experiment Crash\n--------------------------------------------------\nq5-MicSourceRamseySpectrum-6890baf7c7216026b418e939\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-08-04T21:51:42.559Z"}}, {"_id": {"$oid": "6890baee6946627d159cc5bb"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-04 21:51:52.701427", "message": "Experiment Crash\n--------------------------------------------------\nq6-MicSourceRamseySpectrum-6890baf7c7216026b418e93b\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-08-04T21:51:42.614Z"}}, {"_id": {"$oid": "6890baee9192f4bbc39cc585"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-04 21:51:52.747137", "message": "Experiment Crash\n--------------------------------------------------\nq7-MicSourceRamseySpectrum-6890baf7c7216026b418e93d\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-08-04T21:51:42.645Z"}}, {"_id": {"$oid": "6890baee6946627d159cc5bc"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-04 21:51:52.796655", "message": "Experiment Crash\n--------------------------------------------------\nq8-MicSourceRamseySpectrum-6890baf7c7216026b418e93f\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-08-04T21:51:42.675Z"}}, {"_id": {"$oid": "6891706c9192f4bbc39cc890"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-05 10:46:15.083444", "message": "Experiment Crash\n--------------------------------------------------\nq1-MicSourceRamseySpectrum-6891707690761059a6ef35fd\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-08-05T10:46:04.468Z"}}, {"_id": {"$oid": "6891706c6946627d159cc88f"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-05 10:46:15.141448", "message": "Experiment Crash\n--------------------------------------------------\nq2-MicSourceRamseySpectrum-6891707690761059a6ef35ff\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-08-05T10:46:04.525Z"}}, {"_id": {"$oid": "6891706c9192f4bbc39cc891"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-05 10:46:15.183453", "message": "Experiment Crash\n--------------------------------------------------\nq3-MicSourceRamseySpectrum-6891707690761059a6ef3601\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-08-05T10:46:04.614Z"}}, {"_id": {"$oid": "6891706c6946627d159cc890"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-05 10:46:15.225456", "message": "Experiment Crash\n--------------------------------------------------\nq4-MicSourceRamseySpectrum-6891707690761059a6ef3603\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-08-05T10:46:04.644Z"}}, {"_id": {"$oid": "6891706c9192f4bbc39cc892"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-05 10:46:15.269460", "message": "Experiment Crash\n--------------------------------------------------\nq5-MicSourceRamseySpectrum-6891707690761059a6ef3605\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-08-05T10:46:04.685Z"}}, {"_id": {"$oid": "6891706c6946627d159cc891"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-05 10:46:15.315464", "message": "Experiment Crash\n--------------------------------------------------\nq6-MicSourceRamseySpectrum-6891707690761059a6ef3607\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-08-05T10:46:04.733Z"}}, {"_id": {"$oid": "6891706c9192f4bbc39cc893"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-05 10:46:15.356467", "message": "Experiment Crash\n--------------------------------------------------\nq7-MicSourceRamseySpectrum-6891707690761059a6ef3609\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-08-05T10:46:04.763Z"}}, {"_id": {"$oid": "6891706c6946627d159cc892"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-05 10:46:15.399471", "message": "Experiment Crash\n--------------------------------------------------\nq8-MicSourceRamseySpectrum-6891707690761059a6ef360b\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-08-05T10:46:04.79Z"}}, {"_id": {"$oid": "6891b7e1890a246c6cfb08e2"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-05 15:51:08.436269", "message": "Experiment Crash\n--------------------------------------------------\nq1-MicSourceRamseySpectrum-6891b7ec94a082d218d9f8f7\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-08-05T15:50:57.706Z"}}, {"_id": {"$oid": "6891b7e1890a246c6cfb08e3"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-05 15:51:08.476272", "message": "Experiment Crash\n--------------------------------------------------\nq2-MicSourceRamseySpectrum-6891b7ec94a082d218d9f8f9\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-08-05T15:50:57.796Z"}}, {"_id": {"$oid": "6891b7e1890a246c6cfb08e4"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-05 15:51:08.515272", "message": "Experiment Crash\n--------------------------------------------------\nq3-MicSourceRamseySpectrum-6891b7ec94a082d218d9f8fb\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-08-05T15:50:57.843Z"}}, {"_id": {"$oid": "6891b7e1b84b084e52fb0849"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-05 15:51:08.568274", "message": "Experiment Crash\n--------------------------------------------------\nq4-MicSourceRamseySpectrum-6891b7ec94a082d218d9f8fd\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-08-05T15:50:57.853Z"}}, {"_id": {"$oid": "6891b7e1890a246c6cfb08e5"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-05 15:51:08.607275", "message": "Experiment Crash\n--------------------------------------------------\nq5-MicSourceRamseySpectrum-6891b7ec94a082d218d9f8ff\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-08-05T15:50:57.912Z"}}, {"_id": {"$oid": "6891b7e1b84b084e52fb084a"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-05 15:51:08.642279", "message": "Experiment Crash\n--------------------------------------------------\nq6-MicSourceRamseySpectrum-6891b7ec94a082d218d9f901\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-08-05T15:50:57.942Z"}}, {"_id": {"$oid": "6891b7e1890a246c6cfb08e6"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-05 15:51:08.679308", "message": "Experiment Crash\n--------------------------------------------------\nq7-MicSourceRamseySpectrum-6891b7ec94a082d218d9f903\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-08-05T15:50:57.972Z"}}, {"_id": {"$oid": "6891b7e2b84b084e52fb084b"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-05 15:51:08.714279", "message": "Experiment Crash\n--------------------------------------------------\nq8-MicSourceRamseySpectrum-6891b7ec94a082d218d9f905\n--------------------------------------------------\nNo section: 'N5173B'\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 516, in run_experiment\n    await self._sync_composite_run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 183, in _sync_composite_run\n    mic_source_obj = InstConnect(mic_source_name, config_path)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\undetermine\\micsource_ramsey_spectrum.py\", line 36, in __init__\n    self.inst = INSTRUMENT(config_path)[instrument_model]\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 70, in __getitem__\n    return self._get(secname)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\instrument\\INSTRUMENT.py\", line 73, in _get\n    self._visa_address = self._cf.get(secname, \"visa_address\")\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 781, in get\n    d = self._unify_values(section, vars)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\configparser.py\", line 1152, in _unify_values\n    raise NoSectionError(section) from None\nconfigparser.NoSectionError: No section: 'N5173B'\n", "version": "monster | ******** | C", "create_time": {"$date": "2025-08-05T15:50:58.002Z"}}]