#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MongoDB批量更新脚本使用示例
作者: KangKang Geng
"""

from batch_update_mongodb import MongoDBUpdater
import sys


def example_usage():
    """使用示例"""
    
    # 配置参数
    config = {
        'file_path': 'txt_data/AttributeError.txt',  # txt文件路径
        'start_line': 2,                             # 起始行号
        'end_line': 49,                              # 结束行号
        'tackle_value': 1,                           # tackle字段目标值
        'config_file': 'db_config.json'              # 数据库配置文件
    }
    
    print("MongoDB批量更新脚本使用示例")
    print("=" * 50)
    print(f"文件路径: {config['file_path']}")
    print(f"行号范围: {config['start_line']} - {config['end_line']}")
    print(f"tackle值: {config['tackle_value']}")
    print("=" * 50)
    
    # 创建更新器实例
    updater = MongoDBUpdater(config['config_file'])
    
    try:
        # 连接数据库
        print("正在连接数据库...")
        if not updater.connect():
            print("数据库连接失败，退出程序")
            sys.exit(1)
            
        # 解析文件
        print("正在解析文件...")
        object_ids = updater.parse_txt_file(
            config['file_path'], 
            config['start_line'], 
            config['end_line']
        )
        
        if not object_ids:
            print("未找到任何有效的ID，退出程序")
            sys.exit(0)
            
        print(f"找到 {len(object_ids)} 个ID需要更新")
        
        # 确认操作
        response = input("是否继续执行更新操作？(y/N): ")
        if response.lower() != 'y':
            print("操作已取消")
            sys.exit(0)
            
        # 执行更新
        print("开始执行批量更新...")
        stats = updater.update_tackle_field(object_ids, config['tackle_value'])
        
        # 打印摘要
        updater.print_summary(stats)
        
    except Exception as e:
        print(f"程序执行过程中发生错误: {e}")
        sys.exit(1)
    finally:
        updater.disconnect()


def interactive_mode():
    """交互式模式"""
    print("MongoDB批量更新脚本 - 交互式模式")
    print("=" * 50)
    
    try:
        # 获取用户输入
        file_path = input("请输入txt文件路径 (默认: txt_data/AttributeError.txt): ").strip()
        if not file_path:
            file_path = "txt_data/AttributeError.txt"
            
        start_line = input("请输入起始行号 (默认: 2): ").strip()
        start_line = int(start_line) if start_line else 2
        
        end_line = input("请输入结束行号 (默认: 49): ").strip()
        end_line = int(end_line) if end_line else 49
        
        tackle_value = input("请输入tackle字段的目标值 (默认: 1): ").strip()
        tackle_value = int(tackle_value) if tackle_value else 1
        
        config_file = input("请输入数据库配置文件路径 (默认: db_config.json): ").strip()
        if not config_file:
            config_file = "db_config.json"
            
        print("\n配置信息:")
        print(f"文件路径: {file_path}")
        print(f"行号范围: {start_line} - {end_line}")
        print(f"tackle值: {tackle_value}")
        print(f"配置文件: {config_file}")
        
        # 确认配置
        response = input("\n配置是否正确？(y/N): ")
        if response.lower() != 'y':
            print("操作已取消")
            return
            
        # 创建更新器并执行
        updater = MongoDBUpdater(config_file)
        
        # 连接数据库
        if not updater.connect():
            print("数据库连接失败")
            return
            
        # 解析文件
        object_ids = updater.parse_txt_file(file_path, start_line, end_line)
        
        if not object_ids:
            print("未找到任何有效的ID")
            return
            
        print(f"\n找到 {len(object_ids)} 个ID需要更新")
        
        # 最终确认
        response = input("是否继续执行更新操作？(y/N): ")
        if response.lower() != 'y':
            print("操作已取消")
            return
            
        # 执行更新
        stats = updater.update_tackle_field(object_ids, tackle_value)
        
        # 打印摘要
        updater.print_summary(stats)
        
    except KeyboardInterrupt:
        print("\n操作被用户中断")
    except Exception as e:
        print(f"发生错误: {e}")
    finally:
        if 'updater' in locals():
            updater.disconnect()


if __name__ == "__main__":
    print("请选择运行模式:")
    print("1. 示例模式 (使用预设参数)")
    print("2. 交互式模式 (手动输入参数)")
    
    try:
        choice = input("请输入选择 (1/2): ").strip()
        
        if choice == "1":
            example_usage()
        elif choice == "2":
            interactive_mode()
        else:
            print("无效选择，退出程序")
            
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序发生错误: {e}")
