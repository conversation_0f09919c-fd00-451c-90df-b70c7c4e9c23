[{"_id": {"$oid": "688e429cf6e8e052d6d405fd"}, "username": "gll_auto", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-08-03 00:57:06.321757", "message": "Experiment Fail\n--------------------------------------------------\nq95-ReadoutPowerCalibrate-SingleShot_01(15-31)-probe_power=-26-688e435f7196b158d115b8f5\n--------------------------------------------------\n<Concurrent CR Error> | concurrent.futures.process._RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\ProgramData\\Miniconda3\\lib\\concurrent\\futures\\process.py\", line 246, in _process_worker\n    r = call_item.fn(*call_item.args, **call_item.kwargs)\n  File \"D:\\gll\\code_Y_3_2_3\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 134, in parallel_top_experiment_analysis\n    return base_analysis_process(\n  File \"D:\\gll\\code_Y_3_2_3\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 216, in base_analysis_process\n    return run_analysis_process(\n  File \"D:\\gll\\code_Y_3_2_3\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 261, in run_analysis_process\n    analysis_obj.run_analysis()\n  File \"D:\\gll\\code_Y_3_2_3\\0.23.2\\pyqcat-apps\\pyQCat\\analysis\\library\\single_shot_analysis.py\", line 206, in run_analysis\n    self._visualization()\n  File \"D:\\gll\\code_Y_3_2_3\\0.23.2\\pyqcat-apps\\pyQCat\\analysis\\library\\single_shot_analysis.py\", line 179, in _visualization\n    fig = dcm.plot(\n  File \"D:\\gll\\code_Y_3_2_3\\0.23.2\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\iqprobability.py\", line 875, in plot\n    Z = self.predict(i=xx.ravel(), q=yy.ravel(), screen_flag=False)\n  File \"D:\\gll\\code_Y_3_2_3\\0.23.2\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\iqprobability.py\", line 747, in predict\n    return self._convert_label(labels)\n  File \"D:\\gll\\code_Y_3_2_3\\0.23.2\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\iqprobability.py\", line 1136, in _convert_label\n    np.put(convert_label, index_list, self._label.index(label))\n  File \"<__array_function__ internals>\", line 180, in put\n  File \"C:\\ProgramData\\Miniconda3\\lib\\site-packages\\numpy\\core\\fromnumeric.py\", line 543, in put\n    return put(ind, v, mode=mode)\nOverflowError: int too big to convert\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\gll\\code_Y_3_2_3\\0.23.2\\pyqcat-apps\\pyQCat\\concurrent\\calculate_resource.py\", line 35, in run_in_executor\n    return await loop.run_in_executor(executor, fn, *args)\nOverflowError: int too big to convert\n", "version": "monster | 0.23.2.2 | B", "create_time": {"$date": "2025-08-03T00:53:48.471Z"}}]