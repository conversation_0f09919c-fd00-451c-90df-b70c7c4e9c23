[{"_id": {"$oid": "682d711fa77f347882573a34"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:21.311332", "message": "Experiment Fail\n--------------------------------------------------\nq1-DERBSingle\n--------------------------------------------------\n<Exp(DERBSingle) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 492, in run_experiment\n    self.run()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(DERBSingle) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-21T14:22:23.41Z"}}, {"_id": {"$oid": "682d7125a77f347882573a35"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:27.531094", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerFreqCalibration\n--------------------------------------------------\n<Exp(CouplerFreqCalibration) experiment options error> | drive freq is null | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 492, in run_experiment\n    self.run()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 195, in run\n    self._check_options()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\composite\\single_gate\\qubit_freq_calibration.py\", line 102, in _check_options\n    raise ExperimentOptionsError(self, msg=\"drive freq is null\")\npyQCat.errors.ExperimentOptionsError: <Exp(CouplerFreqCalibration) experiment options error> | drive freq is null | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-21T14:22:29.674Z"}}, {"_id": {"$oid": "682d829aa77f347882573aa7"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 15:39:56.109445", "message": "Experiment Fail\n--------------------------------------------------\nq1-DetuneCalibration\n--------------------------------------------------\n<Exp(DetuneCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(DetuneCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-21T15:36:58.261Z"}}, {"_id": {"$oid": "682d82c3a77f347882573aa8"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 15:40:37.522899", "message": "Experiment Fail\n--------------------------------------------------\nq1-XpiDetection\n--------------------------------------------------\n<Exp(XpiDetection) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(XpiDetection) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-21T15:37:39.673Z"}}, {"_id": {"$oid": "682d830ba77f347882573aaa"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 15:41:48.921040", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerDetuneCalibration\n--------------------------------------------------\n<Exp(CouplerDetuneCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(CouplerDetuneCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-21T15:38:51.084Z"}}, {"_id": {"$oid": "682d830ba77f347882573aab"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 15:41:49.151270", "message": "Experiment Fail\n--------------------------------------------------\nq1-XYZTimingComposite\n--------------------------------------------------\n<Exp(XYZTimingComposite) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(XYZTimingComposite) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-21T15:38:51.294Z"}}, {"_id": {"$oid": "682d830ba77f347882573aac"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 15:41:49.334656", "message": "Experiment Fail\n--------------------------------------------------\nq1-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-21T15:38:51.475Z"}}, {"_id": {"$oid": "682d830ba77f347882573aad"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 15:41:49.516049", "message": "Experiment Fail\n--------------------------------------------------\nq1c1-7-ZZShiftSweetPointCalibrationNew\n--------------------------------------------------\n<Exp(ZZShiftSweetPointCalibrationNew) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(ZZShiftSweetPointCalibrationNew) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-21T15:38:51.642Z"}}, {"_id": {"$oid": "682d843ea77f347882573ac7"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 15:46:55.946902", "message": "Experiment Fail\n--------------------------------------------------\nbq(q5)-tq(q4)-ACCrosstalkFixF\n--------------------------------------------------\n<Exp(ACCrosstalkFixF) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(ACCrosstalkFixF) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-21T15:43:58.096Z"}}, {"_id": {"$oid": "682d843ea77f347882573ac8"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 15:46:56.128295", "message": "Experiment Fail\n--------------------------------------------------\nbq(q4)-tq(c1-7)-CouplerACCrosstalkFixF\n--------------------------------------------------\n<Exp(CouplerACCrosstalkFixF) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(CouplerACCrosstalkFixF) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-21T15:43:58.27Z"}}, {"_id": {"$oid": "682d849ca77f347882573aca"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 15:48:30.035135", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSpectrum\n--------------------------------------------------\n<Exp(CouplerSpectrum) experiment options error> | not set freq list, but bit drive freq is null | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 602, in run_experiment\n    self._check_options()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\coupler_experiment_v1.py\", line 120, in _check_options\n    super()._check_options()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\single\\single_gate\\qubit_spectrum.py\", line 237, in _check_options\n    raise ExperimentOptionsError(self, msg=\"not set freq list, but bit drive freq is null\")\npyQCat.errors.ExperimentOptionsError: <Exp(CouplerSpectrum) experiment options error> | not set freq list, but bit drive freq is null | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-21T15:45:32.183Z"}}, {"_id": {"$oid": "682ffbe2f04f1fe4feb73736"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-23 12:41:56.134129", "message": "Experiment Fail\n--------------------------------------------------\nq1-DetuneCalibration\n--------------------------------------------------\n<Exp(DetuneCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(DetuneCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-23T12:38:58.423Z"}}, {"_id": {"$oid": "682ffc0df04f1fe4feb73737"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-23 12:42:39.395375", "message": "Experiment Fail\n--------------------------------------------------\nq1-XpiDetection\n--------------------------------------------------\n<Exp(XpiDetection) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(XpiDetection) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-23T12:39:41.689Z"}}, {"_id": {"$oid": "6830207af04f1fe4feb73798"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-23 15:18:04.159964", "message": "Experiment Fail\n--------------------------------------------------\nq1-DetuneCalibration\n--------------------------------------------------\n<Exp(DetuneCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(DetuneCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-23T15:15:06.45Z"}}, {"_id": {"$oid": "683020a4f04f1fe4feb73799"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-23 15:18:46.115604", "message": "Experiment Fail\n--------------------------------------------------\nq1-XpiDetection\n--------------------------------------------------\n<Exp(XpiDetection) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(XpiDetection) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-23T15:15:48.418Z"}}, {"_id": {"$oid": "68304732f04f1fe4feb7380c"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-23 18:03:15.915900", "message": "Experiment Fail\n--------------------------------------------------\nq1-DetuneCalibration\n--------------------------------------------------\n<Exp(DetuneCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(DetuneCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-23T18:00:18.22Z"}}, {"_id": {"$oid": "6830475cf04f1fe4feb7380d"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-23 18:03:58.314302", "message": "Experiment Fail\n--------------------------------------------------\nq1-XpiDetection\n--------------------------------------------------\n<Exp(XpiDetection) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(XpiDetection) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-23T18:01:00.621Z"}}, {"_id": {"$oid": "68341073f04f1fe4feb73846"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:57:07.938066", "message": "Experiment Fail\n--------------------------------------------------\nq1-DetuneCalibration\n--------------------------------------------------\n<Exp(DetuneCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(DetuneCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:55:47.061Z"}}, {"_id": {"$oid": "68341085f04f1fe4feb73847"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:57:26.742670", "message": "Experiment Fail\n--------------------------------------------------\nq1-XpiDetection\n--------------------------------------------------\n<Exp(XpiDetection) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(XpiDetection) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:56:05.852Z"}}, {"_id": {"$oid": "68341093f04f1fe4feb73848"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:57:40.222308", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerDetuneCalibration\n--------------------------------------------------\n<Exp(CouplerDetuneCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(CouplerDetuneCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:56:19.341Z"}}, {"_id": {"$oid": "68341093f04f1fe4feb73849"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:57:40.355619", "message": "Experiment Fail\n--------------------------------------------------\nq1-XYZTimingComposite\n--------------------------------------------------\n<Exp(XYZTimingComposite) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(XYZTimingComposite) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:56:19.474Z"}}, {"_id": {"$oid": "68341093f04f1fe4feb7384a"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:57:40.478304", "message": "Experiment Fail\n--------------------------------------------------\nq1-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:56:19.594Z"}}, {"_id": {"$oid": "68341093f04f1fe4feb7384b"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:57:40.599448", "message": "Experiment Fail\n--------------------------------------------------\nq1c1-7-ZZShiftSweetPointCalibrationNew\n--------------------------------------------------\n<Exp(ZZShiftSweetPointCalibrationNew) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(ZZShiftSweetPointCalibrationNew) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:56:19.715Z"}}, {"_id": {"$oid": "683410b1f04f1fe4feb73856"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:58:10.450962", "message": "Experiment Fail\n--------------------------------------------------\nbq(q5)-tq(q4)-ACCrosstalkFixF\n--------------------------------------------------\n<Exp(ACCrosstalkFixF) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(ACCrosstalkFixF) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:56:49.562Z"}}, {"_id": {"$oid": "683410b1f04f1fe4feb73857"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:58:10.572831", "message": "Experiment Fail\n--------------------------------------------------\nbq(q4)-tq(c1-7)-CouplerACCrosstalkFixF\n--------------------------------------------------\n<Exp(CouplerACCrosstalkFixF) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(CouplerACCrosstalkFixF) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:56:49.685Z"}}, {"_id": {"$oid": "683410b1f04f1fe4feb73858"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:58:10.702910", "message": "Experiment Fail\n--------------------------------------------------\nq10q4c4-10-CPhaseTMSE\n--------------------------------------------------\n<Exp(CPhaseTMSE) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 604, in run_experiment\n    self._validate_options()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 356, in _validate_options\n    super()._validate_options()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(CPhaseTMSE) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:56:49.818Z"}}, {"_id": {"$oid": "683410b4f04f1fe4feb73859"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:58:12.999589", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSpectrum\n--------------------------------------------------\n<Exp(CouplerSpectrum) experiment options error> | not set freq list, but bit drive freq is null | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 603, in run_experiment\n    self._check_options()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/coupler_experiment_v1.py\", line 120, in _check_options\n    super()._check_options()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/single/single_gate/qubit_spectrum.py\", line 237, in _check_options\n    raise ExperimentOptionsError(self, msg=\"not set freq list, but bit drive freq is null\")\npyQCat.errors.ExperimentOptionsError: <Exp(CouplerSpectrum) experiment options error> | not set freq list, but bit drive freq is null | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:56:52.118Z"}}, {"_id": {"$oid": "68341102f04f1fe4feb73899"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:31.144873", "message": "Experiment Fail\n--------------------------------------------------\nq1-DERB<PERSON>ingle\n--------------------------------------------------\n<Exp(DERBSingle) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(DERBSingle) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:10.253Z"}}, {"_id": {"$oid": "68341103f04f1fe4feb7389a"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:31.950539", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerFreqCalibration\n--------------------------------------------------\n<Exp(CouplerFreqCalibration) experiment options error> | drive freq is null | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 195, in run\n    self._check_options()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite/single_gate/qubit_freq_calibration.py\", line 102, in _check_options\n    raise ExperimentOptionsError(self, msg=\"drive freq is null\")\npyQCat.errors.ExperimentOptionsError: <Exp(CouplerFreqCalibration) experiment options error> | drive freq is null | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:11.062Z"}}, {"_id": {"$oid": "68341151f04f1fe4feb7389c"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 15:00:50.041617", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerXYZTimingComposite\n--------------------------------------------------\n<Exp(CouplerXYZTimingComposite) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(CouplerXYZTimingComposite) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:59:29.152Z"}}, {"_id": {"$oid": "68341151f04f1fe4feb7389d"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 15:00:50.168563", "message": "Experiment Fail\n--------------------------------------------------\nq1c1-7-CouplerXYZTimingZZShiftComposite\n--------------------------------------------------\n<Exp(CouplerXYZTimingZZShiftComposite) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(CouplerXYZTimingZZShiftComposite) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:59:29.276Z"}}, {"_id": {"$oid": "6834115ff04f1fe4feb7389e"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 15:01:04.831946", "message": "Experiment Fail\n--------------------------------------------------\nq4-XYCrossRabiWidth\n--------------------------------------------------\n<Exp(XYCrossRabiWidth) experiment options error> | Please check qubits [Qubit(name=q4, coord=(0.0, 4.8)), Qubit(name=q5, coord=(0.0, 6.4))] is not contained target q4, or bias_name_list ['q3', 'q4', 'q5'] | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 195, in run\n    self._check_options()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite/crosstalk/xy_cross_rw.py\", line 187, in _check_options\n    raise ExperimentOptionsError(\npyQCat.errors.ExperimentOptionsError: <Exp(XYCrossRabiWidth) experiment options error> | Please check qubits [Qubit(name=q4, coord=(0.0, 4.8)), Qubit(name=q5, coord=(0.0, 6.4))] is not contained target q4, or bias_name_list ['q3', 'q4', 'q5'] | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:59:43.948Z"}}, {"_id": {"$oid": "68341160f04f1fe4feb7389f"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 15:01:04.995078", "message": "Experiment Fail\n--------------------------------------------------\nq4-XYCrossPlusRabiWidth-XYCrossRabiWidth(1-2)-Tq4\n--------------------------------------------------\n<Exp(XYCrossRabiWidth) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(XYCrossRabiWidth) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:59:44.11Z"}}, {"_id": {"$oid": "68341160f04f1fe4feb738a0"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 15:01:05.101694", "message": "Experiment Fail\n--------------------------------------------------\nq5-XYCrossPlusRabiWidth-XYCrossRabiWidth(2-2)-Tq5\n--------------------------------------------------\n<Exp(XYCrossRabiWidth) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(XYCrossRabiWidth) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:59:44.214Z"}}, {"_id": {"$oid": "683411def04f1fe4feb738b2"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 15:03:11.081376", "message": "Experiment Fail\n--------------------------------------------------\nq1-StabilitySingleShot\n--------------------------------------------------\n<Exp(StabilitySingleShot) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(StabilitySingleShot) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T15:01:50.204Z"}}, {"_id": {"$oid": "683411def04f1fe4feb738b3"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 15:03:11.219191", "message": "Experiment Fail\n--------------------------------------------------\nq1-StabilityT1\n--------------------------------------------------\n<Exp(StabilityT1) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(StabilityT1) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T15:01:50.334Z"}}, {"_id": {"$oid": "683411def04f1fe4feb738b4"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 15:03:11.353689", "message": "Experiment Fail\n--------------------------------------------------\nq1-StabilityT2Ramsey\n--------------------------------------------------\n<Exp(StabilityT2Ramsey) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(StabilityT2Ramsey) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T15:01:50.465Z"}}, {"_id": {"$oid": "683411def04f1fe4feb738b5"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 15:03:11.505107", "message": "Experiment Fail\n--------------------------------------------------\nq3-MicSourceRamseySpectrum\n--------------------------------------------------\n<Exp(MicSourceRamseySpectrum) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(MicSourceRamseySpectrum) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T15:01:50.61Z"}}, {"_id": {"$oid": "683411def04f1fe4feb738b6"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 15:03:11.636490", "message": "Experiment Fail\n--------------------------------------------------\nq3-<PERSON><PERSON><PERSON>idth<PERSON>omposite\n--------------------------------------------------\n<Exp(RabiWidthComposite) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(RabiWidthComposite) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T15:01:50.742Z"}}, {"_id": {"$oid": "683411e2f04f1fe4feb738b8"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 15:03:14.904914", "message": "Experiment Fail\n--------------------------------------------------\nq1-DistortionPolesOpt\n--------------------------------------------------\n<Exp(DistortionPolesOpt) experiment options error> | key(poles_bounds_path) | value(F:\\2024-01\\monster_dev\\pyqcat-monster\\conf\\poles_bounds_p0.json) | F:\\2024-01\\monster_dev\\pyqcat-monster\\conf\\poles_bounds_p0.json does not exist! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite/z_distortion/distortion_pole_optimization.py\", line 120, in _check_options\n    with open(file_name, mode=\"r\", encoding=\"utf-8\") as fp:\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nFileNotFoundError: [Errno 2] No such file or directory: 'F:\\\\2024-01\\\\monster_dev\\\\pyqcat-monster\\\\conf\\\\poles_bounds_p0.json'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 195, in run\n    self._check_options()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite/z_distortion/distortion_pole_optimization.py\", line 123, in _check_options\n    raise ExperimentOptionsError(\npyQCat.errors.ExperimentOptionsError: <Exp(DistortionPolesOpt) experiment options error> | key(poles_bounds_path) | value(F:\\2024-01\\monster_dev\\pyqcat-monster\\conf\\poles_bounds_p0.json) | F:\\2024-01\\monster_dev\\pyqcat-monster\\conf\\poles_bounds_p0.json does not exist! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T15:01:54.016Z"}}, {"_id": {"$oid": "683411e2f04f1fe4feb738b9"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 15:03:15.061269", "message": "Experiment Fail\n--------------------------------------------------\nq1-SweepDetuneRabiZamp\n--------------------------------------------------\n<Exp(SweepDetuneRabiZamp) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(SweepDetuneRabiZamp) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T15:01:54.172Z"}}, {"_id": {"$oid": "68341287f04f1fe4feb738c3"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 15:06:00.548743", "message": "Experiment Fail\n--------------------------------------------------\nq1-N<PERSON>learParams\n--------------------------------------------------\n<Exp(NMClearParams) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(NMClearParams) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T15:04:39.661Z"}}, {"_id": {"$oid": "68341287f04f1fe4feb738c4"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 15:06:00.677817", "message": "Experiment Fail\n--------------------------------------------------\nq1-NMPhaseParams\n--------------------------------------------------\n<Exp(NMPhaseParams) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(NMPhaseParams) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T15:04:39.789Z"}}, {"_id": {"$oid": "68341287f04f1fe4feb738c5"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 15:06:00.805834", "message": "Experiment Fail\n--------------------------------------------------\nq1-NMPhaseParamsBoth\n--------------------------------------------------\n<Exp(NMPhaseParamsBoth) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(NMPhaseParamsBoth) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T15:04:39.907Z"}}, {"_id": {"$oid": "68341288f04f1fe4feb738c6"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 15:06:00.928002", "message": "Experiment Fail\n--------------------------------------------------\nq1-N<PERSON><PERSON>rParamsBoth\n--------------------------------------------------\n<Exp(NMClearParamsBoth) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(NMClearParamsBoth) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T15:04:40.032Z"}}, {"_id": {"$oid": "6837c9e9f04f1fe4feb73c29"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:43:52.964396", "message": "Experiment Fail\n--------------------------------------------------\nq10-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:43:53.468Z"}}, {"_id": {"$oid": "6837c9e9f04f1fe4feb73c2a"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:43:52.969381", "message": "Experiment Fail\n--------------------------------------------------\nq101-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:43:53.481Z"}}, {"_id": {"$oid": "6837c9e9f04f1fe4feb73c2b"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:43:53.001296", "message": "Experiment Fail\n--------------------------------------------------\nq25-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:43:53.49Z"}}, {"_id": {"$oid": "6837c9e9f04f1fe4feb73c2c"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:43:53.009276", "message": "Experiment Fail\n--------------------------------------------------\nq3-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:43:53.49Z"}}, {"_id": {"$oid": "6837c9e9f04f1fe4feb73c2d"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:43:52.996310", "message": "Experiment Fail\n--------------------------------------------------\nq23-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:43:53.49Z"}}, {"_id": {"$oid": "6837c9e9f04f1fe4feb73c2e"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:43:52.983344", "message": "Experiment Fail\n--------------------------------------------------\nq17-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:43:53.491Z"}}, {"_id": {"$oid": "6837c9e9f04f1fe4feb73c2f"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:43:53.019249", "message": "Experiment Fail\n--------------------------------------------------\nq33-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:43:53.493Z"}}, {"_id": {"$oid": "6837c9e9f04f1fe4feb73c30"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:43:53.014263", "message": "Experiment Fail\n--------------------------------------------------\nq31-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:43:53.494Z"}}, {"_id": {"$oid": "6837c9e9f04f1fe4feb73c31"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:43:52.979354", "message": "Experiment Fail\n--------------------------------------------------\nq15-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:43:53.495Z"}}, {"_id": {"$oid": "6837c9e9f04f1fe4feb73c32"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:43:52.992320", "message": "Experiment Fail\n--------------------------------------------------\nq21-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:43:53.495Z"}}, {"_id": {"$oid": "6837c9e9f04f1fe4feb73c33"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:43:53.023238", "message": "Experiment Fail\n--------------------------------------------------\nq36-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:43:53.495Z"}}, {"_id": {"$oid": "6837c9e9f04f1fe4feb73c34"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:43:52.974369", "message": "Experiment Fail\n--------------------------------------------------\nq12-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:43:53.497Z"}}, {"_id": {"$oid": "6837c9e9f04f1fe4feb73c35"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:43:52.987333", "message": "Experiment Fail\n--------------------------------------------------\nq2-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:43:53.499Z"}}, {"_id": {"$oid": "6837c9e9f04f1fe4feb73c36"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:43:53.005286", "message": "Experiment Fail\n--------------------------------------------------\nq28-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:43:53.499Z"}}, {"_id": {"$oid": "6837c9e9f04f1fe4feb73c37"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:43:53.028224", "message": "Experiment Fail\n--------------------------------------------------\nq38-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:43:53.501Z"}}, {"_id": {"$oid": "6837c9e9f04f1fe4feb73c38"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:43:52.962401", "message": "Experiment Fail\n--------------------------------------------------\nq1-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:43:53.523Z"}}, {"_id": {"$oid": "6837c9e9f04f1fe4feb73c39"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:43:52.966390", "message": "Experiment Fail\n--------------------------------------------------\nq100-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:43:53.524Z"}}, {"_id": {"$oid": "6837c9e9f04f1fe4feb73c3a"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:43:52.989329", "message": "Experiment Fail\n--------------------------------------------------\nq20-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:43:53.539Z"}}, {"_id": {"$oid": "6837c9e9f04f1fe4feb73c3b"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:43:52.994315", "message": "Experiment Fail\n--------------------------------------------------\nq22-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:43:53.539Z"}}, {"_id": {"$oid": "6837c9e9f04f1fe4feb73c3c"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:43:52.971376", "message": "Experiment Fail\n--------------------------------------------------\nq102-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:43:53.539Z"}}, {"_id": {"$oid": "6837c9e9f04f1fe4feb73c3d"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:43:52.999301", "message": "Experiment Fail\n--------------------------------------------------\nq24-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:43:53.539Z"}}, {"_id": {"$oid": "6837c9e9f04f1fe4feb73c3e"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:43:53.003290", "message": "Experiment Fail\n--------------------------------------------------\nq27-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:43:53.542Z"}}, {"_id": {"$oid": "6837c9e9f04f1fe4feb73c40"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:43:52.976363", "message": "Experiment Fail\n--------------------------------------------------\nq14-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:43:53.544Z"}}, {"_id": {"$oid": "6837c9e9f04f1fe4feb73c41"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:43:53.017254", "message": "Experiment Fail\n--------------------------------------------------\nq32-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:43:53.545Z"}}, {"_id": {"$oid": "6837c9e9f04f1fe4feb73c42"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:43:53.026229", "message": "Experiment Fail\n--------------------------------------------------\nq37-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:43:53.547Z"}}, {"_id": {"$oid": "6837c9e9f04f1fe4feb73c43"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:43:53.021243", "message": "Experiment Fail\n--------------------------------------------------\nq34-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:43:53.548Z"}}, {"_id": {"$oid": "6837c9e9f04f1fe4feb73c44"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:43:52.981350", "message": "Experiment Fail\n--------------------------------------------------\nq16-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:43:53.548Z"}}, {"_id": {"$oid": "6837c9e9f04f1fe4feb73c45"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:43:52.985338", "message": "Experiment Fail\n--------------------------------------------------\nq19-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:43:53.549Z"}}, {"_id": {"$oid": "6837c9e9f04f1fe4feb73c3f"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:43:53.007281", "message": "Experiment Fail\n--------------------------------------------------\nq29-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:43:53.543Z"}}, {"_id": {"$oid": "6837c9e9f04f1fe4feb73c46"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:43:53.012268", "message": "Experiment Fail\n--------------------------------------------------\nq30-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:43:53.553Z"}}, {"_id": {"$oid": "6837c9e9f04f1fe4feb73c47"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:43:53.030218", "message": "Experiment Fail\n--------------------------------------------------\nq39-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:43:53.554Z"}}, {"_id": {"$oid": "6837ca08f04f1fe4feb73c48"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:23.879420", "message": "Experiment Fail\n--------------------------------------------------\nq42-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:24.401Z"}}, {"_id": {"$oid": "6837ca08f04f1fe4feb73c49"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:23.874379", "message": "Experiment Fail\n--------------------------------------------------\nq40-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:24.404Z"}}, {"_id": {"$oid": "6837ca08f04f1fe4feb73c4a"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:23.888399", "message": "Experiment Fail\n--------------------------------------------------\nq47-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:24.406Z"}}, {"_id": {"$oid": "6837ca08f04f1fe4feb73c4b"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:23.883354", "message": "Experiment Fail\n--------------------------------------------------\nq45-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:24.407Z"}}, {"_id": {"$oid": "6837ca08f04f1fe4feb73c4d"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:23.892388", "message": "Experiment Fail\n--------------------------------------------------\nq49-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:24.408Z"}}, {"_id": {"$oid": "6837ca08f04f1fe4feb73c4c"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:23.896320", "message": "Experiment Fail\n--------------------------------------------------\nq50-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:24.407Z"}}, {"_id": {"$oid": "6837ca08f04f1fe4feb73c4e"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:23.905295", "message": "Experiment Fail\n--------------------------------------------------\nq54-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:24.41Z"}}, {"_id": {"$oid": "6837ca08f04f1fe4feb73c4f"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:23.911280", "message": "Experiment Fail\n--------------------------------------------------\nq57-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:24.412Z"}}, {"_id": {"$oid": "6837ca08f04f1fe4feb73c50"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:23.901306", "message": "Experiment Fail\n--------------------------------------------------\nq52-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:24.412Z"}}, {"_id": {"$oid": "6837ca08f04f1fe4feb73c51"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:23.923246", "message": "Experiment Fail\n--------------------------------------------------\nq62-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:24.414Z"}}, {"_id": {"$oid": "6837ca08f04f1fe4feb73c52"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:23.915268", "message": "Experiment Fail\n--------------------------------------------------\nq59-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:24.415Z"}}, {"_id": {"$oid": "6837ca08f04f1fe4feb73c53"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:23.919258", "message": "Experiment Fail\n--------------------------------------------------\nq60-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:24.417Z"}}, {"_id": {"$oid": "6837ca08f04f1fe4feb73c54"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:23.931226", "message": "Experiment Fail\n--------------------------------------------------\nq66-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:24.418Z"}}, {"_id": {"$oid": "6837ca08f04f1fe4feb73c55"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:23.927237", "message": "Experiment Fail\n--------------------------------------------------\nq64-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:24.421Z"}}, {"_id": {"$oid": "6837ca08f04f1fe4feb73c56"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:23.936212", "message": "Experiment Fail\n--------------------------------------------------\nq68-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:24.425Z"}}, {"_id": {"$oid": "6837ca08f04f1fe4feb73c57"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:23.872384", "message": "Experiment Fail\n--------------------------------------------------\nq4-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:24.454Z"}}, {"_id": {"$oid": "6837ca08f04f1fe4feb73c58"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:23.877369", "message": "Experiment Fail\n--------------------------------------------------\nq41-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:24.455Z"}}, {"_id": {"$oid": "6837ca08f04f1fe4feb73c59"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:23.894383", "message": "Experiment Fail\n--------------------------------------------------\nq5-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:24.456Z"}}, {"_id": {"$oid": "6837ca08f04f1fe4feb73c5a"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:23.886345", "message": "Experiment Fail\n--------------------------------------------------\nq46-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:24.457Z"}}, {"_id": {"$oid": "6837ca08f04f1fe4feb73c5b"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:23.881360", "message": "Experiment Fail\n--------------------------------------------------\nq44-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:24.457Z"}}, {"_id": {"$oid": "6837ca08f04f1fe4feb73c5c"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:23.925242", "message": "Experiment Fail\n--------------------------------------------------\nq63-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:24.458Z"}}, {"_id": {"$oid": "6837ca08f04f1fe4feb73c5d"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:23.890336", "message": "Experiment Fail\n--------------------------------------------------\nq48-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:24.461Z"}}, {"_id": {"$oid": "6837ca08f04f1fe4feb73c5e"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:23.903301", "message": "Experiment Fail\n--------------------------------------------------\nq53-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:24.461Z"}}, {"_id": {"$oid": "6837ca08f04f1fe4feb73c5f"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:23.921253", "message": "Experiment Fail\n--------------------------------------------------\nq61-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:24.464Z"}}, {"_id": {"$oid": "6837ca08f04f1fe4feb73c60"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:23.917262", "message": "Experiment Fail\n--------------------------------------------------\nq6-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:24.464Z"}}, {"_id": {"$oid": "6837ca08f04f1fe4feb73c61"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:23.909285", "message": "Experiment Fail\n--------------------------------------------------\nq55-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:24.465Z"}}, {"_id": {"$oid": "6837ca08f04f1fe4feb73c62"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:23.913274", "message": "Experiment Fail\n--------------------------------------------------\nq58-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:24.467Z"}}, {"_id": {"$oid": "6837ca08f04f1fe4feb73c63"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:23.929232", "message": "Experiment Fail\n--------------------------------------------------\nq65-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:24.468Z"}}, {"_id": {"$oid": "6837ca08f04f1fe4feb73c64"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:23.933221", "message": "Experiment Fail\n--------------------------------------------------\nq67-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:24.47Z"}}, {"_id": {"$oid": "6837ca08f04f1fe4feb73c65"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:23.899312", "message": "Experiment Fail\n--------------------------------------------------\nq51-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:24.471Z"}}, {"_id": {"$oid": "6837ca08f04f1fe4feb73c66"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:23.938208", "message": "Experiment Fail\n--------------------------------------------------\nq69-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:24.473Z"}}, {"_id": {"$oid": "6837ca26f04f1fe4feb73c67"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:54.024599", "message": "Experiment Fail\n--------------------------------------------------\nq70-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:54.168Z"}}, {"_id": {"$oid": "6837ca26f04f1fe4feb73c68"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:54.021607", "message": "Experiment Fail\n--------------------------------------------------\nq7-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:54.381Z"}}, {"_id": {"$oid": "6837ca26f04f1fe4feb73c69"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:54.028588", "message": "Experiment Fail\n--------------------------------------------------\nq72-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:54.398Z"}}, {"_id": {"$oid": "6837ca26f04f1fe4feb73c6a"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:54.032578", "message": "Experiment Fail\n--------------------------------------------------\nq74-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:54.399Z"}}, {"_id": {"$oid": "6837ca26f04f1fe4feb73c6b"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:54.049534", "message": "Experiment Fail\n--------------------------------------------------\nq81-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:54.414Z"}}, {"_id": {"$oid": "6837ca26f04f1fe4feb73c6c"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:54.045544", "message": "Experiment Fail\n--------------------------------------------------\nq8-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:54.414Z"}}, {"_id": {"$oid": "6837ca26f04f1fe4feb73c6d"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:54.053522", "message": "Experiment Fail\n--------------------------------------------------\nq83-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:54.415Z"}}, {"_id": {"$oid": "6837ca26f04f1fe4feb73c6e"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:54.063496", "message": "Experiment Fail\n--------------------------------------------------\nq88-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:54.415Z"}}, {"_id": {"$oid": "6837ca26f04f1fe4feb73c6f"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:54.037564", "message": "Experiment Fail\n--------------------------------------------------\nq76-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:54.417Z"}}, {"_id": {"$oid": "6837ca26f04f1fe4feb73c70"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:54.041554", "message": "Experiment Fail\n--------------------------------------------------\nq78-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:54.42Z"}}, {"_id": {"$oid": "6837ca26f04f1fe4feb73c71"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:54.059507", "message": "Experiment Fail\n--------------------------------------------------\nq85-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:54.423Z"}}, {"_id": {"$oid": "6837ca26f04f1fe4feb73c72"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:54.030642", "message": "Experiment Fail\n--------------------------------------------------\nq73-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:54.566Z"}}, {"_id": {"$oid": "6837ca26f04f1fe4feb73c73"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:54.026654", "message": "Experiment Fail\n--------------------------------------------------\nq71-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:54.57Z"}}, {"_id": {"$oid": "6837ca26f04f1fe4feb73c74"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:54.051528", "message": "Experiment Fail\n--------------------------------------------------\nq82-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:54.591Z"}}, {"_id": {"$oid": "6837ca26f04f1fe4feb73c75"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:54.039560", "message": "Experiment Fail\n--------------------------------------------------\nq77-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:54.593Z"}}, {"_id": {"$oid": "6837ca26f04f1fe4feb73c76"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:54.047538", "message": "Experiment Fail\n--------------------------------------------------\nq80-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:54.593Z"}}, {"_id": {"$oid": "6837ca26f04f1fe4feb73c77"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:54.043549", "message": "Experiment Fail\n--------------------------------------------------\nq79-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:54.594Z"}}, {"_id": {"$oid": "6837ca26f04f1fe4feb73c78"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:54.035571", "message": "Experiment Fail\n--------------------------------------------------\nq75-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:54.594Z"}}, {"_id": {"$oid": "6837ca26f04f1fe4feb73c79"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:54.061501", "message": "Experiment Fail\n--------------------------------------------------\nq87-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:54.595Z"}}, {"_id": {"$oid": "6837ca26f04f1fe4feb73c7a"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:54.055517", "message": "Experiment Fail\n--------------------------------------------------\nq84-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:54.596Z"}}, {"_id": {"$oid": "6837ca26f04f1fe4feb73c7b"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:54.322803", "message": "Experiment Fail\n--------------------------------------------------\nq97-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:54.598Z"}}, {"_id": {"$oid": "6837ca26f04f1fe4feb73c7c"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:54.318813", "message": "Experiment Fail\n--------------------------------------------------\nq95-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:54.602Z"}}, {"_id": {"$oid": "6837ca26f04f1fe4feb73c7d"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:54.314824", "message": "Experiment Fail\n--------------------------------------------------\nq92-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:54.604Z"}}, {"_id": {"$oid": "6837ca26f04f1fe4feb73c7e"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:54.310835", "message": "Experiment Fail\n--------------------------------------------------\nq90-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:54.604Z"}}, {"_id": {"$oid": "6837ca26f04f1fe4feb73c7f"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:54.326792", "message": "Experiment Fail\n--------------------------------------------------\nq99-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:54.606Z"}}, {"_id": {"$oid": "6837ca26f04f1fe4feb73c80"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:54.065491", "message": "Experiment Fail\n--------------------------------------------------\nq89-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:54.61Z"}}, {"_id": {"$oid": "6837ca26f04f1fe4feb73c81"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:54.312830", "message": "Experiment Fail\n--------------------------------------------------\nq91-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:54.728Z"}}, {"_id": {"$oid": "6837ca26f04f1fe4feb73c82"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:54.316819", "message": "Experiment Fail\n--------------------------------------------------\nq94-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:54.738Z"}}, {"_id": {"$oid": "6837ca26f04f1fe4feb73c83"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:54.320808", "message": "Experiment Fail\n--------------------------------------------------\nq96-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:54.739Z"}}, {"_id": {"$oid": "6837ca26f04f1fe4feb73c84"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:54.324798", "message": "Experiment Fail\n--------------------------------------------------\nq98-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:54.739Z"}}, {"_id": {"$oid": "6837ca26f04f1fe4feb73c85"}, "username": "cali_243", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-05-29 10:44:54.308840", "message": "Experiment Fail\n--------------------------------------------------\nq9-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"D:\\code\\Y3-Online\\AutoCalibration\\pyqcat-apps\\pyQCat\\experiments\\base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T10:44:54.74Z"}}, {"_id": {"$oid": "6837ea2ff04f1fe4feb73d02"}, "username": "job", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-29 13:01:34.662196", "message": "Experiment Fail\n--------------------------------------------------\nq1-DetuneCalibration\n--------------------------------------------------\n<Exp(DetuneCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(DetuneCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T13:01:35.547Z"}}, {"_id": {"$oid": "6837ea4ef04f1fe4feb73d05"}, "username": "job", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-29 13:02:05.616989", "message": "Experiment Fail\n--------------------------------------------------\nq1-XpiDetection\n--------------------------------------------------\n<Exp(XpiDetection) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(XpiDetection) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T13:02:06.502Z"}}, {"_id": {"$oid": "6837eb10f04f1fe4feb73d08"}, "username": "job", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-29 13:05:19.204385", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerDetuneCalibration\n--------------------------------------------------\n<Exp(CouplerDetuneCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(CouplerDetuneCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T13:05:20.069Z"}}, {"_id": {"$oid": "6837eb10f04f1fe4feb73d09"}, "username": "job", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-29 13:05:19.323166", "message": "Experiment Fail\n--------------------------------------------------\nq1-XYZTimingComposite\n--------------------------------------------------\n<Exp(XYZTimingComposite) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(XYZTimingComposite) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T13:05:20.181Z"}}, {"_id": {"$oid": "6837eb10f04f1fe4feb73d0a"}, "username": "job", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-29 13:05:19.435373", "message": "Experiment Fail\n--------------------------------------------------\nq1-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T13:05:20.293Z"}}, {"_id": {"$oid": "6837eb10f04f1fe4feb73d0b"}, "username": "job", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-29 13:05:19.551776", "message": "Experiment Fail\n--------------------------------------------------\nq1c1-7-ZZShiftSweetPointCalibrationNew\n--------------------------------------------------\n<Exp(ZZShiftSweetPointCalibrationNew) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(ZZShiftSweetPointCalibrationNew) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T13:05:20.408Z"}}, {"_id": {"$oid": "683811eaf04f1fe4feb73d23"}, "username": "job", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-29 15:51:05.687262", "message": "Experiment Fail\n--------------------------------------------------\nq1-DetuneCalibration\n--------------------------------------------------\n<Exp(DetuneCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(DetuneCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T15:51:06.13Z"}}, {"_id": {"$oid": "68381209f04f1fe4feb73d26"}, "username": "job", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-29 15:51:37.369621", "message": "Experiment Fail\n--------------------------------------------------\nq1-XpiDetection\n--------------------------------------------------\n<Exp(XpiDetection) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(XpiDetection) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T15:51:37.809Z"}}, {"_id": {"$oid": "683812ccf04f1fe4feb73d29"}, "username": "job", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-29 15:54:51.788554", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerDetuneCalibration\n--------------------------------------------------\n<Exp(CouplerDetuneCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(CouplerDetuneCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T15:54:52.196Z"}}, {"_id": {"$oid": "683812ccf04f1fe4feb73d2a"}, "username": "job", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-29 15:54:51.921191", "message": "Experiment Fail\n--------------------------------------------------\nq1-XYZTimingComposite\n--------------------------------------------------\n<Exp(XYZTimingComposite) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(XYZTimingComposite) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T15:54:52.329Z"}}, {"_id": {"$oid": "683812ccf04f1fe4feb73d2b"}, "username": "job", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-29 15:54:52.043808", "message": "Experiment Fail\n--------------------------------------------------\nq1-VoltageDriftGradientCalibration\n--------------------------------------------------\n<Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(VoltageDriftGradientCalibration) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T15:54:52.455Z"}}, {"_id": {"$oid": "683812ccf04f1fe4feb73d2c"}, "username": "job", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-29 15:54:52.182115", "message": "Experiment Fail\n--------------------------------------------------\nq1c1-7-ZZShiftSweetPointCalibrationNew\n--------------------------------------------------\n<Exp(ZZShiftSweetPointCalibrationNew) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 196, in run\n    self._validate_options()\n  File \"/home/<USER>/pyqcat-monster/pyQCat/experiments/base_experiment.py\", line 945, in _validate_options\n    raise ExperimentOptionsError(self, msg=\"No set x data, maybe experiment options set error!\")\npyQCat.errors.ExperimentOptionsError: <Exp(ZZShiftSweetPointCalibrationNew) experiment options error> | No set x data, maybe experiment options set error! | Please check options!\n", "version": "monster | ******** | B", "tackle": false, "create_time": {"$date": "2025-05-29T15:54:52.583Z"}}]