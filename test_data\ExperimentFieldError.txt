_id	message
682d838fa77f347882573abe	Experiment Fail
--------------------------------------------------
q61-ProcessTomographyV2
--------------------------------------------------
<Exp(ProcessTomographyV2) field error> | IQ discriminator is empty!
--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 602, in run_experiment
    self._check_options()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\single\error_quantification\new_process_tomogrphy.py", line 125, in _check_options
    raise ExperimentFieldError(self, msg="IQ discriminator is empty!")
pyQCat.errors.ExperimentFieldError: <Exp(ProcessTomographyV2) field error> | IQ discriminator is empty!
