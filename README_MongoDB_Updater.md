# MongoDB批量更新脚本

## 概述

这是一个用于批量更新MongoDB数据库中记录的Python脚本。脚本可以根据txt文件中指定行范围的_id，批量更新数据库中对应记录的tackle字段。

## 功能特性

- ✅ 支持从txt文件中提取指定行范围的_id
- ✅ 批量更新MongoDB中的tackle字段
- ✅ 完整的错误处理和日志记录
- ✅ 进度显示和执行摘要
- ✅ 支持命令行参数和交互式模式
- ✅ 自动处理ObjectId格式验证
- ✅ 支持不同数据类型的tackle字段强制更新

## 文件结构

```
├── batch_update_mongodb.py    # 主脚本文件
├── example_usage.py          # 使用示例脚本
├── db_config.json           # 数据库配置文件
├── txt_data/               # txt数据文件目录
│   ├── AttributeError.txt
│   ├── AssertionError.txt
│   └── ...
└── mongodb_update.log      # 日志文件（运行后生成）
```

## 环境要求

### Python版本
- Python 3.6+

### 依赖包
```bash
pip install pymongo
```

## 配置文件

确保`db_config.json`文件存在并包含正确的数据库连接信息：

```json
{
  "host": "***********",
  "port": 27017,
  "db_name": "UserData",
  "collection_name": "ServerMessage",
  "username": "bylz",
  "password": "fjsaoJOIjiojj28hjj",
  "authSource": "admin"
}
```

## 使用方法

### 方法1: 命令行模式

```bash
# 基本用法
python batch_update_mongodb.py --file txt_data/AttributeError.txt --start 2 --end 49 --tackle 1

# 完整参数
python batch_update_mongodb.py \
    --file txt_data/AttributeError.txt \
    --start 2 \
    --end 49 \
    --tackle 1 \
    --config db_config.json
```

#### 命令行参数说明

| 参数 | 简写 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| --file | -f | ✅ | 无 | txt文件路径 |
| --start | -s | ✅ | 无 | 起始行号（从1开始） |
| --end | -e | ✅ | 无 | 结束行号（包含） |
| --tackle | -t | ❌ | 1 | tackle字段的目标值 |
| --config | -c | ❌ | db_config.json | 数据库配置文件路径 |

### 方法2: 交互式模式

```bash
python example_usage.py
```

运行后选择模式：
- 选择 `1`: 使用预设参数的示例模式
- 选择 `2`: 交互式输入参数模式

### 方法3: 编程方式

```python
from batch_update_mongodb import MongoDBUpdater

# 创建更新器实例
updater = MongoDBUpdater('db_config.json')

try:
    # 连接数据库
    if updater.connect():
        # 解析文件获取ID列表
        object_ids = updater.parse_txt_file('txt_data/AttributeError.txt', 2, 49)
        
        # 执行批量更新
        stats = updater.update_tackle_field(object_ids, 1)
        
        # 打印摘要
        updater.print_summary(stats)
finally:
    updater.disconnect()
```

## 输入文件格式

txt文件应该是制表符分隔的格式，第一列为_id，第二列为message：

```
_id	message
683821adf04f1fe4feb73d40	Experiment Crash
68382f44f04f1fe4feb73d43	Experiment Crash
...
```

## 日志记录

脚本会生成详细的日志记录：

- **控制台输出**: 实时显示执行进度和重要信息
- **日志文件**: `mongodb_update.log` 包含完整的执行日志

日志级别：
- `INFO`: 一般信息和进度
- `WARNING`: 警告信息（如未找到记录）
- `ERROR`: 错误信息
- `DEBUG`: 详细调试信息

## 执行结果

脚本执行完成后会显示详细的摘要信息：

```
==================================================
更新摘要:
总计处理: 48 条记录
成功更新: 45 条记录
未找到记录: 2 条记录
发生错误: 1 条记录
==================================================
```

## 错误处理

脚本包含完善的错误处理机制：

1. **配置文件错误**: 检查配置文件是否存在和格式是否正确
2. **数据库连接错误**: 处理连接超时和认证失败
3. **文件解析错误**: 处理文件不存在和格式错误
4. **ObjectId格式错误**: 验证和转换ObjectId格式
5. **数据库操作错误**: 处理更新操作中的各种异常

## 安全注意事项

1. **备份数据**: 在执行批量更新前，建议备份相关数据
2. **测试环境**: 先在测试环境中验证脚本功能
3. **权限控制**: 确保数据库用户具有适当的更新权限
4. **配置安全**: 保护数据库配置文件，避免密码泄露

## 性能优化

- 脚本使用批量更新操作，每100条记录显示一次进度
- 支持大文件处理，内存占用较低
- 包含连接池和超时设置，提高稳定性

## 故障排除

### 常见问题

1. **连接失败**
   - 检查网络连接和防火墙设置
   - 验证数据库配置信息
   - 确认数据库服务是否运行

2. **认证失败**
   - 检查用户名和密码
   - 验证authSource设置
   - 确认用户权限

3. **文件解析错误**
   - 检查文件路径是否正确
   - 验证文件格式（制表符分隔）
   - 确认行号范围是否有效

4. **ObjectId格式错误**
   - 检查_id字段格式
   - 确认是否为有效的24位十六进制字符串

## 扩展功能

脚本设计为易于扩展，可以根据需要添加：

- 支持更多字段的批量更新
- 支持不同的文件格式（CSV、Excel等）
- 支持条件更新和复杂查询
- 支持多数据库和多集合操作

## 版本历史

- v1.0: 初始版本，支持基本的批量更新功能
