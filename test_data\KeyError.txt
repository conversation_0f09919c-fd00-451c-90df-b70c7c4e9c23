_id	message
682edf58f04f1fe4feb73698	Experiment Fail
--------------------------------------------------
q97~c18-24-ZExp
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 490, in run
    self._build_chimera_data()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 406, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 490, in run
    self._build_chimera_data()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 406, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'


682edfcaf04f1fe4feb73699	Experiment Fail
--------------------------------------------------
q97~c18-24-ZExp
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 490, in run
    self._build_chimera_data()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 406, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 490, in run
    self._build_chimera_data()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 406, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'


682ee01ef04f1fe4feb7369a	Experiment Fail
--------------------------------------------------
q97~c18-24-ZExp
--------------------------------------------------
<Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 490, in run
    self._build_chimera_data()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 406, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'c102-107'

--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 606, in run_experiment
    require_id = await self._async_compile()
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 547, in _async_compile
    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\data_client.py", line 395, in register_compile_result
    raise CompilerExperimentError(msg=str(result.message))
pyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 490, in run
    self._build_chimera_data()
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 406, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'c102-107'


685e6d74a0b5e01b01b32576	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(4)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 404, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d74952d8dd2e7b3242c	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(7)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 404, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d74a0b5e01b01b32577	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(5)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 404, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d74952d8dd2e7b3242d	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 404, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d74a0b5e01b01b32578	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(2)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 404, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d74952d8dd2e7b3242e	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(10)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 404, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d74a0b5e01b01b32579	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(3)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 404, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d74952d8dd2e7b3242f	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(8)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 404, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d74a0b5e01b01b3257a	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(6)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 404, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d74952d8dd2e7b32430	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(9)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 404, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d75952d8dd2e7b32431	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(11)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 404, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d75952d8dd2e7b32432	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(12)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 404, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d76a0b5e01b01b3257b	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(13)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 404, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d76952d8dd2e7b32433	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(14)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 404, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d76a0b5e01b01b3257c	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(15)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 404, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d76952d8dd2e7b32434	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(16)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 404, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d76a0b5e01b01b3257d	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(17)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 404, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d76952d8dd2e7b32435	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(18)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 404, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d76a0b5e01b01b3257e	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(20)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 404, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d76952d8dd2e7b32436	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(19)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 404, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d76a0b5e01b01b3257f	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(21)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 404, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d76952d8dd2e7b32437	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(22)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 404, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d77a0b5e01b01b32580	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(23)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 404, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d77a0b5e01b01b32581	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(24)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 404, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d77a0b5e01b01b32582	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(25)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 404, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d77952d8dd2e7b32438	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(26)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 404, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d77a0b5e01b01b32583	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(28)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 404, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d77952d8dd2e7b32439	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(27)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 404, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d77a0b5e01b01b32584	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(29)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 404, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d77952d8dd2e7b3243a	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(30)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 404, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d78952d8dd2e7b3243b	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(31)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 404, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d78952d8dd2e7b3243c	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(32)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 404, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d78a0b5e01b01b32585	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(33)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 404, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d7ca0b5e01b01b3258b	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d7c952d8dd2e7b32441	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(4)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d7ca0b5e01b01b3258c	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(9)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d7c952d8dd2e7b32442	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(5)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d7ca0b5e01b01b3258d	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(7)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d7c952d8dd2e7b32443	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(2)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d7ca0b5e01b01b3258e	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(3)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d7c952d8dd2e7b32444	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(8)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d7ca0b5e01b01b3258f	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(6)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d7c952d8dd2e7b32445	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(10)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d7d952d8dd2e7b32446	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(11)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d7d952d8dd2e7b32447	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(12)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d7da0b5e01b01b32590	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(13)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d7d952d8dd2e7b32448	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(14)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d7da0b5e01b01b32591	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(15)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d7e952d8dd2e7b32449	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(16)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d7ea0b5e01b01b32592	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(17)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d7e952d8dd2e7b3244a	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(18)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d7ea0b5e01b01b32593	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(19)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d7e952d8dd2e7b3244b	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(20)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d7e952d8dd2e7b3244c	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(21)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d7fa0b5e01b01b32594	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(22)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d7f952d8dd2e7b3244d	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(23)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d7f952d8dd2e7b3244e	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(24)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d7fa0b5e01b01b32595	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(25)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d7fa0b5e01b01b32596	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(26)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d7f952d8dd2e7b3244f	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(27)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d7fa0b5e01b01b32597	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(28)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d7f952d8dd2e7b32450	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(29)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d7fa0b5e01b01b32598	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(30)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d80952d8dd2e7b32451	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(31)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d81952d8dd2e7b32452	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(32)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d81952d8dd2e7b32453	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(33)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d84952d8dd2e7b32458	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(3)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d84952d8dd2e7b32459	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(4)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d84a0b5e01b01b3259d	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d84952d8dd2e7b3245a	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(9)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d84a0b5e01b01b3259e	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(7)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d84952d8dd2e7b3245b	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(2)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d84a0b5e01b01b3259f	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(5)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d85952d8dd2e7b3245c	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(10)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d85a0b5e01b01b325a0	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(8)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d85952d8dd2e7b3245d	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(6)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d85a0b5e01b01b325a1	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(11)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d86a0b5e01b01b325a2	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(12)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d86952d8dd2e7b3245e	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(13)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d86a0b5e01b01b325a3	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(14)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d86952d8dd2e7b3245f	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(16)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d86a0b5e01b01b325a4	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(15)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d86952d8dd2e7b32460	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(17)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d86a0b5e01b01b325a5	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(18)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d86952d8dd2e7b32461	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(19)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d86a0b5e01b01b325a6	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(20)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d87a0b5e01b01b325a7	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(21)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d87952d8dd2e7b32462	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(22)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d87952d8dd2e7b32463	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(23)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d88952d8dd2e7b32464	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(24)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d88952d8dd2e7b32465	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(25)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d88952d8dd2e7b32466	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(28)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d88a0b5e01b01b325a8	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(26)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d88952d8dd2e7b32467	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(27)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d88a0b5e01b01b325a9	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(29)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d88a0b5e01b01b325aa	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(30)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d88a0b5e01b01b325ab	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(31)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d88a0b5e01b01b325ac	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(32)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d89952d8dd2e7b32468	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(33)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d8da0b5e01b01b325b2	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d8d952d8dd2e7b3246c	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(2)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d8da0b5e01b01b325b3	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(3)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d8d952d8dd2e7b3246d	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(8)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d8da0b5e01b01b325b4	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(6)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d8d952d8dd2e7b3246e	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(10)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d8da0b5e01b01b325b5	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(4)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d8d952d8dd2e7b3246f	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(5)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d8da0b5e01b01b325b6	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(7)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d8d952d8dd2e7b32470	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(9)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d8e952d8dd2e7b32471	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(11)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d8e952d8dd2e7b32472	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(12)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d8fa0b5e01b01b325b7	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(14)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d8f952d8dd2e7b32473	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(13)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d8fa0b5e01b01b325b8	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(15)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d8f952d8dd2e7b32474	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(16)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d8fa0b5e01b01b325b9	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(17)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d8f952d8dd2e7b32475	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(18)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d8fa0b5e01b01b325ba	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(20)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d8f952d8dd2e7b32476	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(19)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d8fa0b5e01b01b325bb	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(21)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d8f952d8dd2e7b32477	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(22)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d90a0b5e01b01b325bc	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(23)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d90952d8dd2e7b32478	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(24)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d90a0b5e01b01b325bd	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(25)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d90952d8dd2e7b32479	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(26)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d90a0b5e01b01b325be	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(27)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d90952d8dd2e7b3247a	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(28)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d90952d8dd2e7b3247b	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(29)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d90952d8dd2e7b3247c	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(30)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d91952d8dd2e7b3247d	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(31)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d91a0b5e01b01b325bf	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(32)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d91952d8dd2e7b3247e	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(33)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d95952d8dd2e7b32484	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(2)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d95a0b5e01b01b325c4	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d95952d8dd2e7b32485	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(4)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d95a0b5e01b01b325c5	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(5)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d95952d8dd2e7b32486	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(7)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d95a0b5e01b01b325c6	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(3)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d95952d8dd2e7b32487	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(10)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d95a0b5e01b01b325c7	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(8)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d95952d8dd2e7b32488	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(6)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d95a0b5e01b01b325c8	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(9)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d96a0b5e01b01b325c9	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(11)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d96952d8dd2e7b32489	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(12)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d97952d8dd2e7b3248a	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(13)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d97a0b5e01b01b325ca	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(14)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d97952d8dd2e7b3248b	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(15)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d97a0b5e01b01b325cb	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(17)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d97a0b5e01b01b325cc	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(16)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d97a0b5e01b01b325cd	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(18)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d97952d8dd2e7b3248c	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(19)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d97a0b5e01b01b325ce	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(20)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d97a0b5e01b01b325cf	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(21)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d97a0b5e01b01b325d0	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(22)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d98a0b5e01b01b325d1	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(23)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d98a0b5e01b01b325d2	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(24)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d98952d8dd2e7b3248d	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(25)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d98a0b5e01b01b325d3	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(26)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d98952d8dd2e7b3248e	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(27)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d98952d8dd2e7b3248f	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(28)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d98952d8dd2e7b32490	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(29)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d98952d8dd2e7b32491	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(30)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d98a0b5e01b01b325d4	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(31)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d98952d8dd2e7b32492	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(32)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d99952d8dd2e7b32493	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(33)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d9c952d8dd2e7b3249a	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(2)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d9ca0b5e01b01b325d8	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(4)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d9d952d8dd2e7b3249b	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(7)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d9da0b5e01b01b325d9	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(5)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d9d952d8dd2e7b3249c	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(3)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d9da0b5e01b01b325da	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d9d952d8dd2e7b3249d	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(8)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d9da0b5e01b01b325db	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(10)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d9d952d8dd2e7b3249e	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(9)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d9d952d8dd2e7b3249f	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(6)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d9e952d8dd2e7b324a0	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(11)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d9e952d8dd2e7b324a1	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(12)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d9e952d8dd2e7b324a2	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(13)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d9ea0b5e01b01b325dc	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(14)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d9ea0b5e01b01b325dd	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(15)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d9e952d8dd2e7b324a3	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(16)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d9ea0b5e01b01b325de	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(17)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d9e952d8dd2e7b324a4	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(18)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d9f952d8dd2e7b324a5	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(20)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d9f952d8dd2e7b324a6	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(19)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d9f952d8dd2e7b324a7	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(21)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d9f952d8dd2e7b324a8	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(22)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d9fa0b5e01b01b325df	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(23)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d9fa0b5e01b01b325e0	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(24)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6d9f952d8dd2e7b324a9	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(25)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da0952d8dd2e7b324aa	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(26)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da0a0b5e01b01b325e1	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(27)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da0952d8dd2e7b324ab	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(29)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da0a0b5e01b01b325e2	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(28)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da0a0b5e01b01b325e3	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(30)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da0952d8dd2e7b324ac	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(31)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da0952d8dd2e7b324ad	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(32)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da0952d8dd2e7b324ae	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(33)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da4a0b5e01b01b325e9	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(4)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da4952d8dd2e7b324b3	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da4952d8dd2e7b324b4	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(5)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da4a0b5e01b01b325ea	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(7)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da5a0b5e01b01b325eb	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(3)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da5952d8dd2e7b324b5	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(9)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da5952d8dd2e7b324b6	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(2)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da5a0b5e01b01b325ec	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(10)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da5952d8dd2e7b324b7	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(6)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da5a0b5e01b01b325ed	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(8)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da5a0b5e01b01b325ee	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(11)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da5952d8dd2e7b324b8	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(12)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da6952d8dd2e7b324b9	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(13)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da6a0b5e01b01b325ef	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(14)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da6a0b5e01b01b325f0	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(15)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da6952d8dd2e7b324ba	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(16)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da6952d8dd2e7b324bb	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(17)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da6a0b5e01b01b325f1	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(18)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da6952d8dd2e7b324bc	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(19)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da6952d8dd2e7b324bd	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(20)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da7a0b5e01b01b325f2	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(21)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da7952d8dd2e7b324be	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(22)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da7a0b5e01b01b325f3	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(23)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da7a0b5e01b01b325f4	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(24)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da7952d8dd2e7b324bf	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(25)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da7a0b5e01b01b325f5	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(26)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da7a0b5e01b01b325f6	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(27)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da7a0b5e01b01b325f7	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(28)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da8952d8dd2e7b324c0	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(29)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da8a0b5e01b01b325f8	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(30)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da8a0b5e01b01b325f9	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(31)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da8952d8dd2e7b324c1	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(32)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6da8a0b5e01b01b325fa	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(33)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6daca0b5e01b01b325ff	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(4)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6dac952d8dd2e7b324c7	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(6)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6daca0b5e01b01b32600	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(9)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6dac952d8dd2e7b324c8	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(2)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6daca0b5e01b01b32601	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(7)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6dac952d8dd2e7b324c9	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6daca0b5e01b01b32602	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(5)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6dac952d8dd2e7b324ca	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(3)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6daca0b5e01b01b32603	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(10)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6dac952d8dd2e7b324cb	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(8)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6dada0b5e01b01b32604	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(11)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6dada0b5e01b01b32605	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(12)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6dae952d8dd2e7b324cc	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(13)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6daea0b5e01b01b32606	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(14)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6daea0b5e01b01b32607	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(15)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6dae952d8dd2e7b324cd	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(16)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6daea0b5e01b01b32608	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(17)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6dae952d8dd2e7b324ce	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(18)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6daea0b5e01b01b32609	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(19)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6dae952d8dd2e7b324cf	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(20)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6dae952d8dd2e7b324d0	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(21)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6dafa0b5e01b01b3260a	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(22)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6daf952d8dd2e7b324d1	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(23)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6daf952d8dd2e7b324d2	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(24)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6daf952d8dd2e7b324d3	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(25)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6daf952d8dd2e7b324d4	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(26)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6daf952d8dd2e7b324d5	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(27)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6daf952d8dd2e7b324d6	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(28)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6dafa0b5e01b01b3260b	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(29)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6daf952d8dd2e7b324d7	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(30)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6db0a0b5e01b01b3260c	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(31)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6db0a0b5e01b01b3260d	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(32)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

685e6db0a0b5e01b01b3260e	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(33)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68620bf19c3e8a30651e9823	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(SingleShot_01) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\Code\wxy\pyqcat-visage\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\Code\wxy\pyqcat-visage\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68651249a47f89e2f61ea526	Experiment Crash
--------------------------------------------------
Circuit-CircuitComposite-Circuit(1-1)-None-686512b080915fdf628a86d6
--------------------------------------------------
'q'
--------------------------------------------------
Traceback (most recent call last):
  File "/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py", line 608, in run_experiment
    require_id = await self._async_compile()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py", line 541, in _async_compile
    experiment_file: ExperimentFile = build_experiment_message(exp_env)
                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py", line 142, in build_experiment_message
    exp_env.set_xy_pulses(builder)
  File "/home/<USER>/code/pyqcat-monster/pyQCat/experiments/single/undetermine/circuit.py", line 603, in set_xy_pulses
    builder.play_pulse("XY", unit_map[qubit_name], pulse)
                             ~~~~~~~~^^^^^^^^^^^^
KeyError: 'q'

68687ebe9c3e8a30651ea737	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687ec0a47f89e2f61eab9c	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687ec29c3e8a30651ea738	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687ec4a47f89e2f61eab9d	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687ec69c3e8a30651ea739	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687ec8a47f89e2f61eab9e	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687eca9c3e8a30651ea73a	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687ecca47f89e2f61eab9f	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687ece9c3e8a30651ea73b	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687ed0a47f89e2f61eaba0	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687ed29c3e8a30651ea73c	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687ed4a47f89e2f61eaba1	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687ed69c3e8a30651ea73d	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687ed8a47f89e2f61eaba2	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687eda9c3e8a30651ea73e	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687edca47f89e2f61eaba3	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687ede9c3e8a30651ea73f	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687ee0a47f89e2f61eaba4	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687ee29c3e8a30651ea740	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687ee4a47f89e2f61eaba5	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687ee69c3e8a30651ea741	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687ee8a47f89e2f61eaba6	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687eea9c3e8a30651ea742	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687eeba47f89e2f61eaba7	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687eed9c3e8a30651ea743	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687eefa47f89e2f61eaba8	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687ef19c3e8a30651ea744	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687ef3a47f89e2f61eaba9	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687ef59c3e8a30651ea745	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687ef7a47f89e2f61eabaa	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687ef99c3e8a30651ea746	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687efba47f89e2f61eabab	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687efd9c3e8a30651ea747	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687effa47f89e2f61eabac	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687f019c3e8a30651ea748	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687f03a47f89e2f61eabad	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687f059c3e8a30651ea749	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687f07a47f89e2f61eabae	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687f099c3e8a30651ea74a	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687f0ba47f89e2f61eabaf	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687f0c9c3e8a30651ea74b	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687f0ea47f89e2f61eabb0	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

68687f109c3e8a30651ea74c	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

686c7e635f0e2b5dc933f2ab	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

686c7e6559c875935d33f2a6	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

686c7e675f0e2b5dc933f2ac	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

686c7e6959c875935d33f2a7	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

686c7e6b5f0e2b5dc933f2ad	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

686c7e6d59c875935d33f2a8	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

686c7e6f5f0e2b5dc933f2ae	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

686c7e7159c875935d33f2a9	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

686c7e735f0e2b5dc933f2af	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

686c7e7559c875935d33f2aa	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

686c7e775f0e2b5dc933f2b0	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

686c7e7959c875935d33f2ab	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

686c7e7b5f0e2b5dc933f2b1	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

686c7e7d59c875935d33f2ac	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

686c7e7f5f0e2b5dc933f2b2	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

686c7e8159c875935d33f2ad	Parallel Merge Fail
--------------------------------------------------
Parallel-Child-Group-Exp(CavityFreqSpectrum) | default | Count-Identity(1)
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 493, in run
    self._build_chimera_data()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\experiment_compiler.py", line 409, in _build_chimera_data
    awg_bias=self.common.ac_bias[bit][-1],
KeyError: 'q103'

6889ef33f6689fe5521f661d	Experiment Crash
--------------------------------------------------
BUS-5-FindBusCavityFreq-Segm-6889ef71424c4e831609a583
--------------------------------------------------
'chi_square'
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 329, in run
    self._run_experiment()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\library\find_cavity_freq.py", line 524, in _run_experiment
    self._check_bus()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\library\find_cavity_freq.py", line 313, in _check_bus
    self._check_fit_q()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\library\find_cavity_freq.py", line 358, in _check_fit_q
    res = Q_fit(
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\s21_circle_fit_triton.py", line 540, in Q_fit
    chi_square = 'chi_square=%.2f' % port1.fit_result['chi_square']
KeyError: 'chi_square'

6896b830150663897c883b31	Experiment Crash
--------------------------------------------------
q4-DetuneCalibration-APEComposite(1-2)-RoughScan-6896b8201d282846de26080a
--------------------------------------------------
'P0'
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\experiments\composite_experiment.py", line 518, in run_experiment
    await self._async_composite_run_base()
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\experiments\composite_experiment.py", line 469, in _async_composite_run_base
    await self._async_run_analysis()
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\experiments\composite_experiment.py", line 503, in _async_run_analysis
    self._analysis = run_analysis_process(*args)
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\concurrent\worker\analysis_interface.py", line 257, in run_analysis_process
    analysis_obj.run_analysis()
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\analysis\curve_analysis.py", line 422, in run_analysis
    self._extract_result(better_data_key)
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\analysis\library\ape_composite_analysis.py", line 123, in _extract_result
    cur_x_gap, cur_y_gap = _calculate_gap(point_coin)
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\analysis\library\ape_composite_analysis.py", line 86, in _calculate_gap
    y_data = child_data.y_data[self.options.y_label][x_idx]
KeyError: 'P0'

6896b849150663897c883b33	Experiment Crash
--------------------------------------------------
q4-DetuneCalibration-APEComposite(1-2)-RoughScan-6896b8361d282846de260814
--------------------------------------------------
'P0'
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\experiments\composite_experiment.py", line 518, in run_experiment
    await self._async_composite_run_base()
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\experiments\composite_experiment.py", line 469, in _async_composite_run_base
    await self._async_run_analysis()
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\experiments\composite_experiment.py", line 503, in _async_run_analysis
    self._analysis = run_analysis_process(*args)
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\concurrent\worker\analysis_interface.py", line 257, in run_analysis_process
    analysis_obj.run_analysis()
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\analysis\curve_analysis.py", line 422, in run_analysis
    self._extract_result(better_data_key)
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\analysis\library\ape_composite_analysis.py", line 123, in _extract_result
    cur_x_gap, cur_y_gap = _calculate_gap(point_coin)
  File "D:\code\yxy\naga\0.23.2\pyqcat-visage\pyQCat\analysis\library\ape_composite_analysis.py", line 86, in _calculate_gap
    y_data = child_data.y_data[self.options.y_label][x_idx]
KeyError: 'P0'
