[{"_id": {"$oid": "682d711ca77f3478825739f7"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:18.092586", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(1-41)-Detune=-40\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:20.229Z"}}, {"_id": {"$oid": "682d711ca77f3478825739f8"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:18.179295", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(3-41)-Detune=-36\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:20.291Z"}}, {"_id": {"$oid": "682d711ca77f3478825739f9"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:18.135442", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(2-41)-Detune=-38\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:20.292Z"}}, {"_id": {"$oid": "682d711ca77f3478825739fa"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:18.223149", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(4-41)-Detune=-34\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:20.4Z"}}, {"_id": {"$oid": "682d711ca77f3478825739fb"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:18.265008", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(5-41)-Detune=-32\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:20.401Z"}}, {"_id": {"$oid": "682d711ca77f3478825739fc"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:18.310855", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(6-41)-Detune=-30\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:20.454Z"}}, {"_id": {"$oid": "682d711ca77f3478825739fd"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:18.550055", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(7-41)-Detune=-28\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:20.652Z"}}, {"_id": {"$oid": "682d711ca77f3478825739fe"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:18.595902", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(8-41)-Detune=-26\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:20.744Z"}}, {"_id": {"$oid": "682d711ca77f3478825739ff"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:18.641748", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(9-41)-Detune=-24\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:20.836Z"}}, {"_id": {"$oid": "682d711ca77f347882573a00"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:18.686598", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(10-41)-Detune=-22\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:20.837Z"}}, {"_id": {"$oid": "682d711ca77f347882573a01"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:18.774305", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(12-41)-Detune=-18\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:20.87Z"}}, {"_id": {"$oid": "682d711ca77f347882573a02"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:18.729455", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(11-41)-Detune=-20\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:20.876Z"}}, {"_id": {"$oid": "682d711ca77f347882573a03"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:18.820151", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(13-41)-Detune=-16\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:20.913Z"}}, {"_id": {"$oid": "682d711da77f347882573a04"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:18.863008", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(14-41)-Detune=-14\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:21Z"}}, {"_id": {"$oid": "682d711da77f347882573a05"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:18.910848", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(15-41)-Detune=-12\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:21.065Z"}}, {"_id": {"$oid": "682d711da77f347882573a06"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:18.960681", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(16-41)-Detune=-10\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:21.066Z"}}, {"_id": {"$oid": "682d711da77f347882573a07"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:19.005532", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(17-41)-Detune=-8\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:21.134Z"}}, {"_id": {"$oid": "682d711da77f347882573a08"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:19.050381", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(18-41)-Detune=-6\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:21.153Z"}}, {"_id": {"$oid": "682d711da77f347882573a09"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:19.208851", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(19-41)-Detune=-4\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:21.355Z"}}, {"_id": {"$oid": "682d711da77f347882573a0a"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:19.258684", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(20-41)-Detune=-2\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:21.45Z"}}, {"_id": {"$oid": "682d711da77f347882573a0b"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:19.304531", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(21-41)-Detune=0\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:21.483Z"}}, {"_id": {"$oid": "682d711da77f347882573a0c"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:19.390244", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(23-41)-Detune=4\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:21.485Z"}}, {"_id": {"$oid": "682d711da77f347882573a0d"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:19.346391", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(22-41)-Detune=2\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:21.499Z"}}, {"_id": {"$oid": "682d711da77f347882573a0e"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:19.440077", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(24-41)-Detune=6\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:21.531Z"}}, {"_id": {"$oid": "682d711da77f347882573a0f"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:19.490908", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(25-41)-Detune=8\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:21.632Z"}}, {"_id": {"$oid": "682d711da77f347882573a10"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:19.538747", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(26-41)-Detune=10\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:21.712Z"}}, {"_id": {"$oid": "682d711da77f347882573a11"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:19.586587", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(27-41)-Detune=12\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:21.716Z"}}, {"_id": {"$oid": "682d711da77f347882573a12"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:19.672814", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(29-41)-Detune=16\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:21.756Z"}}, {"_id": {"$oid": "682d711da77f347882573a13"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:19.629957", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(28-41)-Detune=14\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:21.757Z"}}, {"_id": {"$oid": "682d711da77f347882573a14"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:19.719657", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(30-41)-Detune=18\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:21.795Z"}}, {"_id": {"$oid": "682d711da77f347882573a15"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:19.875137", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(31-41)-Detune=20\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:21.982Z"}}, {"_id": {"$oid": "682d711ea77f347882573a16"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:19.923974", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(32-41)-Detune=22\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:22.082Z"}}, {"_id": {"$oid": "682d711ea77f347882573a17"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:19.973807", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(33-41)-Detune=24\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:22.138Z"}}, {"_id": {"$oid": "682d711ea77f347882573a18"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:20.019653", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(34-41)-Detune=26\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:22.139Z"}}, {"_id": {"$oid": "682d711ea77f347882573a19"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:20.108357", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(36-41)-Detune=30\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:22.183Z"}}, {"_id": {"$oid": "682d711ea77f347882573a1a"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:20.063507", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(35-41)-Detune=28\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:22.239Z"}}, {"_id": {"$oid": "682d711ea77f347882573a1b"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:20.155200", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(37-41)-Detune=32\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:22.285Z"}}, {"_id": {"$oid": "682d711ea77f347882573a1c"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:20.201047", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(38-41)-Detune=34\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:22.286Z"}}, {"_id": {"$oid": "682d711ea77f347882573a1d"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:20.248887", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(39-41)-Detune=36\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:22.471Z"}}, {"_id": {"$oid": "682d711ea77f347882573a1e"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:20.294733", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(40-41)-Detune=38\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:22.472Z"}}, {"_id": {"$oid": "682d711ea77f347882573a1f"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 14:25:20.341577", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(41-41)-Detune=40\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T14:22:22.472Z"}}, {"_id": {"$oid": "682d8395a77f347882573abf"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 15:44:06.872531", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerRabiScanWidth\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T15:41:09.029Z"}}, {"_id": {"$oid": "682d83eba77f347882573ac1"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-21 15:45:33.350225", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerRabiScanWidthDetune\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 488, in run\n    self._validate_and_merge()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": 0, "create_time": {"$date": "2025-05-21T15:42:35.472Z"}}, {"_id": {"$oid": "682edf58f04f1fe4feb73698"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-22 16:27:54.593341", "message": "Experiment Fail\n--------------------------------------------------\nq97~c18-24-ZExp\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 490, in run\n    self._build_chimera_data()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 406, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 490, in run\n    self._build_chimera_data()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 406, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-22T16:24:56.829Z"}}, {"_id": {"$oid": "682edfcaf04f1fe4feb73699"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-22 16:29:48.647565", "message": "Experiment Fail\n--------------------------------------------------\nq97~c18-24-ZExp\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 490, in run\n    self._build_chimera_data()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 406, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 490, in run\n    self._build_chimera_data()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 406, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'q103'\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-22T16:26:50.886Z"}}, {"_id": {"$oid": "682ee01ef04f1fe4feb7369a"}, "username": "monitor_y4", "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）", "env_name": "Y4", "level": 10, "time": "2025-05-22 16:31:12.353159", "message": "Experiment Fail\n--------------------------------------------------\nq97~c18-24-ZExp\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 490, in run\n    self._build_chimera_data()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 406, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'c102-107'\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 606, in run_experiment\n    require_id = await self._async_compile()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 547, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 490, in run\n    self._build_chimera_data()\n  File \"F:\\Monitor\\pyqcat-apps\\pyQCat\\concurrent\\worker\\experiment_compiler.py\", line 406, in _build_chimera_data\n    awg_bias=self.common.ac_bias[bit][-1],\nKeyError: 'c102-107'\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-22T16:28:14.58Z"}}, {"_id": {"$oid": "683410a3f04f1fe4feb7384f"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:57:56.319778", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerRabiScanWidth\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:56:35.437Z"}}, {"_id": {"$oid": "683410a4f04f1fe4feb73850"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:57:57.125757", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerRabiScanWidthDetune\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\n<PERSON>back (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:56:36.243Z"}}, {"_id": {"$oid": "683410fef04f1fe4feb7385a"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:27.223296", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(1-41)-Detune=-40\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:06.33Z"}}, {"_id": {"$oid": "683410fef04f1fe4feb7385b"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:27.282908", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(2-41)-Detune=-38\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:06.403Z"}}, {"_id": {"$oid": "683410fef04f1fe4feb7385c"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:27.340405", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(3-41)-Detune=-36\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:06.503Z"}}, {"_id": {"$oid": "683410fef04f1fe4feb7385d"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:27.401318", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(4-41)-Detune=-34\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:06.504Z"}}, {"_id": {"$oid": "683410fef04f1fe4feb7385e"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:27.470983", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(5-41)-Detune=-32\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:06.699Z"}}, {"_id": {"$oid": "683410fef04f1fe4feb7385f"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:27.558815", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(6-41)-Detune=-30\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:06.706Z"}}, {"_id": {"$oid": "683410fef04f1fe4feb73860"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:27.656846", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(7-41)-Detune=-28\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:06.762Z"}}, {"_id": {"$oid": "683410fef04f1fe4feb73861"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:27.726113", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(8-41)-Detune=-26\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:06.847Z"}}, {"_id": {"$oid": "683410fef04f1fe4feb73862"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:27.800484", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(9-41)-Detune=-24\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:06.888Z"}}, {"_id": {"$oid": "683410fff04f1fe4feb73863"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:27.870771", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(10-41)-Detune=-22\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:07.016Z"}}, {"_id": {"$oid": "683410fff04f1fe4feb73864"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:27.942044", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(11-41)-Detune=-20\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:07.052Z"}}, {"_id": {"$oid": "683410fff04f1fe4feb73865"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:28.184118", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(12-41)-Detune=-18\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:07.377Z"}}, {"_id": {"$oid": "683410fff04f1fe4feb73866"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:28.251504", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(13-41)-Detune=-16\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:07.384Z"}}, {"_id": {"$oid": "683410fff04f1fe4feb73867"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:28.321817", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(14-41)-Detune=-14\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:07.43Z"}}, {"_id": {"$oid": "683410fff04f1fe4feb73868"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:28.386653", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(15-41)-Detune=-12\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:07.509Z"}}, {"_id": {"$oid": "683410fff04f1fe4feb73869"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:28.450401", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(16-41)-Detune=-10\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:07.562Z"}}, {"_id": {"$oid": "683410fff04f1fe4feb7386a"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:28.512714", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(17-41)-Detune=-8\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:07.632Z"}}, {"_id": {"$oid": "683410fff04f1fe4feb7386b"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:28.573537", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(18-41)-Detune=-6\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:07.682Z"}}, {"_id": {"$oid": "683410fff04f1fe4feb7386c"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:28.631430", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(19-41)-Detune=-4\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:07.812Z"}}, {"_id": {"$oid": "683410fff04f1fe4feb7386d"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:28.689809", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(20-41)-Detune=-2\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:07.813Z"}}, {"_id": {"$oid": "683410fff04f1fe4feb7386e"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:28.750421", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(21-41)-Detune=0\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:07.864Z"}}, {"_id": {"$oid": "683410fff04f1fe4feb7386f"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:28.811722", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(22-41)-Detune=2\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:07.902Z"}}, {"_id": {"$oid": "68341100f04f1fe4feb73870"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:29.013691", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(23-41)-Detune=4\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:08.138Z"}}, {"_id": {"$oid": "68341100f04f1fe4feb73871"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:29.074720", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(24-41)-Detune=6\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:08.197Z"}}, {"_id": {"$oid": "68341100f04f1fe4feb73872"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:29.136805", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(25-41)-Detune=8\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:08.284Z"}}, {"_id": {"$oid": "68341100f04f1fe4feb73873"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:29.196443", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(26-41)-Detune=10\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:08.286Z"}}, {"_id": {"$oid": "68341100f04f1fe4feb73874"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:29.257071", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(27-41)-Detune=12\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:08.393Z"}}, {"_id": {"$oid": "68341100f04f1fe4feb73875"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:29.325397", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(28-41)-Detune=14\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:08.435Z"}}, {"_id": {"$oid": "68341100f04f1fe4feb73876"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:29.388120", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(29-41)-Detune=16\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:08.505Z"}}, {"_id": {"$oid": "68341100f04f1fe4feb73877"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:29.449424", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(30-41)-Detune=18\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:08.547Z"}}, {"_id": {"$oid": "68341100f04f1fe4feb73878"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:29.510084", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(31-41)-Detune=20\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:08.623Z"}}, {"_id": {"$oid": "68341100f04f1fe4feb73879"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:29.575080", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(32-41)-Detune=22\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:08.69Z"}}, {"_id": {"$oid": "68341100f04f1fe4feb7387a"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:29.774671", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(33-41)-Detune=24\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:08.885Z"}}, {"_id": {"$oid": "68341100f04f1fe4feb7387b"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:29.835435", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(34-41)-Detune=26\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:08.948Z"}}, {"_id": {"$oid": "68341101f04f1fe4feb7387c"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:29.896283", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(35-41)-Detune=28\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:09.027Z"}}, {"_id": {"$oid": "68341101f04f1fe4feb7387d"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:29.958866", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(36-41)-Detune=30\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:09.091Z"}}, {"_id": {"$oid": "68341101f04f1fe4feb7387e"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:30.017623", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(37-41)-Detune=32\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:09.135Z"}}, {"_id": {"$oid": "68341101f04f1fe4feb7387f"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:30.077978", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(38-41)-Detune=34\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:09.201Z"}}, {"_id": {"$oid": "68341101f04f1fe4feb73880"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:30.133025", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(39-41)-Detune=36\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:09.312Z"}}, {"_id": {"$oid": "68341101f04f1fe4feb73881"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:30.192767", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(40-41)-Detune=38\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:09.316Z"}}, {"_id": {"$oid": "68341101f04f1fe4feb73882"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 14:59:30.262884", "message": "Experiment Fail\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(41-41)-Detune=40\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T14:58:09.381Z"}}, {"_id": {"$oid": "683411d6f04f1fe4feb738a2"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 15:03:03.066594", "message": "Experiment Fail\n--------------------------------------------------\nq11q17c11-17-SlepianLamNum-SlepianLamNumOnce(1-9)-cz_num=0\n--------------------------------------------------\n<Exp(SlepianLamNumOnce) error> | Options lam1_list and lam2_list must not be empty!\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 544, in _async_compile\n    experiment_file: ExperimentFile = build_experiment_message(exp_env)\n                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 129, in build_experiment_message\n    exp_env.set_z_pulses(builder)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/single/two_qubit_gate/slepian_lam_once.py\", line 295, in set_z_pulses\n    raise ExperimentError(\npyQCat.errors.ExperimentError: <Exp(SlepianLamNumOnce) error> | Options lam1_list and lam2_list must not be empty!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T15:01:42.22Z"}}, {"_id": {"$oid": "683411d6f04f1fe4feb738a3"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 15:03:03.071389", "message": "Experiment Fail\n--------------------------------------------------\nq11q17c11-17-SlepianLamNum-SlepianLamNumOnce(2-9)-cz_num=5\n--------------------------------------------------\n<Exp(SlepianLamNumOnce) error> | Options lam1_list and lam2_list must not be empty!\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 544, in _async_compile\n    experiment_file: ExperimentFile = build_experiment_message(exp_env)\n                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 129, in build_experiment_message\n    exp_env.set_z_pulses(builder)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/single/two_qubit_gate/slepian_lam_once.py\", line 295, in set_z_pulses\n    raise ExperimentError(\npyQCat.errors.ExperimentError: <Exp(SlepianLamNumOnce) error> | Options lam1_list and lam2_list must not be empty!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T15:01:42.29Z"}}, {"_id": {"$oid": "683411d6f04f1fe4feb738a5"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 15:03:03.091153", "message": "Experiment Fail\n--------------------------------------------------\nq11q17c11-17-SlepianLamNum-SlepianLamNumOnce(6-9)-cz_num=25\n--------------------------------------------------\n<Exp(SlepianLamNumOnce) error> | Options lam1_list and lam2_list must not be empty!\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 544, in _async_compile\n    experiment_file: ExperimentFile = build_experiment_message(exp_env)\n                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 129, in build_experiment_message\n    exp_env.set_z_pulses(builder)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/single/two_qubit_gate/slepian_lam_once.py\", line 295, in set_z_pulses\n    raise ExperimentError(\npyQCat.errors.ExperimentError: <Exp(SlepianLamNumOnce) error> | Options lam1_list and lam2_list must not be empty!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T15:01:42.294Z"}}, {"_id": {"$oid": "683411d6f04f1fe4feb738a6"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 15:03:03.098909", "message": "Experiment Fail\n--------------------------------------------------\nq11q17c11-17-SlepianLamNum-SlepianLamNumOnce(8-9)-cz_num=35\n--------------------------------------------------\n<Exp(SlepianLamNumOnce) error> | Options lam1_list and lam2_list must not be empty!\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 544, in _async_compile\n    experiment_file: ExperimentFile = build_experiment_message(exp_env)\n                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 129, in build_experiment_message\n    exp_env.set_z_pulses(builder)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/single/two_qubit_gate/slepian_lam_once.py\", line 295, in set_z_pulses\n    raise ExperimentError(\npyQCat.errors.ExperimentError: <Exp(SlepianLamNumOnce) error> | Options lam1_list and lam2_list must not be empty!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T15:01:42.294Z"}}, {"_id": {"$oid": "683411d6f04f1fe4feb738a4"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 15:03:03.080138", "message": "Experiment Fail\n--------------------------------------------------\nq11q17c11-17-SlepianLamNum-SlepianLamNumOnce(4-9)-cz_num=15\n--------------------------------------------------\n<Exp(SlepianLamNumOnce) error> | Options lam1_list and lam2_list must not be empty!\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 544, in _async_compile\n    experiment_file: ExperimentFile = build_experiment_message(exp_env)\n                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 129, in build_experiment_message\n    exp_env.set_z_pulses(builder)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/single/two_qubit_gate/slepian_lam_once.py\", line 295, in set_z_pulses\n    raise ExperimentError(\npyQCat.errors.ExperimentError: <Exp(SlepianLamNumOnce) error> | Options lam1_list and lam2_list must not be empty!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T15:01:42.293Z"}}, {"_id": {"$oid": "683411d6f04f1fe4feb738a7"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 15:03:03.075277", "message": "Experiment Fail\n--------------------------------------------------\nq11q17c11-17-SlepianLamNum-SlepianLamNumOnce(3-9)-cz_num=10\n--------------------------------------------------\n<Exp(SlepianLamNumOnce) error> | Options lam1_list and lam2_list must not be empty!\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 544, in _async_compile\n    experiment_file: ExperimentFile = build_experiment_message(exp_env)\n                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 129, in build_experiment_message\n    exp_env.set_z_pulses(builder)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/single/two_qubit_gate/slepian_lam_once.py\", line 295, in set_z_pulses\n    raise ExperimentError(\npyQCat.errors.ExperimentError: <Exp(SlepianLamNumOnce) error> | Options lam1_list and lam2_list must not be empty!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T15:01:42.325Z"}}, {"_id": {"$oid": "683411d6f04f1fe4feb738a8"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 15:03:03.085819", "message": "Experiment Fail\n--------------------------------------------------\nq11q17c11-17-SlepianLamNum-SlepianLamNumOnce(5-9)-cz_num=20\n--------------------------------------------------\n<Exp(SlepianLamNumOnce) error> | Options lam1_list and lam2_list must not be empty!\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 544, in _async_compile\n    experiment_file: ExperimentFile = build_experiment_message(exp_env)\n                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 129, in build_experiment_message\n    exp_env.set_z_pulses(builder)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/single/two_qubit_gate/slepian_lam_once.py\", line 295, in set_z_pulses\n    raise ExperimentError(\npyQCat.errors.ExperimentError: <Exp(SlepianLamNumOnce) error> | Options lam1_list and lam2_list must not be empty!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T15:01:42.335Z"}}, {"_id": {"$oid": "683411d6f04f1fe4feb738a9"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 15:03:03.094838", "message": "Experiment Fail\n--------------------------------------------------\nq11q17c11-17-SlepianLamNum-SlepianLamNumOnce(7-9)-cz_num=30\n--------------------------------------------------\n<Exp(SlepianLamNumOnce) error> | Options lam1_list and lam2_list must not be empty!\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 544, in _async_compile\n    experiment_file: ExperimentFile = build_experiment_message(exp_env)\n                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 129, in build_experiment_message\n    exp_env.set_z_pulses(builder)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/single/two_qubit_gate/slepian_lam_once.py\", line 295, in set_z_pulses\n    raise ExperimentError(\npyQCat.errors.ExperimentError: <Exp(SlepianLamNumOnce) error> | Options lam1_list and lam2_list must not be empty!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T15:01:42.336Z"}}, {"_id": {"$oid": "683411d6f04f1fe4feb738aa"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 15:03:03.104769", "message": "Experiment Fail\n--------------------------------------------------\nq11q17c11-17-SlepianLamNum-SlepianLamNumOnce(9-9)-cz_num=40\n--------------------------------------------------\n<Exp(SlepianLamNumOnce) error> | Options lam1_list and lam2_list must not be empty!\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 544, in _async_compile\n    experiment_file: ExperimentFile = build_experiment_message(exp_env)\n                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 129, in build_experiment_message\n    exp_env.set_z_pulses(builder)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/single/two_qubit_gate/slepian_lam_once.py\", line 295, in set_z_pulses\n    raise ExperimentError(\npyQCat.errors.ExperimentError: <Exp(SlepianLamNumOnce) error> | Options lam1_list and lam2_list must not be empty!\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T15:01:42.336Z"}}, {"_id": {"$oid": "68341208f04f1fe4feb738ba"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 15:03:53.344802", "message": "Experiment Fail\n--------------------------------------------------\nq1q2-McmSpectatorDePhase\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 260, in _validate_and_merge\n    assert len(set(loop_nums)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Inconsistent number of loops in sweep control, details:\n[51, 1]\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 260, in _validate_and_merge\n    assert len(set(loop_nums)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Inconsistent number of loops in sweep control, details:\n[51, 1]\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T15:02:32.459Z"}}, {"_id": {"$oid": "68341208f04f1fe4feb738bb"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 15:03:53.513737", "message": "Experiment Fail\n--------------------------------------------------\nq1q2-McmSpectatorDePhaseComposite-McmSpectatorDePhase(1-4)-scale_coef=1\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 260, in _validate_and_merge\n    assert len(set(loop_nums)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Inconsistent number of loops in sweep control, details:\n[51, 1]\n\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 260, in _validate_and_merge\n    assert len(set(loop_nums)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Inconsistent number of loops in sweep control, details:\n[51, 1]\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T15:02:32.676Z"}}, {"_id": {"$oid": "68341208f04f1fe4feb738bc"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 15:03:53.557373", "message": "Experiment Fail\n--------------------------------------------------\nq1q2-McmSpectatorDePhaseComposite-McmSpectatorDePhase(3-4)-scale_coef=0.98\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 260, in _validate_and_merge\n    assert len(set(loop_nums)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Inconsistent number of loops in sweep control, details:\n[51, 1]\n\n--------------------------------------------------\n<PERSON>back (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 260, in _validate_and_merge\n    assert len(set(loop_nums)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Inconsistent number of loops in sweep control, details:\n[51, 1]\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T15:02:32.678Z"}}, {"_id": {"$oid": "68341208f04f1fe4feb738bd"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 15:03:53.536784", "message": "Experiment Fail\n--------------------------------------------------\nq1q2-McmSpectatorDePhaseComposite-McmSpectatorDePhase(2-4)-scale_coef=0.99\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 260, in _validate_and_merge\n    assert len(set(loop_nums)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Inconsistent number of loops in sweep control, details:\n[51, 1]\n\n--------------------------------------------------\n<PERSON>back (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 260, in _validate_and_merge\n    assert len(set(loop_nums)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Inconsistent number of loops in sweep control, details:\n[51, 1]\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T15:02:32.69Z"}}, {"_id": {"$oid": "68341208f04f1fe4feb738be"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 15:03:53.578676", "message": "Experiment Fail\n--------------------------------------------------\nq1q2-McmSpectatorDePhaseComposite-McmSpectatorDePhase(4-4)-scale_coef=0.97\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 260, in _validate_and_merge\n    assert len(set(loop_nums)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Inconsistent number of loops in sweep control, details:\n[51, 1]\n\n--------------------------------------------------\n<PERSON>back (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/concurrent/worker/experiment_compiler.py\", line 260, in _validate_and_merge\n    assert len(set(loop_nums)) == 1, (\n           ^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Inconsistent number of loops in sweep control, details:\n[51, 1]\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T15:02:32.692Z"}}, {"_id": {"$oid": "68342bd7f04f1fe4feb738c7"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:00.940395", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(1-41)-Detune=-40\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:39.969Z"}}, {"_id": {"$oid": "68342bd8f04f1fe4feb738c8"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:01.039940", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(2-41)-Detune=-38\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:40.15Z"}}, {"_id": {"$oid": "68342bd8f04f1fe4feb738c9"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:01.145092", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(3-41)-Detune=-36\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:40.239Z"}}, {"_id": {"$oid": "68342bd8f04f1fe4feb738ca"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:01.582154", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(4-41)-Detune=-34\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:40.604Z"}}, {"_id": {"$oid": "68342bd8f04f1fe4feb738cb"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:01.706162", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(5-41)-Detune=-32\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:40.722Z"}}, {"_id": {"$oid": "68342bd8f04f1fe4feb738cc"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:01.822171", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(6-41)-Detune=-30\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:40.887Z"}}, {"_id": {"$oid": "68342bd8f04f1fe4feb738cd"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:01.911913", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(7-41)-Detune=-28\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:40.935Z"}}, {"_id": {"$oid": "68342bd8f04f1fe4feb738ce"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:01.979122", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(8-41)-Detune=-26\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:40.974Z"}}, {"_id": {"$oid": "68342bd9f04f1fe4feb738cf"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:02.048913", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(9-41)-Detune=-24\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:41.083Z"}}, {"_id": {"$oid": "68342bd9f04f1fe4feb738d0"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:02.119606", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(10-41)-Detune=-22\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:41.202Z"}}, {"_id": {"$oid": "68342bd9f04f1fe4feb738d1"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:02.186686", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(11-41)-Detune=-20\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:41.204Z"}}, {"_id": {"$oid": "68342bd9f04f1fe4feb738d2"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:02.339647", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(12-41)-Detune=-18\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:41.37Z"}}, {"_id": {"$oid": "68342bd9f04f1fe4feb738d3"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:02.405412", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(13-41)-Detune=-16\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:41.43Z"}}, {"_id": {"$oid": "68342bd9f04f1fe4feb738d4"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:02.473621", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(14-41)-Detune=-14\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:41.489Z"}}, {"_id": {"$oid": "68342bd9f04f1fe4feb738d5"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:02.541532", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(15-41)-Detune=-12\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:41.551Z"}}, {"_id": {"$oid": "68342bd9f04f1fe4feb738d6"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:02.609313", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(16-41)-Detune=-10\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:41.628Z"}}, {"_id": {"$oid": "68342bd9f04f1fe4feb738d7"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:02.675626", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(17-41)-Detune=-8\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:41.687Z"}}, {"_id": {"$oid": "68342bd9f04f1fe4feb738d8"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:02.743601", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(18-41)-Detune=-6\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:41.762Z"}}, {"_id": {"$oid": "68342bd9f04f1fe4feb738d9"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:02.809627", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(19-41)-Detune=-4\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:41.936Z"}}, {"_id": {"$oid": "68342bd9f04f1fe4feb738da"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:02.964068", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(20-41)-Detune=-2\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:41.973Z"}}, {"_id": {"$oid": "68342bdaf04f1fe4feb738db"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:03.031340", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(21-41)-Detune=0\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:42.127Z"}}, {"_id": {"$oid": "68342bdaf04f1fe4feb738dc"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:03.095838", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(22-41)-Detune=2\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:42.129Z"}}, {"_id": {"$oid": "68342bdaf04f1fe4feb738dd"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:03.163601", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(23-41)-Detune=4\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:42.19Z"}}, {"_id": {"$oid": "68342bdaf04f1fe4feb738de"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:03.228178", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(24-41)-Detune=6\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:42.252Z"}}, {"_id": {"$oid": "68342bdaf04f1fe4feb738df"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:03.295170", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(25-41)-Detune=8\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:42.325Z"}}, {"_id": {"$oid": "68342bdaf04f1fe4feb738e0"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:03.366087", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(26-41)-Detune=10\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:42.39Z"}}, {"_id": {"$oid": "68342bdaf04f1fe4feb738e1"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:03.529893", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(27-41)-Detune=12\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:42.604Z"}}, {"_id": {"$oid": "68342bdaf04f1fe4feb738e2"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:03.595830", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(28-41)-Detune=14\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:42.604Z"}}, {"_id": {"$oid": "68342bdaf04f1fe4feb738e3"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:03.663127", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(29-41)-Detune=16\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:42.706Z"}}, {"_id": {"$oid": "68342bdaf04f1fe4feb738e4"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:03.738048", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(30-41)-Detune=18\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:42.777Z"}}, {"_id": {"$oid": "68342bdaf04f1fe4feb738e5"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:03.809034", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(31-41)-Detune=20\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:42.82Z"}}, {"_id": {"$oid": "68342bdaf04f1fe4feb738e6"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:03.875630", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(32-41)-Detune=22\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:42.895Z"}}, {"_id": {"$oid": "68342bdbf04f1fe4feb738e7"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:03.946654", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(33-41)-Detune=24\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:43.01Z"}}, {"_id": {"$oid": "68342bdbf04f1fe4feb738e8"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:04.019745", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(34-41)-Detune=26\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:43.101Z"}}, {"_id": {"$oid": "68342bdbf04f1fe4feb738e9"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:04.091009", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(35-41)-Detune=28\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:43.103Z"}}, {"_id": {"$oid": "68342bdbf04f1fe4feb738ea"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:04.270609", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(36-41)-Detune=30\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:43.297Z"}}, {"_id": {"$oid": "68342bdbf04f1fe4feb738eb"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:04.346846", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(37-41)-Detune=32\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:43.376Z"}}, {"_id": {"$oid": "68342bdbf04f1fe4feb738ec"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:04.421844", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(38-41)-Detune=34\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:43.463Z"}}, {"_id": {"$oid": "68342bdbf04f1fe4feb738ed"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:04.496158", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(39-41)-Detune=36\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:43.585Z"}}, {"_id": {"$oid": "68342bdbf04f1fe4feb738ee"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:04.568350", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(40-41)-Detune=38\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:43.593Z"}}, {"_id": {"$oid": "68342bdbf04f1fe4feb738ef"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 5, "time": "2025-05-26 16:54:04.636711", "message": "Experiment Crash\n--------------------------------------------------\nq1q7c1-7-CouplerSweepDetuneRabiWidth-CouplerRabiScanWidthDetune(41-41)-Detune=40\n--------------------------------------------------\n<Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n--------------------------------------------------\n<PERSON><PERSON> (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 607, in run_experiment\n    require_id = await self._async_compile()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/experiments/top_experiment_v1.py\", line 548, in _async_compile\n    state, require_id, task_id = await register_compile_result(result, self.simulator_mode.value)\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/data_client.py\", line 395, in register_compile_result\n    raise CompilerExperimentError(msg=str(result.message))\npyQCat.errors.CompilerExperimentError: <Compiler Experiment Program Error> | Traceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 491, in run\n    self._validate_and_merge()\n  File \"/home/<USER>/code/pyqcat-apps/pyQCat/concurrent/worker/experiment_compiler.py\", line 269, in _validate_and_merge\n    assert len(set(same_lo_gaps)) == 1, (\nAssertionError: Same lo validate error, details:\nlo: xy-1\nvalue:[4950, 3450]\nunit: ['q1', 'q7']\n\n", "version": "monster | ******** | B", "tackle": true, "create_time": {"$date": "2025-05-26T16:52:43.631Z"}}]