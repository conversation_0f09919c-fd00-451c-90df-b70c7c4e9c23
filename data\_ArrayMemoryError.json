[{"_id": {"$oid": "686cc97d59896c8d2c02fcc7"}, "username": "wxy-Y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-08 15:33:46.870237", "message": "Experiment Crash\n--------------------------------------------------\nq-circuit-QCloudPlusV1-686cc8518bf45b2d14e2a760\n--------------------------------------------------\nUnable to allocate 512. GiB for an array with shape (131072, 2, 131072, 2) and data type float64\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\Code\\wxy\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\Code\\wxy\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 614, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\Code\\wxy\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\Code\\wxy\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 156, in top_experiment_analysis\n    data_acquisition = acq_class(**acquisition_options)\n  File \"D:\\Code\\wxy\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 202, in __init__\n    self.fidelity_matrix = self.update_fidelity_matrix(is_amend, fidelity_matrix, on_discriminator)\n  File \"D:\\Code\\wxy\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 361, in update_fidelity_matrix\n    new_fd_matrix = np.kron(new_fd_matrix, dcm.fidelity_matrix)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\numpy\\lib\\shape_base.py\", line 1173, in kron\n    result = _nx.multiply(a_arr, b_arr, subok=(not is_any_mat))\nnumpy.core._exceptions._ArrayMemoryError: Unable to allocate 512. GiB for an array with shape (131072, 2, 131072, 2) and data type float64\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-08T15:32:13.546Z"}}, {"_id": {"$oid": "686f69f659896c8d2c03033a"}, "username": "wxy-Y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 10, "time": "2025-07-10 15:23:04.312481", "message": "Experiment Crash\n--------------------------------------------------\nq-circuit-QCloudPlusV1-686f68df2d27b927c1ec1c6c\n--------------------------------------------------\nUnable to allocate 512. GiB for an array with shape (131072, 2, 131072, 2) and data type float64\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\Code\\wxy\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\Code\\wxy\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 614, in run_experiment\n    self.analysis = await self._async_analysis(require_id)\n  File \"D:\\Code\\wxy\\pyqcat-apps\\pyQCat\\experiments\\top_experiment_v1.py\", line 512, in _async_analysis\n    result = await top_experiment_analysis(**kwargs)\n  File \"D:\\Code\\wxy\\pyqcat-apps\\pyQCat\\concurrent\\worker\\analysis_interface.py\", line 156, in top_experiment_analysis\n    data_acquisition = acq_class(**acquisition_options)\n  File \"D:\\Code\\wxy\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 202, in __init__\n    self.fidelity_matrix = self.update_fidelity_matrix(is_amend, fidelity_matrix, on_discriminator)\n  File \"D:\\Code\\wxy\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 361, in update_fidelity_matrix\n    new_fd_matrix = np.kron(new_fd_matrix, dcm.fidelity_matrix)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\numpy\\lib\\shape_base.py\", line 1173, in kron\n    result = _nx.multiply(a_arr, b_arr, subok=(not is_any_mat))\nnumpy.core._exceptions._ArrayMemoryError: Unable to allocate 512. GiB for an array with shape (131072, 2, 131072, 2) and data type float64\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-10T15:21:26.895Z"}}, {"_id": {"$oid": "68765de659896c8d2c031834"}, "username": "wxy-Y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-15 21:57:38.132245", "message": "Experiment Fail\n--------------------------------------------------\nq-circuit-QCloudPlusV1-68765586ba251c64cf9ba4df\n--------------------------------------------------\n<Acq Data Tackle Error> | Traceback (most recent call last):\n  File \"D:\\Code\\wxy\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 705, in async_execute_loop\n    await self._async_get_measure_data(self.sample_channels, loop)\n  File \"D:\\Code\\wxy\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 765, in _async_get_measure_data\n    self.correct_res()\n  File \"D:\\Code\\wxy\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 437, in correct_res\n    prob = correct_fidelity(\n  File \"D:\\Code\\wxy\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\iqprobability.py\", line 1270, in correct_fidelity\n    after_correct_f = iter_bayesian_unfold(\n  File \"D:\\Code\\wxy\\pyqcat-apps\\pyQCat\\analysis\\algorithms\\iqprobability.py\", line 1483, in iter_bayesian_unfold\n    ptruen_mat = np.tile(ptruen, (dim, 1))\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\numpy\\lib\\shape_base.py\", line 1272, in tile\n    c = c.reshape(-1, n).repeat(nrep, 0)\nnumpy.core._exceptions._ArrayMemoryError: Unable to allocate 128. GiB for an array with shape (131072, 131072) and data type float64\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-15T21:55:50.055Z"}}]