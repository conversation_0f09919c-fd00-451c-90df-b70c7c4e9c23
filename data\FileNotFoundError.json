[{"_id": {"$oid": "683411e2f04f1fe4feb738b8"}, "username": "zyc", "sample": "Test-102bit-v2", "env_name": "S251", "level": 10, "time": "2025-05-26 15:03:14.904914", "message": "Experiment Fail\n--------------------------------------------------\nq1-DistortionPolesOpt\n--------------------------------------------------\n<Exp(DistortionPolesOpt) experiment options error> | key(poles_bounds_path) | value(F:\\2024-01\\monster_dev\\pyqcat-monster\\conf\\poles_bounds_p0.json) | F:\\2024-01\\monster_dev\\pyqcat-monster\\conf\\poles_bounds_p0.json does not exist! | Please check options!\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite/z_distortion/distortion_pole_optimization.py\", line 120, in _check_options\n    with open(file_name, mode=\"r\", encoding=\"utf-8\") as fp:\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nFileNotFoundError: [Errno 2] No such file or directory: 'F:\\\\2024-01\\\\monster_dev\\\\pyqcat-monster\\\\conf\\\\poles_bounds_p0.json'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/_wrappers.py\", line 71, in wrapper\n    await func(*args, **kwargs)\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 498, in run_experiment\n    self.run()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite_experiment.py\", line 195, in run\n    self._check_options()\n  File \"/home/<USER>/code/pyqcat-monster/pyQCat/experiments/composite/z_distortion/distortion_pole_optimization.py\", line 123, in _check_options\n    raise ExperimentOptionsError(\npyQCat.errors.ExperimentOptionsError: <Exp(DistortionPolesOpt) experiment options error> | key(poles_bounds_path) | value(F:\\2024-01\\monster_dev\\pyqcat-monster\\conf\\poles_bounds_p0.json) | F:\\2024-01\\monster_dev\\pyqcat-monster\\conf\\poles_bounds_p0.json does not exist! | Please check options!\n", "version": "monster | 0.23.1.9 | B", "tackle": true, "create_time": {"$date": "2025-05-26T15:01:54.016Z"}}, {"_id": {"$oid": "686e021a2ebaa9fee5f72aa5"}, "username": "job", "sample": "D8-24bit-2", "env_name": "D8-2-3", "level": 10, "time": "2025-07-09 13:46:02.092677", "message": "Experiment Crash\n--------------------------------------------------\nq17-OptimizeFirDicarlo-686e0219318a0dde21a228bf\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-07-09T13:46:02.572Z"}}, {"_id": {"$oid": "686e021aec42392c4af72aab"}, "username": "job", "sample": "D8-24bit-2", "env_name": "D8-2-3", "level": 10, "time": "2025-07-09 13:46:02.144690", "message": "Experiment Crash\n--------------------------------------------------\nq18-OptimizeFirDicarlo-686e0219318a0dde21a228c1\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-07-09T13:46:02.612Z"}}, {"_id": {"$oid": "686e021a2ebaa9fee5f72aa6"}, "username": "job", "sample": "D8-24bit-2", "env_name": "D8-2-3", "level": 10, "time": "2025-07-09 13:46:02.197702", "message": "Experiment Crash\n--------------------------------------------------\nq19-OptimizeFirDicarlo-686e021a318a0dde21a228c3\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-07-09T13:46:02.67Z"}}, {"_id": {"$oid": "686e021aec42392c4af72aac"}, "username": "job", "sample": "D8-24bit-2", "env_name": "D8-2-3", "level": 10, "time": "2025-07-09 13:46:02.248714", "message": "Experiment Crash\n--------------------------------------------------\nq20-OptimizeFirDicarlo-686e021a318a0dde21a228c5\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-07-09T13:46:02.72Z"}}, {"_id": {"$oid": "686e021aec42392c4af72aad"}, "username": "job", "sample": "D8-24bit-2", "env_name": "D8-2-3", "level": 10, "time": "2025-07-09 13:46:02.302727", "message": "Experiment Crash\n--------------------------------------------------\nq21-OptimizeFirDicarlo-686e021a318a0dde21a228c7\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-07-09T13:46:02.776Z"}}, {"_id": {"$oid": "686e021a2ebaa9fee5f72aa7"}, "username": "job", "sample": "D8-24bit-2", "env_name": "D8-2-3", "level": 10, "time": "2025-07-09 13:46:02.361740", "message": "Experiment Crash\n--------------------------------------------------\nq22-OptimizeFirDicarlo-686e021a318a0dde21a228c9\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-07-09T13:46:02.829Z"}}, {"_id": {"$oid": "686e021aec42392c4af72aae"}, "username": "job", "sample": "D8-24bit-2", "env_name": "D8-2-3", "level": 10, "time": "2025-07-09 13:46:02.412753", "message": "Experiment Crash\n--------------------------------------------------\nq23-OptimizeFirDicarlo-686e021a318a0dde21a228cb\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-07-09T13:46:02.877Z"}}, {"_id": {"$oid": "686e021a2ebaa9fee5f72aa8"}, "username": "job", "sample": "D8-24bit-2", "env_name": "D8-2-3", "level": 10, "time": "2025-07-09 13:46:02.463765", "message": "Experiment Crash\n--------------------------------------------------\nq24-OptimizeFirDicarlo-686e021a318a0dde21a228cd\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"C:\\code\\apps\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\monster\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n", "version": "monster | 0.23.2 | C", "create_time": {"$date": "2025-07-09T13:46:02.937Z"}}, {"_id": {"$oid": "68882672f4b30d9991446ec8"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 09:42:21.519050", "message": "Experiment Fail\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(1-31)-power=-40dBm-688826fdf13d5118f9313221\n--------------------------------------------------\n<Acq Data Tackle Error> | Traceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 795, in execute_loop\n    data = np.load(self._simulator_data_path)\n  File \"C:\\Users\\<USER>\\.conda\\envs\\app39\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 427, in load\n    fid = stack.enter_context(open(os_fspath(file), \"rb\"))\nFileNotFoundError: [Errno 2] No such file or directory: 'Z'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:40:02.691Z"}}, {"_id": {"$oid": "68882672f340c94b3b446ff2"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 09:42:21.582880", "message": "Experiment Fail\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(4-31)-power=-37dBm-688826fdf13d5118f9313224\n--------------------------------------------------\n<Acq Data Tackle Error> | Traceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 795, in execute_loop\n    data = np.load(self._simulator_data_path)\n  File \"C:\\Users\\<USER>\\.conda\\envs\\app39\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 427, in load\n    fid = stack.enter_context(open(os_fspath(file), \"rb\"))\nFileNotFoundError: [Errno 2] No such file or directory: 'Y'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:40:02.741Z"}}, {"_id": {"$oid": "68882672f4b30d9991446ec9"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 09:42:21.551962", "message": "Experiment Fail\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(3-31)-power=-38dBm-688826fdf13d5118f9313223\n--------------------------------------------------\n<Acq Data Tackle Error> | Traceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 795, in execute_loop\n    data = np.load(self._simulator_data_path)\n  File \"C:\\Users\\<USER>\\.conda\\envs\\app39\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 427, in load\n    fid = stack.enter_context(open(os_fspath(file), \"rb\"))\nFileNotFoundError: [Errno 2] No such file or directory: '\\\\'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:40:02.742Z"}}, {"_id": {"$oid": "68882672f4b30d9991446eca"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 09:42:21.615792", "message": "Experiment Fail\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(5-31)-power=-36dBm-688826fdf13d5118f9313225\n--------------------------------------------------\n<Acq Data Tackle Error> | Traceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 795, in execute_loop\n    data = np.load(self._simulator_data_path)\n  File \"C:\\Users\\<USER>\\.conda\\envs\\app39\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 427, in load\n    fid = stack.enter_context(open(os_fspath(file), \"rb\"))\nFileNotFoundError: [Errno 2] No such file or directory: '3'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:40:02.777Z"}}, {"_id": {"$oid": "68882672f4b30d9991446ecb"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 09:42:21.653691", "message": "Experiment Fail\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(6-31)-power=-35dBm-688826fdf13d5118f9313226\n--------------------------------------------------\n<Acq Data Tackle Error> | Traceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 795, in execute_loop\n    data = np.load(self._simulator_data_path)\n  File \"C:\\Users\\<USER>\\.conda\\envs\\app39\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 427, in load\n    fid = stack.enter_context(open(os_fspath(file), \"rb\"))\nFileNotFoundError: [Errno 2] No such file or directory: '\\\\'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:40:02.824Z"}}, {"_id": {"$oid": "68882672f340c94b3b446ff3"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 09:42:21.687600", "message": "Experiment Fail\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(7-31)-power=-34dBm-688826fdf13d5118f9313227\n--------------------------------------------------\n<Acq Data Tackle Error> | Traceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 795, in execute_loop\n    data = np.load(self._simulator_data_path)\n  File \"C:\\Users\\<USER>\\.conda\\envs\\app39\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 427, in load\n    fid = stack.enter_context(open(os_fspath(file), \"rb\"))\nFileNotFoundError: [Errno 2] No such file or directory: 'r'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:40:02.836Z"}}, {"_id": {"$oid": "68882672f340c94b3b446ff4"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 09:42:21.727004", "message": "Experiment Fail\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(8-31)-power=-33dBm-688826fdf13d5118f9313228\n--------------------------------------------------\n<Acq Data Tackle Error> | Traceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 795, in execute_loop\n    data = np.load(self._simulator_data_path)\n  File \"C:\\Users\\<USER>\\.conda\\envs\\app39\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 427, in load\n    fid = stack.enter_context(open(os_fspath(file), \"rb\"))\nFileNotFoundError: [Errno 2] No such file or directory: 'o'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:40:02.882Z"}}, {"_id": {"$oid": "68882672f4b30d9991446ecc"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 09:42:21.763905", "message": "Experiment Fail\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(9-31)-power=-32dBm-688826fdf13d5118f9313229\n--------------------------------------------------\n<Acq Data Tackle Error> | Traceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 795, in execute_loop\n    data = np.load(self._simulator_data_path)\n  File \"C:\\Users\\<USER>\\.conda\\envs\\app39\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 427, in load\n    fid = stack.enter_context(open(os_fspath(file), \"rb\"))\nFileNotFoundError: [Errno 2] No such file or directory: 'b'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:40:02.935Z"}}, {"_id": {"$oid": "68882672f4b30d9991446ecd"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 09:42:21.829730", "message": "Experiment Fail\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(11-31)-power=-30dBm-688826fdf13d5118f931322b\n--------------------------------------------------\n<Acq Data Tackle Error> | Traceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 795, in execute_loop\n    data = np.load(self._simulator_data_path)\n  File \"C:\\Users\\<USER>\\.conda\\envs\\app39\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 427, in load\n    fid = stack.enter_context(open(os_fspath(file), \"rb\"))\nFileNotFoundError: [Errno 2] No such file or directory: 't'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:40:02.985Z"}}, {"_id": {"$oid": "68882672f4b30d9991446ece"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 09:42:21.796818", "message": "Experiment Fail\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(10-31)-power=-31dBm-688826fdf13d5118f931322a\n--------------------------------------------------\n<Acq Data Tackle Error> | Traceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 795, in execute_loop\n    data = np.load(self._simulator_data_path)\n  File \"C:\\Users\\<USER>\\.conda\\envs\\app39\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 427, in load\n    fid = stack.enter_context(open(os_fspath(file), \"rb\"))\nFileNotFoundError: [Errno 2] No such file or directory: 'o'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:40:02.986Z"}}, {"_id": {"$oid": "68882673f340c94b3b446ff5"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 09:42:21.863639", "message": "Experiment Fail\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(12-31)-power=-29dBm-688826fdf13d5118f931322c\n--------------------------------------------------\n<Acq Data Tackle Error> | Traceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 795, in execute_loop\n    data = np.load(self._simulator_data_path)\n  File \"C:\\Users\\<USER>\\.conda\\envs\\app39\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 427, in load\n    fid = stack.enter_context(open(os_fspath(file), \"rb\"))\nFileNotFoundError: [Errno 2] No such file or directory: '\\\\'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:40:03.013Z"}}, {"_id": {"$oid": "68882673f4b30d9991446ecf"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 09:42:21.895554", "message": "Experiment Fail\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(13-31)-power=-28dBm-688826fdf13d5118f931322d\n--------------------------------------------------\n<Acq Data Tackle Error> | Traceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 795, in execute_loop\n    data = np.load(self._simulator_data_path)\n  File \"C:\\Users\\<USER>\\.conda\\envs\\app39\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 427, in load\n    fid = stack.enter_context(open(os_fspath(file), \"rb\"))\nFileNotFoundError: [Errno 2] No such file or directory: 'd'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:40:03.066Z"}}, {"_id": {"$oid": "68882673f4b30d9991446ed0"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 09:42:21.931514", "message": "Experiment Fail\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(14-31)-power=-27dBm-688826fdf13d5118f931322e\n--------------------------------------------------\n<Acq Data Tackle Error> | Traceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 795, in execute_loop\n    data = np.load(self._simulator_data_path)\n  File \"C:\\Users\\<USER>\\.conda\\envs\\app39\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 427, in load\n    fid = stack.enter_context(open(os_fspath(file), \"rb\"))\nFileNotFoundError: [Errno 2] No such file or directory: 'a'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:40:03.105Z"}}, {"_id": {"$oid": "68882673f340c94b3b446ff6"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 09:42:21.965087", "message": "Experiment Fail\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(15-31)-power=-26dBm-688826fdf13d5118f931322f\n--------------------------------------------------\n<Acq Data Tackle Error> | Traceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 795, in execute_loop\n    data = np.load(self._simulator_data_path)\n  File \"C:\\Users\\<USER>\\.conda\\envs\\app39\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 427, in load\n    fid = stack.enter_context(open(os_fspath(file), \"rb\"))\nFileNotFoundError: [Errno 2] No such file or directory: 't'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:40:03.113Z"}}, {"_id": {"$oid": "68882673f4b30d9991446ed1"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 09:42:21.998996", "message": "Experiment Fail\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(16-31)-power=-25dBm-688826fdf13d5118f9313230\n--------------------------------------------------\n<Acq Data Tackle Error> | Traceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 795, in execute_loop\n    data = np.load(self._simulator_data_path)\n  File \"C:\\Users\\<USER>\\.conda\\envs\\app39\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 427, in load\n    fid = stack.enter_context(open(os_fspath(file), \"rb\"))\nFileNotFoundError: [Errno 2] No such file or directory: 'a'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:40:03.149Z"}}, {"_id": {"$oid": "68882673f340c94b3b446ff7"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 09:42:22.030911", "message": "Experiment Fail\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(17-31)-power=-24dBm-688826fef13d5118f9313231\n--------------------------------------------------\n<Acq Data Tackle Error> | Traceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 795, in execute_loop\n    data = np.load(self._simulator_data_path)\n  File \"C:\\Users\\<USER>\\.conda\\envs\\app39\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 427, in load\n    fid = stack.enter_context(open(os_fspath(file), \"rb\"))\nFileNotFoundError: [Errno 2] No such file or directory: '\\\\'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:40:03.186Z"}}, {"_id": {"$oid": "68882673f340c94b3b446ff8"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 09:42:22.062825", "message": "Experiment Fail\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(18-31)-power=-23dBm-688826fef13d5118f9313232\n--------------------------------------------------\n<Acq Data Tackle Error> | Traceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 795, in execute_loop\n    data = np.load(self._simulator_data_path)\n  File \"C:\\Users\\<USER>\\.conda\\envs\\app39\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 427, in load\n    fid = stack.enter_context(open(os_fspath(file), \"rb\"))\nFileNotFoundError: [Errno 2] No such file or directory: 'V'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:40:03.227Z"}}, {"_id": {"$oid": "68882673f340c94b3b446ff9"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 09:42:22.093253", "message": "Experiment Fail\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(19-31)-power=-22dBm-688826fef13d5118f9313233\n--------------------------------------------------\n<Acq Data Tackle Error> | Traceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 795, in execute_loop\n    data = np.load(self._simulator_data_path)\n  File \"C:\\Users\\<USER>\\.conda\\envs\\app39\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 427, in load\n    fid = stack.enter_context(open(os_fspath(file), \"rb\"))\nFileNotFoundError: [Errno 2] No such file or directory: '3'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:40:03.261Z"}}, {"_id": {"$oid": "68882673f340c94b3b446ffa"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 09:42:22.123173", "message": "Experiment Fail\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(20-31)-power=-21dBm-688826fef13d5118f9313234\n--------------------------------------------------\n<Acq Data Tackle Error> | Traceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 795, in execute_loop\n    data = np.load(self._simulator_data_path)\n  File \"C:\\Users\\<USER>\\.conda\\envs\\app39\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 427, in load\n    fid = stack.enter_context(open(os_fspath(file), \"rb\"))\nFileNotFoundError: [Errno 2] No such file or directory: '\\\\'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:40:03.297Z"}}, {"_id": {"$oid": "68882673f340c94b3b446ffb"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 09:42:22.157083", "message": "Experiment Fail\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(21-31)-power=-20dBm-688826fef13d5118f9313235\n--------------------------------------------------\n<Acq Data Tackle Error> | Traceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 795, in execute_loop\n    data = np.load(self._simulator_data_path)\n  File \"C:\\Users\\<USER>\\.conda\\envs\\app39\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 427, in load\n    fid = stack.enter_context(open(os_fspath(file), \"rb\"))\nFileNotFoundError: [Errno 2] No such file or directory: '2'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:40:03.331Z"}}, {"_id": {"$oid": "68882673f340c94b3b446ffc"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 09:42:22.189995", "message": "Experiment Fail\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(22-31)-power=-19dBm-688826fef13d5118f9313236\n--------------------------------------------------\n<Acq Data Tackle Error> | Traceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 795, in execute_loop\n    data = np.load(self._simulator_data_path)\n  File \"C:\\Users\\<USER>\\.conda\\envs\\app39\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 427, in load\n    fid = stack.enter_context(open(os_fspath(file), \"rb\"))\nFileNotFoundError: [Errno 2] No such file or directory: '4'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:40:03.366Z"}}, {"_id": {"$oid": "68882673f340c94b3b446ffd"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 09:42:22.222906", "message": "Experiment Fail\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(23-31)-power=-18dBm-688826fef13d5118f9313237\n--------------------------------------------------\n<Acq Data Tackle Error> | Traceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 795, in execute_loop\n    data = np.load(self._simulator_data_path)\n  File \"C:\\Users\\<USER>\\.conda\\envs\\app39\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 427, in load\n    fid = stack.enter_context(open(os_fspath(file), \"rb\"))\nFileNotFoundError: [Errno 2] No such file or directory: '1'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:40:03.4Z"}}, {"_id": {"$oid": "68882673f4b30d9991446ed2"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 09:42:22.256816", "message": "Experiment Fail\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(24-31)-power=-17dBm-688826fef13d5118f9313238\n--------------------------------------------------\n<Acq Data Tackle Error> | Traceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 795, in execute_loop\n    data = np.load(self._simulator_data_path)\n  File \"C:\\Users\\<USER>\\.conda\\envs\\app39\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 427, in load\n    fid = stack.enter_context(open(os_fspath(file), \"rb\"))\nFileNotFoundError: [Errno 2] No such file or directory: '1'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:40:03.406Z"}}, {"_id": {"$oid": "68882673f4b30d9991446ed3"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 09:42:22.287733", "message": "Experiment Fail\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(25-31)-power=-16dBm-688826fef13d5118f9313239\n--------------------------------------------------\n<Acq Data Tackle Error> | Traceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 795, in execute_loop\n    data = np.load(self._simulator_data_path)\n  File \"C:\\Users\\<USER>\\.conda\\envs\\app39\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 427, in load\n    fid = stack.enter_context(open(os_fspath(file), \"rb\"))\nFileNotFoundError: [Errno 2] No such file or directory: '1'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:40:03.461Z"}}, {"_id": {"$oid": "68882673f4b30d9991446ed4"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 09:42:22.318302", "message": "Experiment Fail\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(26-31)-power=-15dBm-688826fef13d5118f931323a\n--------------------------------------------------\n<Acq Data Tackle Error> | Traceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 795, in execute_loop\n    data = np.load(self._simulator_data_path)\n  File \"C:\\Users\\<USER>\\.conda\\envs\\app39\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 427, in load\n    fid = stack.enter_context(open(os_fspath(file), \"rb\"))\nFileNotFoundError: [Errno 2] No such file or directory: '8'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:40:03.495Z"}}, {"_id": {"$oid": "68882673f4b30d9991446ed5"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 09:42:22.356201", "message": "Experiment Fail\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(27-31)-power=-14dBm-688826fef13d5118f931323b\n--------------------------------------------------\n<Acq Data Tackle Error> | Traceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 795, in execute_loop\n    data = np.load(self._simulator_data_path)\n  File \"C:\\Users\\<USER>\\.conda\\envs\\app39\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 427, in load\n    fid = stack.enter_context(open(os_fspath(file), \"rb\"))\nFileNotFoundError: [Errno 2] No such file or directory: '-'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:40:03.531Z"}}, {"_id": {"$oid": "68882673f340c94b3b446ffe"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 09:42:22.405070", "message": "Experiment Fail\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(28-31)-power=-13dBm-688826fef13d5118f931323c\n--------------------------------------------------\n<Acq Data Tackle Error> | Traceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 795, in execute_loop\n    data = np.load(self._simulator_data_path)\n  File \"C:\\Users\\<USER>\\.conda\\envs\\app39\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 427, in load\n    fid = stack.enter_context(open(os_fspath(file), \"rb\"))\nFileNotFoundError: [Errno 2] No such file or directory: '设'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:40:03.565Z"}}, {"_id": {"$oid": "68882673f4b30d9991446ed6"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 09:42:22.440974", "message": "Experiment Fail\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(29-31)-power=-12dBm-688826fef13d5118f931323d\n--------------------------------------------------\n<Acq Data Tackle Error> | Traceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 795, in execute_loop\n    data = np.load(self._simulator_data_path)\n  File \"C:\\Users\\<USER>\\.conda\\envs\\app39\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 427, in load\n    fid = stack.enter_context(open(os_fspath(file), \"rb\"))\nFileNotFoundError: [Errno 2] No such file or directory: '计'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:40:03.588Z"}}, {"_id": {"$oid": "68882673f4b30d9991446ed7"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 09:42:22.476878", "message": "Experiment Fail\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(30-31)-power=-11dBm-688826fef13d5118f931323e\n--------------------------------------------------\n<Acq Data Tackle Error> | Traceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 795, in execute_loop\n    data = np.load(self._simulator_data_path)\n  File \"C:\\Users\\<USER>\\.conda\\envs\\app39\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 427, in load\n    fid = stack.enter_context(open(os_fspath(file), \"rb\"))\nFileNotFoundError: [Errno 2] No such file or directory: '验'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:40:03.626Z"}}, {"_id": {"$oid": "68882673f340c94b3b446fff"}, "username": "monitor_y3", "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）", "env_name": "Y3", "level": 5, "time": "2025-07-29 09:42:22.508794", "message": "Experiment Fail\n--------------------------------------------------\nq12-CavityPowerScan-CavityFreqSpectrum(31-31)-power=-10dBm-688826fef13d5118f931323f\n--------------------------------------------------\n<Acq Data Tackle Error> | Traceback (most recent call last):\n  File \"D:\\code\\Y3-Online\\Monitor\\0.23.2\\pyqcat-apps\\pyQCat\\acquisition\\acquisition.py\", line 795, in execute_loop\n    data = np.load(self._simulator_data_path)\n  File \"C:\\Users\\<USER>\\.conda\\envs\\app39\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 427, in load\n    fid = stack.enter_context(open(os_fspath(file), \"rb\"))\nFileNotFoundError: [Errno 2] No such file or directory: '证'\n", "version": "monster | ******** | B", "create_time": {"$date": "2025-07-29T09:40:03.657Z"}}, {"_id": {"$oid": "6890bb8b6946627d159cc5c8"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-04 21:54:29.888379", "message": "Experiment Crash\n--------------------------------------------------\nq1-OptimizeFirDicarlo-6890bb95c7216026b418e985\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-04T21:54:19.761Z"}}, {"_id": {"$oid": "6890bb8b9192f4bbc39cc58f"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-04 21:54:29.951648", "message": "Experiment Crash\n--------------------------------------------------\nq2-OptimizeFirDicarlo-6890bb95c7216026b418e987\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-04T21:54:19.835Z"}}, {"_id": {"$oid": "6890bb8b6946627d159cc5c9"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-04 21:54:30.012648", "message": "Experiment Crash\n--------------------------------------------------\nq3-OptimizeFirDicarlo-6890bb95c7216026b418e989\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-04T21:54:19.909Z"}}, {"_id": {"$oid": "6890bb8b9192f4bbc39cc590"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-04 21:54:30.075618", "message": "Experiment Crash\n--------------------------------------------------\nq4-OptimizeFirDicarlo-6890bb95c7216026b418e98b\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-04T21:54:19.954Z"}}, {"_id": {"$oid": "6890bb8c6946627d159cc5ca"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-04 21:54:30.133616", "message": "Experiment Crash\n--------------------------------------------------\nq5-OptimizeFirDicarlo-6890bb95c7216026b418e98d\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-04T21:54:20.043Z"}}, {"_id": {"$oid": "6890bb8c6946627d159cc5cb"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-04 21:54:30.193617", "message": "Experiment Crash\n--------------------------------------------------\nq6-OptimizeFirDicarlo-6890bb95c7216026b418e98f\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-04T21:54:20.108Z"}}, {"_id": {"$oid": "6890bb8c9192f4bbc39cc591"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-04 21:54:30.255617", "message": "Experiment Crash\n--------------------------------------------------\nq7-OptimizeFirDicarlo-6890bb95c7216026b418e991\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-04T21:54:20.164Z"}}, {"_id": {"$oid": "6890bb8c6946627d159cc5cc"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-04 21:54:30.314617", "message": "Experiment Crash\n--------------------------------------------------\nq8-OptimizeFirDicarlo-6890bb95c7216026b418e993\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-04T21:54:20.209Z"}}, {"_id": {"$oid": "689170fe9192f4bbc39cc89f"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-05 10:48:41.287254", "message": "Experiment Crash\n--------------------------------------------------\nq1-OptimizeFirDicarlo-6891710990761059a6ef3650\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T10:48:30.66Z"}}, {"_id": {"$oid": "689170fe6946627d159cc89c"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-05 10:48:41.345260", "message": "Experiment Crash\n--------------------------------------------------\nq2-OptimizeFirDicarlo-6891710990761059a6ef3652\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T10:48:30.719Z"}}, {"_id": {"$oid": "689170fe6946627d159cc89d"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-05 10:48:41.404262", "message": "Experiment Crash\n--------------------------------------------------\nq3-OptimizeFirDicarlo-6891710990761059a6ef3654\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T10:48:30.812Z"}}, {"_id": {"$oid": "689170fe9192f4bbc39cc8a0"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-05 10:48:41.462269", "message": "Experiment Crash\n--------------------------------------------------\nq4-OptimizeFirDicarlo-6891710990761059a6ef3656\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T10:48:30.869Z"}}, {"_id": {"$oid": "689170fe6946627d159cc89e"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-05 10:48:41.523276", "message": "Experiment Crash\n--------------------------------------------------\nq5-OptimizeFirDicarlo-6891710990761059a6ef3658\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T10:48:30.898Z"}}, {"_id": {"$oid": "689170fe9192f4bbc39cc8a1"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-05 10:48:41.584279", "message": "Experiment Crash\n--------------------------------------------------\nq6-OptimizeFirDicarlo-6891710990761059a6ef365a\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T10:48:30.987Z"}}, {"_id": {"$oid": "689170ff6946627d159cc89f"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-05 10:48:41.641283", "message": "Experiment Crash\n--------------------------------------------------\nq7-OptimizeFirDicarlo-6891710990761059a6ef365c\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T10:48:31.038Z"}}, {"_id": {"$oid": "689170ff9192f4bbc39cc8a2"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-05 10:48:41.699321", "message": "Experiment Crash\n--------------------------------------------------\nq8-OptimizeFirDicarlo-6891710990761059a6ef365e\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T10:48:31.078Z"}}, {"_id": {"$oid": "6891b87f890a246c6cfb08ef"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-05 15:53:46.564888", "message": "Experiment Crash\n--------------------------------------------------\nq1-OptimizeFirDicarlo-6891b88a94a082d218d9f94b\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T15:53:35.835Z"}}, {"_id": {"$oid": "6891b87fb84b084e52fb0858"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-05 15:53:46.636898", "message": "Experiment Crash\n--------------------------------------------------\nq2-OptimizeFirDicarlo-6891b88a94a082d218d9f94d\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T15:53:35.895Z"}}, {"_id": {"$oid": "6891b880890a246c6cfb08f0"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-05 15:53:46.710903", "message": "Experiment Crash\n--------------------------------------------------\nq3-OptimizeFirDicarlo-6891b88a94a082d218d9f94f\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T15:53:36.027Z"}}, {"_id": {"$oid": "6891b880b84b084e52fb0859"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-05 15:53:46.777909", "message": "Experiment Crash\n--------------------------------------------------\nq4-OptimizeFirDicarlo-6891b88a94a082d218d9f951\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T15:53:36.057Z"}}, {"_id": {"$oid": "6891b880890a246c6cfb08f1"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-05 15:53:46.842915", "message": "Experiment Crash\n--------------------------------------------------\nq5-OptimizeFirDicarlo-6891b88a94a082d218d9f953\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T15:53:36.117Z"}}, {"_id": {"$oid": "6891b880b84b084e52fb085a"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-05 15:53:46.910923", "message": "Experiment Crash\n--------------------------------------------------\nq6-OptimizeFirDicarlo-6891b88a94a082d218d9f955\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T15:53:36.208Z"}}, {"_id": {"$oid": "6891b880890a246c6cfb08f2"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-05 15:53:46.997972", "message": "Experiment Crash\n--------------------------------------------------\nq7-OptimizeFirDicarlo-6891b88a94a082d218d9f957\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T15:53:36.269Z"}}, {"_id": {"$oid": "6891b880b84b084e52fb085b"}, "username": "job", "sample": "D8-72bit-4", "env_name": "D8-2-4", "level": 10, "time": "2025-08-05 15:53:47.070809", "message": "Experiment Crash\n--------------------------------------------------\nq8-OptimizeFirDicarlo-6891b88a94a082d218d9f959\n--------------------------------------------------\nE:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\_wrappers.py\", line 73, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"D:\\apps-test\\pyqcat-apps\\pyQCat\\analysis\\fit\\fit_models.py\", line 1303, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"D:\\Anaconda3\\envs\\visage_o\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: E:\\DATA\\q33\\ACSpectrum\\2024-03-05\\15.21.53\\ACSpectrum.dat not found.\n", "version": "monster | 0.23.2.1 | C", "create_time": {"$date": "2025-08-05T15:53:36.33Z"}}, {"_id": {"$oid": "6895946250c6afe211883efe"}, "username": "yxyY413", "sample": "Reset_LRU_test", "env_name": "Y8", "level": 10, "time": "2025-08-08 14:08:23.886439", "message": "Experiment Crash\n--------------------------------------------------\nq3q4c3-4-CouplerOptimizeFirDicarlo-6895945743c752d3ff4a99bb\n--------------------------------------------------\nZ:\\Y8\\yxy\\data\\250805\\QubitcellV6.1\\Reset_LRU_test\\ACSpectrumByCoupler\\q4c3-4\\2025-08-08\\11.52.57ACSpectrumByCoupler.dat not found.\n--------------------------------------------------\nTraceback (most recent call last):\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\_wrappers.py\", line 74, in wrapper\n    await func(*args, **kwargs)\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 513, in run_experiment\n    self.run()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite_experiment.py\", line 197, in run\n    self._check_options()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 511, in _check_options\n    super()._check_options()\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\experiments\\composite\\z_distortion\\optimize_fir_dicarlo.py\", line 222, in _check_options\n    func1, func2 = get_zfunc(\n  File \"D:\\code\\yxy\\naga\\0.23.2\\pyqcat-visage\\pyQCat\\analysis\\fit\\fit_models.py\", line 1330, in get_zfunc\n    data = np.genfromtxt(fname)\n  File \"D:\\software\\Anaconda\\envs\\visage\\lib\\site-packages\\numpy\\lib\\npyio.py\", line 1980, in genfromtxt\n    fid = np.lib._datasource.open(fname, 'rt', encoding=encoding)\n  File \"D:\\software\\Anaconda\\envs\\visage\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 193, in open\n    return ds.open(path, mode, encoding=encoding, newline=newline)\n  File \"D:\\software\\Anaconda\\envs\\visage\\lib\\site-packages\\numpy\\lib\\_datasource.py\", line 533, in open\n    raise FileNotFoundError(f\"{path} not found.\")\nFileNotFoundError: Z:\\Y8\\yxy\\data\\250805\\QubitcellV6.1\\Reset_LRU_test\\ACSpectrumByCoupler\\q4c3-4\\2025-08-08\\11.52.57ACSpectrumByCoupler.dat not found.\n", "version": "monster | 0.23.2 | B", "create_time": {"$date": "2025-08-08T14:08:34.068Z"}}]