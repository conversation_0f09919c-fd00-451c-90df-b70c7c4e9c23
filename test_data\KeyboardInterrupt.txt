_id	message
682c4d0bec3ac400d5d8cbbb	Experiment Fail
--------------------------------------------------
q1-CavityFreqSpectrum
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1854, in _run_once
    event_list = self._selector.select(timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 324, in select
    r, w, _ = self._select(self._readers, self._writers, [], timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 315, in _select
    r, w, x = select.select(r, w, w, timeout)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682d71e3a77f347882573a38	Experiment Fail
--------------------------------------------------
q1-SweepAmpRabiWidth-RabiScanWidthAmp(23-101)-Amp=0.22
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\composite_experiment.py", line 438, in _run_and_check
    await child_exp.run_experiment(clear=True, callback=self._handle_child_experiment)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682d71e3a77f347882573a39	Experiment Fail
--------------------------------------------------
q1-SweepAmpRabiWidth-RabiScanWidthAmp(7-101)-Amp=0.06
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\composite_experiment.py", line 438, in _run_and_check
    await child_exp.run_experiment(clear=True, callback=self._handle_child_experiment)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682d71e3a77f347882573a3a	Experiment Fail
--------------------------------------------------
q1-SweepAmpRabiWidth-RabiScanWidthAmp(100-101)-Amp=0.99
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\composite_experiment.py", line 438, in _run_and_check
    await child_exp.run_experiment(clear=True, callback=self._handle_child_experiment)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682d71e3a77f347882573a3b	Experiment Fail
--------------------------------------------------
q1-SweepAmpRabiWidth-RabiScanWidthAmp(56-101)-Amp=0.55
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\composite_experiment.py", line 438, in _run_and_check
    await child_exp.run_experiment(clear=True, callback=self._handle_child_experiment)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682d71e3a77f347882573a3c	Experiment Fail
--------------------------------------------------
q1-SweepAmpRabiWidth-RabiScanWidthAmp(10-101)-Amp=0.09
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\composite_experiment.py", line 438, in _run_and_check
    await child_exp.run_experiment(clear=True, callback=self._handle_child_experiment)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682d71e3a77f347882573a3d	Experiment Fail
--------------------------------------------------
q1-SweepAmpRabiWidth-RabiScanWidthAmp(9-101)-Amp=0.08
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\composite_experiment.py", line 438, in _run_and_check
    await child_exp.run_experiment(clear=True, callback=self._handle_child_experiment)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682d71e3a77f347882573a40	Experiment Fail
--------------------------------------------------
q1-SweepAmpRabiWidth-RabiScanWidthAmp(5-101)-Amp=0.04
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\composite_experiment.py", line 438, in _run_and_check
    await child_exp.run_experiment(clear=True, callback=self._handle_child_experiment)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682d71e3a77f347882573a3f	Experiment Fail
--------------------------------------------------
q1-SweepAmpRabiWidth-RabiScanWidthAmp(6-101)-Amp=0.05
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\composite_experiment.py", line 438, in _run_and_check
    await child_exp.run_experiment(clear=True, callback=self._handle_child_experiment)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682d71e3a77f347882573a3e	Experiment Fail
--------------------------------------------------
q1-SweepAmpRabiWidth-RabiScanWidthAmp(8-101)-Amp=0.07
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\composite_experiment.py", line 438, in _run_and_check
    await child_exp.run_experiment(clear=True, callback=self._handle_child_experiment)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682d728ea77f347882573a41	Experiment Fail
--------------------------------------------------
q1-ReadoutPowerCalibrate-SingleShot_01(8-21)-probe_power=-28
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682d728ea77f347882573a42	Experiment Fail
--------------------------------------------------
q1-ReadoutPowerCalibrate-SingleShot_01(7-21)-probe_power=-29
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682d728ea77f347882573a43	Experiment Fail
--------------------------------------------------
q1-ReadoutPowerCalibrate-SingleShot_01(10-21)-probe_power=-26
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682d728ea77f347882573a44	Experiment Fail
--------------------------------------------------
q1-ReadoutPowerCalibrate-SingleShot_01(20-21)-probe_power=-16
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682d728ea77f347882573a45	Experiment Fail
--------------------------------------------------
q1-ReadoutPowerCalibrate-SingleShot_01(9-21)-probe_power=-27
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682d75b4a77f347882573a47	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(17-31)-power=-24dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682d75b4a77f347882573a48	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(25-31)-power=-16dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682d75b4a77f347882573a49	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(21-31)-power=-20dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682d75b4a77f347882573a4a	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(15-31)-power=-26dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682d75b4a77f347882573a4b	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(11-31)-power=-30dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682d944fa77f347882573aec	Experiment Fail
--------------------------------------------------
q73~c48-54-ZExp
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1854, in _run_once
    event_list = self._selector.select(timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 324, in select
    r, w, _ = self._select(self._readers, self._writers, [], timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 315, in _select
    r, w, x = select.select(r, w, w, timeout)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682d9454a77f347882573aed	Experiment Fail
--------------------------------------------------
q1-SweepDetuneRabiWidth-RabiScanWidthDetune(23-41)-Detune=4
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682d9454a77f347882573aee	Experiment Fail
--------------------------------------------------
q1-SweepDetuneRabiWidth-RabiScanWidthDetune(38-41)-Detune=34
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682d9454a77f347882573aef	Experiment Fail
--------------------------------------------------
q1-SweepDetuneRabiWidth-RabiScanWidthDetune(19-41)-Detune=-4
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682d9454a77f347882573af0	Experiment Fail
--------------------------------------------------
q1-SweepDetuneRabiWidth-RabiScanWidthDetune(35-41)-Detune=28
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682d9454a77f347882573af1	Experiment Fail
--------------------------------------------------
q1-SweepDetuneRabiWidth-RabiScanWidthDetune(15-41)-Detune=-12
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682d9454a77f347882573af2	Experiment Fail
--------------------------------------------------
q1-SweepDetuneRabiWidth-RabiScanWidthDetune(24-41)-Detune=6
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682d9454a77f347882573af3	Experiment Fail
--------------------------------------------------
q1-SweepDetuneRabiWidth-RabiScanWidthDetune(27-41)-Detune=12
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682d94aba77f347882573af4	Experiment Fail
--------------------------------------------------
q73~c48-54-ZExp
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1854, in _run_once
    event_list = self._selector.select(timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 324, in select
    r, w, _ = self._select(self._readers, self._writers, [], timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 315, in _select
    r, w, x = select.select(r, w, w, timeout)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682d9ac0a77f347882573af5	Experiment Fail
--------------------------------------------------
q17~c5-10-ZExp
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1854, in _run_once
    event_list = self._selector.select(timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 324, in select
    r, w, _ = self._select(self._readers, self._writers, [], timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 315, in _select
    r, w, x = select.select(r, w, w, timeout)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682d9b05a77f347882573af6	Experiment Fail
--------------------------------------------------
q17~c5-10-ZExp
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1854, in _run_once
    event_list = self._selector.select(timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 324, in select
    r, w, _ = self._select(self._readers, self._writers, [], timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 315, in _select
    r, w, x = select.select(r, w, w, timeout)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682fdef2f04f1fe4feb736ba	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(30-31)-power=-11dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1854, in _run_once
    event_list = self._selector.select(timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 324, in select
    r, w, _ = self._select(self._readers, self._writers, [], timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 315, in _select
    r, w, x = select.select(r, w, w, timeout)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682fdef2f04f1fe4feb736bb	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(23-31)-power=-18dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1854, in _run_once
    event_list = self._selector.select(timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 324, in select
    r, w, _ = self._select(self._readers, self._writers, [], timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 315, in _select
    r, w, x = select.select(r, w, w, timeout)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682fdef2f04f1fe4feb736bd	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(21-31)-power=-20dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1854, in _run_once
    event_list = self._selector.select(timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 324, in select
    r, w, _ = self._select(self._readers, self._writers, [], timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 315, in _select
    r, w, x = select.select(r, w, w, timeout)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682fdef2f04f1fe4feb736be	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(18-31)-power=-23dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1854, in _run_once
    event_list = self._selector.select(timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 324, in select
    r, w, _ = self._select(self._readers, self._writers, [], timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 315, in _select
    r, w, x = select.select(r, w, w, timeout)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682fdef2f04f1fe4feb736bc	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(11-31)-power=-30dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1854, in _run_once
    event_list = self._selector.select(timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 324, in select
    r, w, _ = self._select(self._readers, self._writers, [], timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 315, in _select
    r, w, x = select.select(r, w, w, timeout)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682fdef2f04f1fe4feb736bf	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(27-31)-power=-14dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1854, in _run_once
    event_list = self._selector.select(timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 324, in select
    r, w, _ = self._select(self._readers, self._writers, [], timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 315, in _select
    r, w, x = select.select(r, w, w, timeout)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682fdef2f04f1fe4feb736c0	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(22-31)-power=-19dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1854, in _run_once
    event_list = self._selector.select(timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 324, in select
    r, w, _ = self._select(self._readers, self._writers, [], timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 315, in _select
    r, w, x = select.select(r, w, w, timeout)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682fdef2f04f1fe4feb736c1	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(31-31)-power=-10dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1854, in _run_once
    event_list = self._selector.select(timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 324, in select
    r, w, _ = self._select(self._readers, self._writers, [], timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 315, in _select
    r, w, x = select.select(r, w, w, timeout)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682fdef2f04f1fe4feb736c3	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(29-31)-power=-12dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1854, in _run_once
    event_list = self._selector.select(timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 324, in select
    r, w, _ = self._select(self._readers, self._writers, [], timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 315, in _select
    r, w, x = select.select(r, w, w, timeout)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682fdef2f04f1fe4feb736c2	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(26-31)-power=-15dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1854, in _run_once
    event_list = self._selector.select(timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 324, in select
    r, w, _ = self._select(self._readers, self._writers, [], timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 315, in _select
    r, w, x = select.select(r, w, w, timeout)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682fe0c1f04f1fe4feb736c4	Experiment Fail
--------------------------------------------------
q1-CavityFreqSpectrum
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1854, in _run_once
    event_list = self._selector.select(timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 324, in select
    r, w, _ = self._select(self._readers, self._writers, [], timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 315, in _select
    r, w, x = select.select(r, w, w, timeout)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682fedfff04f1fe4feb736d2	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(6-31)-power=-35dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1854, in _run_once
    event_list = self._selector.select(timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 324, in select
    r, w, _ = self._select(self._readers, self._writers, [], timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 315, in _select
    r, w, x = select.select(r, w, w, timeout)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682fedfff04f1fe4feb736d3	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(7-31)-power=-34dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1854, in _run_once
    event_list = self._selector.select(timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 324, in select
    r, w, _ = self._select(self._readers, self._writers, [], timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 315, in _select
    r, w, x = select.select(r, w, w, timeout)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682fedfff04f1fe4feb736d4	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(8-31)-power=-33dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1854, in _run_once
    event_list = self._selector.select(timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 324, in select
    r, w, _ = self._select(self._readers, self._writers, [], timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 315, in _select
    r, w, x = select.select(r, w, w, timeout)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682fedfff04f1fe4feb736d5	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(5-31)-power=-36dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1854, in _run_once
    event_list = self._selector.select(timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 324, in select
    r, w, _ = self._select(self._readers, self._writers, [], timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 315, in _select
    r, w, x = select.select(r, w, w, timeout)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682fedfff04f1fe4feb736d6	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(4-31)-power=-37dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1854, in _run_once
    event_list = self._selector.select(timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 324, in select
    r, w, _ = self._select(self._readers, self._writers, [], timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 315, in _select
    r, w, x = select.select(r, w, w, timeout)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682fedfff04f1fe4feb736d7	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(1-31)-power=-40dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1854, in _run_once
    event_list = self._selector.select(timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 324, in select
    r, w, _ = self._select(self._readers, self._writers, [], timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 315, in _select
    r, w, x = select.select(r, w, w, timeout)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682fedfff04f1fe4feb736d8	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(2-31)-power=-39dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1854, in _run_once
    event_list = self._selector.select(timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 324, in select
    r, w, _ = self._select(self._readers, self._writers, [], timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 315, in _select
    r, w, x = select.select(r, w, w, timeout)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682fedfff04f1fe4feb736d9	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(3-31)-power=-38dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1854, in _run_once
    event_list = self._selector.select(timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 324, in select
    r, w, _ = self._select(self._readers, self._writers, [], timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 315, in _select
    r, w, x = select.select(r, w, w, timeout)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682fedfff04f1fe4feb736da	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(9-31)-power=-32dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1854, in _run_once
    event_list = self._selector.select(timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 324, in select
    r, w, _ = self._select(self._readers, self._writers, [], timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 315, in _select
    r, w, x = select.select(r, w, w, timeout)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682fedfff04f1fe4feb736db	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(10-31)-power=-31dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1854, in _run_once
    event_list = self._selector.select(timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 324, in select
    r, w, _ = self._select(self._readers, self._writers, [], timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 315, in _select
    r, w, x = select.select(r, w, w, timeout)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

682fee56f04f1fe4feb736dc	Experiment Fail
--------------------------------------------------
q1-CavityFreqSpectrum
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1854, in _run_once
    event_list = self._selector.select(timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 324, in select
    r, w, _ = self._select(self._readers, self._writers, [], timeout)
  File "E:\Anaconda\envs\visage\lib\selectors.py", line 315, in _select
    r, w, x = select.select(r, w, w, timeout)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

6830245ff04f1fe4feb7379a	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(9-31)-power=-32dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

6830245ff04f1fe4feb7379b	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(5-31)-power=-36dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

6830245ff04f1fe4feb7379c	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(2-31)-power=-39dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

6830245ff04f1fe4feb7379d	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(3-31)-power=-38dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

6830245ff04f1fe4feb7379e	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(4-31)-power=-37dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

6830245ff04f1fe4feb7379f	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(10-31)-power=-31dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

6830245ff04f1fe4feb737a0	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(6-31)-power=-35dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

6830245ff04f1fe4feb737a1	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(7-31)-power=-34dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

6830245ff04f1fe4feb737a2	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(8-31)-power=-33dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

683024caf04f1fe4feb737a3	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(4-31)-power=-37dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

683024caf04f1fe4feb737a4	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(9-31)-power=-32dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

683024caf04f1fe4feb737a5	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(3-31)-power=-38dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

683024caf04f1fe4feb737a6	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(5-31)-power=-36dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

683024caf04f1fe4feb737a7	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(8-31)-power=-33dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

683024caf04f1fe4feb737a8	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(7-31)-power=-34dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

683024caf04f1fe4feb737a9	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(6-31)-power=-35dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

683024caf04f1fe4feb737aa	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(10-31)-power=-31dBm
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

683034a9f04f1fe4feb737ab	Experiment Fail
--------------------------------------------------
q1-ACBiasTunable-CavityFreqSpectrum(40-54)-ac_bias = 0.185v
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

683034a9f04f1fe4feb737ac	Experiment Fail
--------------------------------------------------
q1-ACBiasTunable-CavityFreqSpectrum(48-54)-ac_bias = 0.305v
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

683034a9f04f1fe4feb737ad	Experiment Fail
--------------------------------------------------
q1-ACBiasTunable-CavityFreqSpectrum(20-54)-ac_bias = -0.115v
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

683034a9f04f1fe4feb737ae	Experiment Fail
--------------------------------------------------
q1-ACBiasTunable-CavityFreqSpectrum(28-54)-ac_bias = 0.005v
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

683034a9f04f1fe4feb737af	Experiment Fail
--------------------------------------------------
q1-ACBiasTunable-CavityFreqSpectrum(30-54)-ac_bias = 0.035v
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

683034a9f04f1fe4feb737b0	Experiment Fail
--------------------------------------------------
q1-ACBiasTunable-CavityFreqSpectrum(16-54)-ac_bias = -0.175v
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

683034a9f04f1fe4feb737b1	Experiment Fail
--------------------------------------------------
q1-ACBiasTunable-CavityFreqSpectrum(26-54)-ac_bias = -0.025v
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "E:\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
  File "E:\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
  File "E:\Anaconda\envs\visage\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 514, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 166, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 705, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "F:\Monitor\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 772, in _async_get_measure_data
    time.sleep(0.01)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 611, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "F:\Monitor\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 519, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

6830477df04f1fe4feb7380e	Experiment Fail
--------------------------------------------------
q2-SingleShot_01
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "C:\ProgramData\miniconda3\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\ProgramData\miniconda3\envs\visage\lib\asyncio\base_events.py", line 634, in run_until_complete
    self.run_forever()
  File "C:\ProgramData\miniconda3\envs\visage\lib\asyncio\base_events.py", line 601, in run_forever
    self._run_once()
  File "C:\ProgramData\miniconda3\envs\visage\lib\asyncio\base_events.py", line 1869, in _run_once
    event_list = self._selector.select(timeout)
  File "C:\ProgramData\miniconda3\envs\visage\lib\selectors.py", line 324, in select
    r, w, _ = self._select(self._readers, self._writers, [], timeout)
  File "C:\ProgramData\miniconda3\envs\visage\lib\selectors.py", line 315, in _select
    r, w, x = select.select(r, w, w, timeout)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\code\Y3-Online\AutoCalibration\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\AutoCalibration\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 612, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\Y3-Online\AutoCalibration\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 520, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

68304c71f04f1fe4feb7380f	Experiment Fail
--------------------------------------------------
q3-SingleShot_01
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
--------------------------------------------------
Traceback (most recent call last):
  File "C:\ProgramData\miniconda3\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\ProgramData\miniconda3\envs\visage\lib\asyncio\base_events.py", line 634, in run_until_complete
    self.run_forever()
  File "C:\ProgramData\miniconda3\envs\visage\lib\asyncio\base_events.py", line 601, in run_forever
    self._run_once()
  File "C:\ProgramData\miniconda3\envs\visage\lib\asyncio\base_events.py", line 1869, in _run_once
    event_list = self._selector.select(timeout)
  File "C:\ProgramData\miniconda3\envs\visage\lib\selectors.py", line 324, in select
    r, w, _ = self._select(self._readers, self._writers, [], timeout)
  File "C:\ProgramData\miniconda3\envs\visage\lib\selectors.py", line 315, in _select
    r, w, x = select.select(r, w, w, timeout)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\code\Y3-Online\AutoCalibration\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 71, in wrapper
    await func(*args, **kwargs)
  File "D:\code\Y3-Online\AutoCalibration\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 612, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\Y3-Online\AutoCalibration\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 520, in _async_analysis
    raise AnalysisError(msg=str(result))
pyQCat.errors.AnalysisError: <Analysis Error> | User KeyboardInterrupt

68381bddf04f1fe4feb73d3d	Experiment Fail
--------------------------------------------------
q1-SingleShot_01-683810af8aa810b9267552eb
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6839176ef04f1fe4feb73e41	Experiment Fail
--------------------------------------------------
q40-ECCircuit1-6839174309bc5c665127e2ce
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
683ec32ff04f1fe4feb7469a	Experiment Fail
--------------------------------------------------
q3q8c3-8-Swap-SwapOnce(2-21)-scan q3 4404.947MHz-683ec337697f5a145910becd
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
683ec32ff04f1fe4feb74699	Experiment Fail
--------------------------------------------------
q3q8c3-8-Swap-SwapOnce(9-21)-scan q3 4418.947MHz-683ec342697f5a145910bed4
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
683ec32ff04f1fe4feb7469c	Experiment Fail
--------------------------------------------------
q3q8c3-8-Swap-SwapOnce(10-21)-scan q3 4420.947MHz-683ec343697f5a145910bed5
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
683ec32ff04f1fe4feb7469b	Experiment Fail
--------------------------------------------------
q3q8c3-8-Swap-SwapOnce(3-21)-scan q3 4406.947MHz-683ec338697f5a145910bece
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
683ec32ff04f1fe4feb74698	Experiment Fail
--------------------------------------------------
q3q8c3-8-Swap-SwapOnce(6-21)-scan q3 4412.947MHz-683ec341697f5a145910bed1
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
683ec32ff04f1fe4feb7469d	Experiment Fail
--------------------------------------------------
q3q8c3-8-Swap-SwapOnce(5-21)-scan q3 4410.947MHz-683ec339697f5a145910bed0
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
683ec32ff04f1fe4feb7469e	Experiment Fail
--------------------------------------------------
q3q8c3-8-Swap-SwapOnce(11-21)-scan q3 4422.947MHz-683ec343697f5a145910bed6
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
683ec32ff04f1fe4feb7469f	Experiment Fail
--------------------------------------------------
q3q8c3-8-Swap-SwapOnce(8-21)-scan q3 4416.947MHz-683ec342697f5a145910bed3
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
683ec32ff04f1fe4feb746a0	Experiment Fail
--------------------------------------------------
q3q8c3-8-Swap-SwapOnce(4-21)-scan q3 4408.947MHz-683ec338697f5a145910becf
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
683ec32ff04f1fe4feb746a1	Experiment Crash
--------------------------------------------------
q3q8c3-8-Swap-683ec337697f5a145910bec6
--------------------------------------------------
list index out of range
--------------------------------------------------
Traceback (most recent call last):
  File "C:\ProgramData\Miniconda3\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\ProgramData\Miniconda3\lib\asyncio\base_events.py", line 634, in run_until_complete
    self.run_forever()
  File "C:\ProgramData\Miniconda3\lib\asyncio\base_events.py", line 601, in run_forever
    self._run_once()
  File "C:\ProgramData\Miniconda3\lib\asyncio\base_events.py", line 1905, in _run_once
    handle._run()
  File "C:\ProgramData\Miniconda3\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\ProgramData\Miniconda3\lib\asyncio\futures.py", line 312, in _set_result_unless_cancelled
    fut.set_result(result)
  File "C:\ProgramData\Miniconda3\lib\asyncio\base_events.py", line 755, in call_soon
    handle = self._call_soon(callback, args, context)
  File "C:\ProgramData\Miniconda3\lib\asyncio\base_events.py", line 771, in _call_soon
    handle = events.Handle(callback, args, self, context)
  File "C:\ProgramData\Miniconda3\lib\asyncio\events.py", line 34, in __init__
    self._context = context
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\lzw\code\GitProject\0.23.1\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 72, in wrapper
    await func(*args, **kwargs)
  File "E:\lzw\code\GitProject\0.23.1\pyqcat-apps\pyQCat\experiments\composite_experiment.py", line 507, in run_experiment
    await self._async_composite_run_base()
  File "E:\lzw\code\GitProject\0.23.1\pyqcat-apps\pyQCat\experiments\composite_experiment.py", line 458, in _async_composite_run_base
    await self._async_run_analysis()
  File "E:\lzw\code\GitProject\0.23.1\pyqcat-apps\pyQCat\experiments\composite_experiment.py", line 492, in _async_run_analysis
    self._analysis = run_analysis_process(*args)
  File "E:\lzw\code\GitProject\0.23.1\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 254, in run_analysis_process
    analysis_obj.run_analysis()
  File "E:\lzw\code\GitProject\0.23.1\pyqcat-apps\pyQCat\parameters.py", line 714, in _freq2amp_run_analysis
    origin_run_analysis(self)
  File "E:\lzw\code\GitProject\0.23.1\pyqcat-apps\pyQCat\analysis\library\swap_analysis.py", line 293, in run_analysis
    super().run_analysis()
  File "E:\lzw\code\GitProject\0.23.1\pyqcat-apps\pyQCat\analysis\curve_analysis.py", line 424, in run_analysis
    self._extract_result(better_data_key)
  File "E:\lzw\code\GitProject\0.23.1\pyqcat-apps\pyQCat\analysis\library\swap_analysis.py", line 187, in _extract_result
    self.experiment_data.child_data(i)
  File "E:\lzw\code\GitProject\0.23.1\pyqcat-apps\pyQCat\structures.py", line 726, in child_data
    return list(self._child_data.values())[index]
IndexError: list index out of range

683ec32ff04f1fe4feb746a2	Experiment Fail
--------------------------------------------------
q3q8c3-8-Swap-SwapOnce(7-21)-scan q3 4414.947MHz-683ec342697f5a145910bed2
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
683ec431f04f1fe4feb746a3	Experiment Fail
--------------------------------------------------
q3q8c3-8-FixedSwapFreqCaliCoupler-SwapOnce(3-unknow)-idx=2, z_amp=-0.21448-683ec446829218e08ff5846b
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
683ec431f04f1fe4feb746a4	Experiment Crash
--------------------------------------------------
cali(c3-8)-pair(q3q8)-FixedSwapFreqCaliCoupler-683ec42c829218e08ff58467
--------------------------------------------------
'dict' object has no attribute 'freq'
--------------------------------------------------
Traceback (most recent call last):
  File "C:\ProgramData\Miniconda3\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\ProgramData\Miniconda3\lib\asyncio\base_events.py", line 634, in run_until_complete
    self.run_forever()
  File "C:\ProgramData\Miniconda3\lib\asyncio\base_events.py", line 601, in run_forever
    self._run_once()
  File "C:\ProgramData\Miniconda3\lib\asyncio\base_events.py", line 1869, in _run_once
    event_list = self._selector.select(timeout)
  File "C:\ProgramData\Miniconda3\lib\selectors.py", line 324, in select
    r, w, _ = self._select(self._readers, self._writers, [], timeout)
  File "C:\ProgramData\Miniconda3\lib\selectors.py", line 315, in _select
    r, w, x = select.select(r, w, w, timeout)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\lzw\code\GitProject\0.23.1\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 72, in wrapper
    await func(*args, **kwargs)
  File "E:\lzw\code\GitProject\0.23.1\pyqcat-apps\pyQCat\experiments\composite_experiment.py", line 505, in run_experiment
    await self._sync_composite_run()
  File "E:\lzw\code\GitProject\0.23.1\pyqcat-apps\pyQCat\experiments\composite\calibration\voltage_drift_calibration.py", line 1426, in _sync_composite_run
    await self._calculate_gradient()
  File "E:\lzw\code\GitProject\0.23.1\pyqcat-apps\pyQCat\experiments\composite\calibration\voltage_drift_calibration.py", line 1190, in _calculate_gradient
    delta_f = await self._run_child_experiment(
  File "E:\lzw\code\GitProject\0.23.1\pyqcat-apps\pyQCat\experiments\composite\calibration\voltage_drift_calibration.py", line 1406, in _run_child_experiment
    osc_freq = child_exp.analysis.results.freq.value
AttributeError: 'dict' object has no attribute 'freq'

683ffabbf04f1fe4feb74877	Experiment Fail
--------------------------------------------------
q11q6c6-11-Swap-SwapOnce(6-21)-scan q6 4322.554MHz-683ffaaa01f34bca87e01dd7
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
683ffabbf04f1fe4feb74878	Experiment Fail
--------------------------------------------------
q11q6c6-11-Swap-SwapOnce(7-21)-scan q6 4324.554MHz-683ffaaa01f34bca87e01dd8
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
683ffabbf04f1fe4feb74879	Experiment Fail
--------------------------------------------------
q11q6c6-11-Swap-SwapOnce(10-21)-scan q6 4330.554MHz-683ffaab01f34bca87e01ddb
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
683ffabbf04f1fe4feb7487a	Experiment Fail
--------------------------------------------------
q11q6c6-11-Swap-SwapOnce(5-21)-scan q6 4320.554MHz-683ffaa201f34bca87e01dd6
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
683ffabbf04f1fe4feb7487b	Experiment Fail
--------------------------------------------------
q11q6c6-11-Swap-SwapOnce(11-21)-scan q6 4332.554MHz-683ffaab01f34bca87e01ddc
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
683ffabbf04f1fe4feb7487c	Experiment Fail
--------------------------------------------------
q11q6c6-11-Swap-SwapOnce(2-21)-scan q6 4314.554MHz-683ffaa101f34bca87e01dd3
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
683ffabbf04f1fe4feb7487d	Experiment Fail
--------------------------------------------------
q11q6c6-11-Swap-SwapOnce(3-21)-scan q6 4316.554MHz-683ffaa101f34bca87e01dd4
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
683ffabbf04f1fe4feb7487e	Experiment Fail
--------------------------------------------------
q11q6c6-11-Swap-SwapOnce(9-21)-scan q6 4328.554MHz-683ffaab01f34bca87e01dda
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
683ffabbf04f1fe4feb7487f	Experiment Fail
--------------------------------------------------
q11q6c6-11-Swap-SwapOnce(8-21)-scan q6 4326.554MHz-683ffaaa01f34bca87e01dd9
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
683ffabbf04f1fe4feb74880	Experiment Fail
--------------------------------------------------
q11q6c6-11-Swap-SwapOnce(4-21)-scan q6 4318.554MHz-683ffaa201f34bca87e01dd5
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68400d2df04f1fe4feb74883	Experiment Fail
--------------------------------------------------
q11q6c6-11-FixedPointCalibration-RamseyExtend(1-unknow)-idx=0, z_amp=0.37575Vtarget_freq_osc=4335.596501_25MHz,-68400cf4c93815409a1dbf76
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
685e693da47f89e2f61e94c4	Experiment Fail
--------------------------------------------------
q2-QubitFreqCalibration-Ramsey(1-2)-fringe=50MHz-685e6904f78008ff5a456b04
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
685e693d9c3e8a30651e9299	Experiment Fail
--------------------------------------------------
q2-QubitFreqCalibration-Ramsey(2-2)-fringe=-50MHz-685e6904f78008ff5a456b05
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
685e6ac1a47f89e2f61e94c6	Experiment Fail
--------------------------------------------------
q94-XYZTimingComposite-XYZTiming(2-unknow)-count-1-xy_delay-16.2710352-z_delay-0.0-685e6aa98d95988d34761d2f
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68633442a0b5e01b01b32985	Experiment Fail
--------------------------------------------------
q44-RabiScanWidthF12-686334ac770613d63cc67a15
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686487c7a0b5e01b01b32f43	Experiment Fail
--------------------------------------------------
q2-QubitFreqCalibration-Ramsey(2-2)-fringe=-50MHz-6864880bfb8131c95224c7f9
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686487c7952d8dd2e7b32d0d	Experiment Fail
--------------------------------------------------
q2-QubitFreqCalibration-Ramsey(1-2)-fringe=50MHz-6864880bfb8131c95224c7f8
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6864a7cda0b5e01b01b32f85	Experiment Fail
--------------------------------------------------
q14q8-XYCrossRabiWidthOnce-6864a7e19f084f06721f5d29
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6865e911a47f89e2f61ea61d	Experiment Fail
--------------------------------------------------
q-circuit-QCloudPlusV1-6865e90027f0955eac64d59b
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6865e9279c3e8a30651ea4f4	Experiment Fail
--------------------------------------------------
q-circuit-QCloudPlusV1-6865e9825ac74930ffc79603
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6865ed08a47f89e2f61ea637	Experiment Fail
--------------------------------------------------
q-circuit-QCloudPlusV1-6865ed3cd8023221d85dc642
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6865ef2d952d8dd2e7b32e7c	Experiment Fail
--------------------------------------------------
q-circuit-QCloudPlusV1-6865eda54a3ec7f42873c7a8
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686743719c3e8a30651ea608	Experiment Crash
--------------------------------------------------
q34q40-SingleQubitPhase-6865f954094cb35de7940b45
--------------------------------------------------
list index out of range
--------------------------------------------------
Traceback (most recent call last):
  File "C:\ProgramData\miniconda3\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\ProgramData\miniconda3\envs\visage\lib\asyncio\base_events.py", line 634, in run_until_complete
    self.run_forever()
  File "C:\ProgramData\miniconda3\envs\visage\lib\asyncio\base_events.py", line 601, in run_forever
    self._run_once()
  File "C:\ProgramData\miniconda3\envs\visage\lib\asyncio\base_events.py", line 1869, in _run_once
    event_list = self._selector.select(timeout)
  File "C:\ProgramData\miniconda3\envs\visage\lib\selectors.py", line 324, in select
    r, w, _ = self._select(self._readers, self._writers, [], timeout)
  File "C:\ProgramData\miniconda3\envs\visage\lib\selectors.py", line 315, in _select
    r, w, x = select.select(r, w, w, timeout)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\experiments\composite_experiment.py", line 518, in run_experiment
    await self._async_composite_run_base()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\experiments\composite_experiment.py", line 469, in _async_composite_run_base
    await self._async_run_analysis()
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\experiments\composite_experiment.py", line 489, in _async_run_analysis
    experiment_data = self._create_composite_experiment_data(x_data)
  File "D:\code\ycf\Y3project\Git-Project\0.23.2\pyqcat-apps\pyQCat\experiments\composite\two_qubit_gate\single_qubit_phase.py", line 191, in _create_composite_experiment_data
    new_phase_list.append(phase_list[idx + 1] - phase_list[idx])
IndexError: list index out of range

686d1b8adb014d4cc803021d	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(10-25)-changing_phase=7.497 amp=None-686d19996f8e0ba17caf7cc8
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686d1b8a59896c8d2c02fdd1	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(23-25)-changing_phase=18.326 amp=None-686d199d6f8e0ba17caf7cd5
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686d1b8adb014d4cc803021e	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(25-25)-changing_phase=19.992 amp=None-686d199e6f8e0ba17caf7cd7
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686d1b8adb014d4cc803021f	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(5-25)-changing_phase=3.332 amp=None-686d19876f8e0ba17caf7cc3
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686d1b8adb014d4cc8030220	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(17-25)-changing_phase=13.328 amp=None-686d199d6f8e0ba17caf7ccf
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686d1b8a59896c8d2c02fdd2	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(9-25)-changing_phase=6.664 amp=None-686d19956f8e0ba17caf7cc7
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686d1b8adb014d4cc8030221	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(19-25)-changing_phase=14.994 amp=None-686d199d6f8e0ba17caf7cd1
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686d1b8a59896c8d2c02fdd3	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(6-25)-changing_phase=4.165 amp=None-686d198a6f8e0ba17caf7cc4
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686d1b8a59896c8d2c02fdd4	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(8-25)-changing_phase=5.831 amp=None-686d19926f8e0ba17caf7cc6
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686d1b8a59896c8d2c02fdd5	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(7-25)-changing_phase=4.998 amp=None-686d198e6f8e0ba17caf7cc5
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686dd5f2db014d4cc803025e	Experiment Fail
--------------------------------------------------
q96-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(19-25)-changing_phase=14.994 amp=None-686dd25b5c8815f41f296d51
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686dd5f259896c8d2c02fe3f	Experiment Fail
--------------------------------------------------
q96-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(9-25)-changing_phase=6.664 amp=None-686dd2525c8815f41f296d47
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686dd5f259896c8d2c02fe40	Experiment Fail
--------------------------------------------------
q96-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(25-25)-changing_phase=19.992 amp=None-686dd25b5c8815f41f296d57
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686dd5f259896c8d2c02fe41	Experiment Fail
--------------------------------------------------
q96-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(23-25)-changing_phase=18.326 amp=None-686dd25b5c8815f41f296d55
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686dd5f2db014d4cc803025f	Experiment Fail
--------------------------------------------------
q96-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(17-25)-changing_phase=13.328 amp=None-686dd25b5c8815f41f296d4f
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686dd5f2db014d4cc8030260	Experiment Fail
--------------------------------------------------
q96-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(11-25)-changing_phase=8.33 amp=None-686dd25a5c8815f41f296d49
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686dd5f2db014d4cc8030261	Experiment Fail
--------------------------------------------------
q96-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(8-25)-changing_phase=5.831 amp=None-686dd24e5c8815f41f296d46
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686dd5f2db014d4cc8030262	Experiment Fail
--------------------------------------------------
q96-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(10-25)-changing_phase=7.497 amp=None-686dd2565c8815f41f296d48
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686dd5f259896c8d2c02fe42	Experiment Fail
--------------------------------------------------
q96-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(13-25)-changing_phase=9.996 amp=None-686dd25a5c8815f41f296d4b
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686dd5f259896c8d2c02fe43	Experiment Fail
--------------------------------------------------
q96-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(21-25)-changing_phase=16.66 amp=None-686dd25b5c8815f41f296d53
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686de06d59896c8d2c02fe48	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(13-25)-changing_phase=9.996 amp=None-686de0a304445c3a6fad47f7
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686de06ddb014d4cc8030268	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(5-25)-changing_phase=3.332 amp=None-686de04a04445c3a6fad47ef
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686de06ddb014d4cc8030269	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(6-25)-changing_phase=4.165 amp=None-686de05604445c3a6fad47f0
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686de06d59896c8d2c02fe49	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(9-25)-changing_phase=6.664 amp=None-686de07c04445c3a6fad47f3
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686de06d59896c8d2c02fe4a	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(7-25)-changing_phase=4.998 amp=None-686de06204445c3a6fad47f1
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686de06ddb014d4cc803026a	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(10-25)-changing_phase=7.497 amp=None-686de08804445c3a6fad47f4
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686de06ddb014d4cc803026b	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(4-25)-changing_phase=2.499 amp=None-686de03c04445c3a6fad47ee
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686de06ddb014d4cc803026c	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(8-25)-changing_phase=5.831 amp=None-686de07004445c3a6fad47f2
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686de06ddb014d4cc803026d	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(3-25)-changing_phase=1.666 amp=None-686de03004445c3a6fad47ed
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686de06ddb014d4cc803026e	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(11-25)-changing_phase=8.33 amp=None-686de09604445c3a6fad47f5
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686de30659896c8d2c02fea9	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(8-25)-changing_phase=5.831 amp=None-686de23ec810b6ce4545ce0a
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686de306db014d4cc80302ce	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(3-25)-changing_phase=1.666 amp=None-686de1fbc810b6ce4545ce05
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686de30659896c8d2c02feaa	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(9-25)-changing_phase=6.664 amp=None-686de24bc810b6ce4545ce0b
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686de30659896c8d2c02feab	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(4-25)-changing_phase=2.499 amp=None-686de208c810b6ce4545ce06
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686de30659896c8d2c02feac	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(6-25)-changing_phase=4.165 amp=None-686de224c810b6ce4545ce08
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686de306db014d4cc80302cf	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(7-25)-changing_phase=4.998 amp=None-686de231c810b6ce4545ce09
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686de306db014d4cc80302d0	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(5-25)-changing_phase=3.332 amp=None-686de217c810b6ce4545ce07
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686de306db014d4cc80302d1	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(12-25)-changing_phase=9.163 amp=None-686de2a2c810b6ce4545ce0e
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686de306db014d4cc80302d2	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakage-FloquetCalibrationSingleLeakageOnce(10-25)-changing_phase=7.497 amp=None-686de258c810b6ce4545ce0c
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686e1046db014d4cc80302e4	Experiment Fail
--------------------------------------------------
q89-FloquetLeakage-686e0a8e05d2afbbea60d655
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686e17a359896c8d2c02febd	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(24-30)-changing_phase=4.983215933280362 amp=None-686e09cb2c9f37f6a69faa0e
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686e17a3db014d4cc80302e5	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(20-30)-changing_phase=4.116569684014212 amp=None-686e09cb2c9f37f6a69faa0a
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686e17a3db014d4cc80302e6	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(28-30)-changing_phase=5.849862182546511 amp=None-686e09cb2c9f37f6a69faa12
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686e17a3db014d4cc80302e7	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(29-30)-changing_phase=6.066523744863049 amp=None-686e09cc2c9f37f6a69faa13
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686e17a3db014d4cc80302e8	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(16-30)-changing_phase=3.249923434748062 amp=None-686e09ca2c9f37f6a69faa06
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686e17a359896c8d2c02febe	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(30-30)-changing_phase=6.283185307179586 amp=None-686e09cc2c9f37f6a69faa14
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686e17a359896c8d2c02febf	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(19-30)-changing_phase=3.8999081216976745 amp=None-686e09cb2c9f37f6a69faa09
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686e17a359896c8d2c02fec0	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(17-30)-changing_phase=3.4665849970645994 amp=None-686e09ca2c9f37f6a69faa07
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686e17a3db014d4cc80302e9	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(11-30)-changing_phase=2.1666156231653746 amp=None-686e09ca2c9f37f6a69faa01
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686e17a359896c8d2c02fec1	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(14-30)-changing_phase=2.8166003101149872 amp=None-686e09ca2c9f37f6a69faa04
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686f579a59c875935d33fa9c	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(10-30)-changing_phase=1.9499540608488373 amp=None-686f575cd427fbba07f8db3d
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686f579a5f0e2b5dc933fbae	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(5-30)-changing_phase=0.8666462492661499 amp=None-686f5700d427fbba07f8db38
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686f579a59c875935d33fa9d	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(8-30)-changing_phase=1.5166309362157622 amp=None-686f5738d427fbba07f8db3b
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686f579a59c875935d33fa9e	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(28-30)-changing_phase=5.849862182546511 amp=None-686f5770d427fbba07f8db4f
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686f579a59c875935d33fa9f	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(2-30)-changing_phase=0.21666156231653746 amp=None-686f56cad427fbba07f8db35
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686f579a59c875935d33faa0	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(7-30)-changing_phase=1.2999693738992248 amp=None-686f5726d427fbba07f8db3a
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686f579a59c875935d33faa1	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(6-30)-changing_phase=1.0833078115826873 amp=None-686f5713d427fbba07f8db39
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686f5e2359896c8d2c030331	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(9-30)-changing_phase=1.7332924985322997 amp=None-686f5c27312c193d04ae5915
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686f5e23db014d4cc803076f	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(24-30)-changing_phase=4.983215933280362 amp=None-686f5c45312c193d04ae5924
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686f5e2359896c8d2c030332	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(8-30)-changing_phase=1.5166309362157622 amp=None-686f5c19312c193d04ae5914
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686f5e2359896c8d2c030333	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(10-30)-changing_phase=1.9499540608488373 amp=None-686f5c35312c193d04ae5916
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686f5e23db014d4cc8030770	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(7-30)-changing_phase=1.2999693738992248 amp=None-686f5c0c312c193d04ae5913
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686f5e23db014d4cc8030771	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(6-30)-changing_phase=1.0833078115826873 amp=None-686f5bfd312c193d04ae5912
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686f5e2359896c8d2c030334	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(18-30)-changing_phase=3.683246559381137 amp=None-686f5c44312c193d04ae591e
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686f5e2359896c8d2c030335	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(5-30)-changing_phase=0.8666462492661499 amp=None-686f5bee312c193d04ae5911
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686faf0adb014d4cc8030931	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(9-30)-changing_phase=1.7332924985322997 amp=None-686fa638ee2bf498c38ed273
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686faf0a59896c8d2c0303e6	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(23-30)-changing_phase=4.766554370963824 amp=None-686fa657ee2bf498c38ed281
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686faf0a59896c8d2c0303e7	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(7-30)-changing_phase=1.2999693738992248 amp=None-686fa619ee2bf498c38ed271
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686faf0a59896c8d2c0303e8	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(27-30)-changing_phase=5.6332006202299745 amp=None-686fa657ee2bf498c38ed285
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686faf0a59896c8d2c0303e9	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(5-30)-changing_phase=0.8666462492661499 amp=None-686fa5fdee2bf498c38ed26f
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686faf0adb014d4cc8030932	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(8-30)-changing_phase=1.5166309362157622 amp=None-686fa628ee2bf498c38ed272
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686faf0adb014d4cc8030933	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(15-30)-changing_phase=3.0332618724315243 amp=None-686fa656ee2bf498c38ed279
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686faf0adb014d4cc8030934	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(6-30)-changing_phase=1.0833078115826873 amp=None-686fa60bee2bf498c38ed270
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
686faf0adb014d4cc8030935	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(10-30)-changing_phase=1.9499540608488373 amp=None-686fa647ee2bf498c38ed274
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b754db014d4cc8030a77	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(46-60)-changing_phase=4.7922599800522265 amp=None-6870a18a03df365bae51f5cd
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b75459896c8d2c0305b5	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(30-60)-changing_phase=3.0883453204781017 amp=None-6870a0b103df365bae51f5bd
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b75459896c8d2c0305b6	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(32-60)-changing_phase=3.3013346529248673 amp=None-6870a0d403df365bae51f5bf
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b754db014d4cc8030a78	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(59-60)-changing_phase=6.1766906409562035 amp=None-6870a18b03df365bae51f5da
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b754db014d4cc8030a79	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(56-60)-changing_phase=5.857206642286055 amp=None-6870a18b03df365bae51f5d7
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b754db014d4cc8030a7a	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(48-60)-changing_phase=5.005249312498992 amp=None-6870a18a03df365bae51f5cf
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b754db014d4cc8030a7b	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(33-60)-changing_phase=3.40782931914825 amp=None-6870a12103df365bae51f5c0
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b754db014d4cc8030a7d	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(49-60)-changing_phase=5.111743978722375 amp=None-6870a18a03df365bae51f5d0
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b754db014d4cc8030a7c	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(60-60)-changing_phase=6.283185307179586 amp=None-6870a18c03df365bae51f5db
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b754db014d4cc8030a7e	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(34-60)-changing_phase=3.514323985371633 amp=None-6870a13203df365bae51f5c1
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b754db014d4cc8030a7f	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(52-60)-changing_phase=5.431227977392523 amp=None-6870a18b03df365bae51f5d3
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b754db014d4cc8030a80	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(43-60)-changing_phase=4.472775981382078 amp=None-6870a18a03df365bae51f5ca
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b754db014d4cc8030a81	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(51-60)-changing_phase=5.32473331116914 amp=None-6870a18b03df365bae51f5d2
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b75459896c8d2c0305b7	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(47-60)-changing_phase=4.898754646275609 amp=None-6870a18a03df365bae51f5ce
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b75459896c8d2c0305b8	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(57-60)-changing_phase=5.963701308509438 amp=None-6870a18b03df365bae51f5d8
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b75459896c8d2c0305b9	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(41-60)-changing_phase=4.259786648935313 amp=None-6870a18a03df365bae51f5c8
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b75459896c8d2c0305ba	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(58-60)-changing_phase=6.070195974732821 amp=None-6870a18b03df365bae51f5d9
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b75459896c8d2c0305bb	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(37-60)-changing_phase=3.833807984041781 amp=None-6870a16903df365bae51f5c4
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b75459896c8d2c0305bc	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(50-60)-changing_phase=5.218238644945758 amp=None-6870a18b03df365bae51f5d1
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b75459896c8d2c0305bd	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(36-60)-changing_phase=3.7273133178183984 amp=None-6870a15703df365bae51f5c3
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b75459896c8d2c0305be	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(38-60)-changing_phase=3.940302650265164 amp=None-6870a17903df365bae51f5c5
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b75459896c8d2c0305bf	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(31-60)-changing_phase=3.1948399867014845 amp=None-6870a0c203df365bae51f5be
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b75459896c8d2c0305c0	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(39-60)-changing_phase=4.046797316488547 amp=None-6870a18a03df365bae51f5c6
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b755db014d4cc8030a82	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(55-60)-changing_phase=5.750711976062672 amp=None-6870a18b03df365bae51f5d6
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b755db014d4cc8030a83	Experiment Fail
--------------------------------------------------
q63-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(35-60)-changing_phase=3.6208186515950156 amp=None-6870a14503df365bae51f5c2
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b75759896c8d2c0305c1	Experiment Fail
--------------------------------------------------
q84-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(25-60)-changing_phase=2.5558719893611874 amp=None-6870a08aef3da316d396fc1c
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b757db014d4cc8030a84	Experiment Fail
--------------------------------------------------
q84-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(39-60)-changing_phase=4.046797316488547 amp=None-6870a196ef3da316d396fc2a
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b757db014d4cc8030a85	Experiment Fail
--------------------------------------------------
q84-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(33-60)-changing_phase=3.40782931914825 amp=None-6870a150ef3da316d396fc24
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b75759896c8d2c0305c2	Experiment Fail
--------------------------------------------------
q84-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(48-60)-changing_phase=5.005249312498992 amp=None-6870a197ef3da316d396fc33
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b75759896c8d2c0305c3	Experiment Fail
--------------------------------------------------
q84-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(49-60)-changing_phase=5.111743978722375 amp=None-6870a197ef3da316d396fc34
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b75759896c8d2c0305c4	Experiment Fail
--------------------------------------------------
q84-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(58-60)-changing_phase=6.070195974732821 amp=None-6870a198ef3da316d396fc3d
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b757db014d4cc8030a86	Experiment Fail
--------------------------------------------------
q84-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(26-60)-changing_phase=2.66236665558457 amp=None-6870a0d8ef3da316d396fc1d
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b757db014d4cc8030a87	Experiment Fail
--------------------------------------------------
q84-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(27-60)-changing_phase=2.768861321807953 amp=None-6870a0e9ef3da316d396fc1e
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b757db014d4cc8030a88	Experiment Fail
--------------------------------------------------
q84-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(51-60)-changing_phase=5.32473331116914 amp=None-6870a197ef3da316d396fc36
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b757db014d4cc8030a89	Experiment Fail
--------------------------------------------------
q84-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(29-60)-changing_phase=2.981850654254719 amp=None-6870a10aef3da316d396fc20
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b757db014d4cc8030a8a	Experiment Fail
--------------------------------------------------
q84-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(60-60)-changing_phase=6.283185307179586 amp=None-6870a198ef3da316d396fc3f
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b757db014d4cc8030a8b	Experiment Fail
--------------------------------------------------
q84-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(50-60)-changing_phase=5.218238644945758 amp=None-6870a197ef3da316d396fc35
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b75759896c8d2c0305c5	Experiment Fail
--------------------------------------------------
q84-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(52-60)-changing_phase=5.431227977392523 amp=None-6870a197ef3da316d396fc37
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b75759896c8d2c0305c6	Experiment Fail
--------------------------------------------------
q84-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(40-60)-changing_phase=4.15329198271193 amp=None-6870a196ef3da316d396fc2b
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b75759896c8d2c0305c7	Experiment Fail
--------------------------------------------------
q84-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(43-60)-changing_phase=4.472775981382078 amp=None-6870a197ef3da316d396fc2e
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b75759896c8d2c0305c8	Experiment Fail
--------------------------------------------------
q84-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(30-60)-changing_phase=3.0883453204781017 amp=None-6870a11fef3da316d396fc21
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b75759896c8d2c0305c9	Experiment Fail
--------------------------------------------------
q84-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(42-60)-changing_phase=4.366281315158695 amp=None-6870a197ef3da316d396fc2d
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b75759896c8d2c0305ca	Experiment Fail
--------------------------------------------------
q84-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(28-60)-changing_phase=2.875355988031336 amp=None-6870a0f9ef3da316d396fc1f
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870b75759896c8d2c0305cb	Experiment Fail
--------------------------------------------------
q84-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(41-60)-changing_phase=4.259786648935313 amp=None-6870a196ef3da316d396fc2c
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870d96d59c875935d33ff30	Experiment Fail
--------------------------------------------------
q22-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(20-30)-changing_phase=4.116569684014212 amp=None-6870b98aac4705028caae763
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870d96d5f0e2b5dc934006a	Experiment Fail
--------------------------------------------------
q22-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(30-30)-changing_phase=6.283185307179586 amp=None-6870ba4fac4705028caae76d
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870d96d5f0e2b5dc934006b	Experiment Fail
--------------------------------------------------
q22-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(19-30)-changing_phase=3.8999081216976745 amp=None-6870b976ac4705028caae762
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870d96d59c875935d33ff31	Experiment Fail
--------------------------------------------------
q22-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(24-30)-changing_phase=4.983215933280362 amp=None-6870b9d8ac4705028caae767
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870d96d59c875935d33ff32	Experiment Fail
--------------------------------------------------
q22-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(16-30)-changing_phase=3.249923434748062 amp=None-6870b93aac4705028caae75f
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870d96d59c875935d33ff33	Experiment Fail
--------------------------------------------------
q22-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(26-30)-changing_phase=5.416539057913437 amp=None-6870ba02ac4705028caae769
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870d96d59c875935d33ff34	Experiment Fail
--------------------------------------------------
q22-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(21-30)-changing_phase=4.333231246330749 amp=None-6870b99eac4705028caae764
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870d96d59c875935d33ff35	Experiment Fail
--------------------------------------------------
q22-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(27-30)-changing_phase=5.6332006202299745 amp=None-6870ba15ac4705028caae76a
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870d96d59c875935d33ff36	Experiment Fail
--------------------------------------------------
q22-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(23-30)-changing_phase=4.766554370963824 amp=None-6870b9c4ac4705028caae766
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870d96d59c875935d33ff37	Experiment Fail
--------------------------------------------------
q22-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(15-30)-changing_phase=3.0332618724315243 amp=None-6870b925ac4705028caae75e
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870d96d5f0e2b5dc934006c	Experiment Fail
--------------------------------------------------
q22-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(25-30)-changing_phase=5.199877495596899 amp=None-6870b9ebac4705028caae768
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870d96d5f0e2b5dc934006d	Experiment Fail
--------------------------------------------------
q22-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(14-30)-changing_phase=2.8166003101149872 amp=None-6870b911ac4705028caae75d
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870d96d5f0e2b5dc934006e	Experiment Fail
--------------------------------------------------
q22-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(28-30)-changing_phase=5.849862182546511 amp=None-6870ba28ac4705028caae76b
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870d96d5f0e2b5dc934006f	Experiment Fail
--------------------------------------------------
q22-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(17-30)-changing_phase=3.4665849970645994 amp=None-6870b94eac4705028caae760
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870d96d5f0e2b5dc9340070	Experiment Fail
--------------------------------------------------
q22-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(29-30)-changing_phase=6.066523744863049 amp=None-6870ba3dac4705028caae76c
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870d96d5f0e2b5dc9340071	Experiment Fail
--------------------------------------------------
q22-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(22-30)-changing_phase=4.549892808647287 amp=None-6870b9b0ac4705028caae765
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6870d96d5f0e2b5dc9340072	Experiment Fail
--------------------------------------------------
q22-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(18-30)-changing_phase=3.683246559381137 amp=None-6870b962ac4705028caae761
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
687340e7814a76608c8cdfc0	Experiment Fail
--------------------------------------------------
q2-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(8-10)-changing_phase=4.886921905584122 amp=None-687340ae13e43155524e84b5
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
687475731368ae4e654ee0d4	Experiment Fail
--------------------------------------------------
q6-RabiScanWidthBF-RabiScanWidthBFOnce(17-31)-BF=1160-687474ff2f6d8fe40f716d51
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68747573df3cd76f324ee082	Experiment Fail
--------------------------------------------------
q6-RabiScanWidthBF-RabiScanWidthBFOnce(31-31)-BF=1300-687474ff2f6d8fe40f716d5f
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68747573df3cd76f324ee083	Experiment Fail
--------------------------------------------------
q6-RabiScanWidthBF-RabiScanWidthBFOnce(22-31)-BF=1210-687474ff2f6d8fe40f716d56
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
687475dedf3cd76f324ee084	Experiment Fail
--------------------------------------------------
q6-RabiScanWidthBF-RabiScanWidthBFOnce(11-31)-BF=1100-6874758dadc162e82752d897
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
687475dedf3cd76f324ee085	Experiment Fail
--------------------------------------------------
q6-RabiScanWidthBF-RabiScanWidthBFOnce(21-31)-BF=1200-6874758dadc162e82752d8a1
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
687475de1368ae4e654ee0d5	Experiment Fail
--------------------------------------------------
q6-RabiScanWidthBF-RabiScanWidthBFOnce(13-31)-BF=1120-6874758dadc162e82752d899
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
687475df1368ae4e654ee0d6	Experiment Fail
--------------------------------------------------
q6-RabiScanWidthBF-RabiScanWidthBFOnce(15-31)-BF=1140-6874758dadc162e82752d89b
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
687475df1368ae4e654ee0d7	Experiment Fail
--------------------------------------------------
q6-RabiScanWidthBF-RabiScanWidthBFOnce(25-31)-BF=1240-6874758dadc162e82752d8a5
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
687475df1368ae4e654ee0d8	Experiment Fail
--------------------------------------------------
q6-RabiScanWidthBF-RabiScanWidthBFOnce(27-31)-BF=1260-6874758dadc162e82752d8a7
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68747887814a76608c8cdfc3	Experiment Fail
--------------------------------------------------
q5q6c5-6-CouplerRabiScanWidthBF-CouplerRabiScanWidthBFOnce(10-51)-BF=890-6874783efbac0f71356b8a43
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6874a9ca1b17e2c4f88cdfbc	Experiment Fail
--------------------------------------------------
q5q6c5-6-CouplerRabiScanWidthBF-CouplerRabiScanWidthBFOnce(6-31)-BF=1050-6874a98f070fb01b477a8cb4
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6874a9ca814a76608c8cdfc7	Experiment Fail
--------------------------------------------------
q5q6c5-6-CouplerRabiScanWidthBF-CouplerRabiScanWidthBFOnce(7-31)-BF=1060-6874a98f070fb01b477a8cb5
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6874a9ca814a76608c8cdfc8	Experiment Fail
--------------------------------------------------
q5q6c5-6-CouplerRabiScanWidthBF-CouplerRabiScanWidthBFOnce(9-31)-BF=1080-6874a98f070fb01b477a8cb7
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6874a9ca1b17e2c4f88cdfbd	Experiment Fail
--------------------------------------------------
q5q6c5-6-CouplerRabiScanWidthBF-CouplerRabiScanWidthBFOnce(8-31)-BF=1070-6874a98f070fb01b477a8cb6
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6874a9ca1b17e2c4f88cdfbe	Experiment Fail
--------------------------------------------------
q5q6c5-6-CouplerRabiScanWidthBF-CouplerRabiScanWidthBFOnce(10-31)-BF=1090-6874a98f070fb01b477a8cb8
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6874be31814a76608c8cdfca	Experiment Fail
--------------------------------------------------
q5q6c5-6-DEDoubleDriving-DoubleDrivingScanPreAmp(193-unknow)-DE-192-6874be0b5f40a7e5d1947702
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6874be31814a76608c8cdfcb	Experiment Crash
--------------------------------------------------
q5q6c5-6-DEDoubleDriving-6874bc3e5f40a7e5d1947640
--------------------------------------------------
'QDict' object is not callable
--------------------------------------------------
Traceback (most recent call last):
  File "D:\software\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "D:\software\Anaconda\envs\visage\lib\asyncio\base_events.py", line 634, in run_until_complete
    self.run_forever()
  File "D:\software\Anaconda\envs\visage\lib\asyncio\base_events.py", line 601, in run_forever
    self._run_once()
  File "D:\software\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1869, in _run_once
    event_list = self._selector.select(timeout)
  File "D:\software\Anaconda\envs\visage\lib\selectors.py", line 324, in select
    r, w, _ = self._select(self._readers, self._writers, [], timeout)
  File "D:\software\Anaconda\envs\visage\lib\selectors.py", line 315, in _select
    r, w, x = select.select(r, w, w, timeout)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\code\yxy\naga\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 73, in wrapper
    await func(*args, **kwargs)
  File "D:\code\yxy\naga\pyqcat-apps\pyQCat\experiments\composite_experiment.py", line 516, in run_experiment
    await self._sync_composite_run()
  File "D:\code\yxy\naga\pyqcat-apps\pyQCat\experiments\composite\error_quantification\nm_base.py", line 182, in _sync_composite_run
    res = await ea.optimize(
  File "D:\code\yxy\naga\pyqcat-apps\pyQCat\tools\de_optimize\optimize.py", line 153, in optimize
    [optPop, lastPop] = await algorithm.run(prophetPop)
  File "D:\code\yxy\naga\pyqcat-apps\pyQCat\tools\de_optimize\soea_DE_best_1_bin_templet.py", line 1106, in run
    await self.call_aimFunc(experimentPop)  # 计算目标函数值
  File "D:\code\yxy\naga\pyqcat-apps\pyQCat\tools\de_optimize\soea_DE_best_1_bin_templet.py", line 237, in call_aimFunc
    await self.problem.evaluation(pop)  # 调用问题类的evaluation()
  File "D:\code\yxy\naga\pyqcat-apps\pyQCat\tools\de_optimize\problem.py", line 221, in evaluation
    return_object = await self.evalVars(pop.Phen)
  File "D:\code\yxy\naga\pyqcat-apps\pyQCat\experiments\composite\error_quantification\nm_base.py", line 212, in eval_vars
    res = await self.nm_opt_func(Vars[i, :])
  File "D:\code\yxy\naga\pyqcat-apps\pyQCat\experiments\composite\error_quantification\nm_base.py", line 217, in nm_opt_func
    res = await self._execute_exp(parameters)
  File "D:\code\yxy\naga\pyqcat-apps\app\script_yxy\reset\qc_double_driving_new.py", line 1352, in _execute_exp
    return np.mean(analysis_data)
  File "D:\software\Anaconda\envs\visage\lib\site-packages\numpy\core\fromnumeric.py", line 3502, in mean
    return mean(axis=axis, dtype=dtype, out=out, **kwargs)
TypeError: 'QDict' object is not callable

687642f61368ae4e654ee0e4	Experiment Fail
--------------------------------------------------
q6-ReadoutFreqCalibrate-CavityFreqSpectrum(1-2)-xy_pulse amp=0-687642cd758cde32a6820075
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
687711a31b17e2c4f88ce0f3	Experiment Fail
--------------------------------------------------
q6c5-6-QCT2Ramsey-QCRamsey(2-unknow)-count=1-z_amp=-0.005-fringe=3.333-68771171dc9bd64a127f092c
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68771ed51368ae4e654ee0f1	Experiment Fail
--------------------------------------------------
q6-PhotonNumMeasVsAmp-PhotonNumMeas(2-41)-amp=0.005v-68771eab81ac166293c42d20
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68771ed51368ae4e654ee0f2	Experiment Fail
--------------------------------------------------
q6-PhotonNumMeasVsAmp-PhotonNumMeas(8-41)-amp=0.035v-68771eac81ac166293c42d26
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68771ed5df3cd76f324ee09a	Experiment Fail
--------------------------------------------------
q6-PhotonNumMeasVsAmp-PhotonNumMeas(6-41)-amp=0.025v-68771eac81ac166293c42d24
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68771ed5df3cd76f324ee09b	Experiment Fail
--------------------------------------------------
q6-PhotonNumMeasVsAmp-PhotonNumMeas(5-41)-amp=0.02v-68771eac81ac166293c42d23
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68771ed51368ae4e654ee0f3	Experiment Fail
--------------------------------------------------
q6-PhotonNumMeasVsAmp-PhotonNumMeas(7-41)-amp=0.03v-68771eac81ac166293c42d25
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68771ed5df3cd76f324ee09c	Experiment Fail
--------------------------------------------------
q6-PhotonNumMeasVsAmp-PhotonNumMeas(1-41)-amp=0.0v-68771eab81ac166293c42d1f
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68771ed6df3cd76f324ee09d	Experiment Fail
--------------------------------------------------
q6-PhotonNumMeasVsAmp-PhotonNumMeas(10-41)-amp=0.045v-68771ead81ac166293c42d28
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68771ed61368ae4e654ee0f4	Experiment Fail
--------------------------------------------------
q6-PhotonNumMeasVsAmp-PhotonNumMeas(9-41)-amp=0.04v-68771eac81ac166293c42d27
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68771ed61368ae4e654ee0f5	Experiment Fail
--------------------------------------------------
q6-PhotonNumMeasVsAmp-PhotonNumMeas(3-41)-amp=0.01v-68771eab81ac166293c42d21
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68771ed61368ae4e654ee0f6	Experiment Fail
--------------------------------------------------
q6-PhotonNumMeasVsAmp-PhotonNumMeas(4-41)-amp=0.015v-68771eac81ac166293c42d22
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68778b9c1b17e2c4f88ce110	Experiment Fail
--------------------------------------------------
q5q6c5-6-CouplerSpectrumZAmpDynamic-CouplerSpectrum(9-21)-z_amp=-0.001-68778b2c560a22b45cc18363
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68778b9c814a76608c8ce019	Experiment Fail
--------------------------------------------------
q5q6c5-6-CouplerSpectrumZAmpDynamic-CouplerSpectrum(10-21)-z_amp=-0.0005-68778b2c560a22b45cc18364
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68778b9c1b17e2c4f88ce111	Experiment Fail
--------------------------------------------------
q5q6c5-6-CouplerSpectrumZAmpDynamic-CouplerSpectrum(7-21)-z_amp=-0.002-68778b2c560a22b45cc18361
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68778b9c1b17e2c4f88ce112	Experiment Fail
--------------------------------------------------
q5q6c5-6-CouplerSpectrumZAmpDynamic-CouplerSpectrum(8-21)-z_amp=-0.0015-68778b2c560a22b45cc18362
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68778c321368ae4e654ee0fc	Experiment Fail
--------------------------------------------------
q5q6c5-6-CouplerSpectrum-68778c08967a3c4399b8ade2
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68778c321368ae4e654ee0fd	Experiment Fail
--------------------------------------------------
q5q6c5-6-CouplerSpectrum-68778c08967a3c4399b8ade4
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68778c32df3cd76f324ee0aa	Experiment Fail
--------------------------------------------------
q5q6c5-6-CouplerSpectrum-68778c08967a3c4399b8ade3
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68778c82814a76608c8ce01a	Experiment Fail
--------------------------------------------------
q5q6c5-6-CouplerSpectrum-68778c2aeb9f51e1dffd6d16
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68778c821b17e2c4f88ce113	Experiment Fail
--------------------------------------------------
q5q6c5-6-CouplerSpectrum-68778c2aeb9f51e1dffd6d11
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68778c821b17e2c4f88ce114	Experiment Fail
--------------------------------------------------
q5q6c5-6-CouplerSpectrum-68778c2aeb9f51e1dffd6d18
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68778c82814a76608c8ce01b	Experiment Fail
--------------------------------------------------
q5q6c5-6-CouplerSpectrum-68778c2aeb9f51e1dffd6d0f
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68778c82814a76608c8ce01c	Experiment Fail
--------------------------------------------------
q5q6c5-6-CouplerSpectrum-68778c2aeb9f51e1dffd6d0b
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68778c82814a76608c8ce01d	Experiment Fail
--------------------------------------------------
q5q6c5-6-CouplerSpectrum-68778c2aeb9f51e1dffd6d0d
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68778c82814a76608c8ce01e	Experiment Fail
--------------------------------------------------
q5q6c5-6-CouplerSpectrum-68778c2aeb9f51e1dffd6d10
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68778c82814a76608c8ce01f	Experiment Fail
--------------------------------------------------
q5q6c5-6-CouplerSpectrum-68778c2aeb9f51e1dffd6d0e
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68778c83814a76608c8ce020	Experiment Fail
--------------------------------------------------
q5q6c5-6-CouplerSpectrum-68778c2aeb9f51e1dffd6d14
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68778c831b17e2c4f88ce115	Experiment Fail
--------------------------------------------------
q5q6c5-6-CouplerSpectrum-68778c2aeb9f51e1dffd6d15
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68778c831b17e2c4f88ce116	Experiment Fail
--------------------------------------------------
q5q6c5-6-CouplerSpectrum-68778c2aeb9f51e1dffd6d17
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68778c831b17e2c4f88ce117	Experiment Fail
--------------------------------------------------
q5q6c5-6-CouplerSpectrum-68778c2aeb9f51e1dffd6d12
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68778c831b17e2c4f88ce119	Experiment Fail
--------------------------------------------------
q5q6c5-6-CouplerSpectrum-68778c2aeb9f51e1dffd6d13
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68778c831b17e2c4f88ce118	Experiment Fail
--------------------------------------------------
q5q6c5-6-CouplerSpectrum-68778c2aeb9f51e1dffd6d0c
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6877a3cadf3cd76f324ee0c3	Experiment Fail
--------------------------------------------------
q5q6c5-6-CouplerSpectrumZAmpDynamic-CouplerSpectrum(3-21)-z_amp=-0.004-6877a39e599a561bfab57680
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6877a3cadf3cd76f324ee0c4	Experiment Crash
--------------------------------------------------
q5q6c5-6-CouplerSpectrumZAmpDynamic-6877a386599a561bfab5767c
--------------------------------------------------
'dict' object has no attribute 'peaks'
--------------------------------------------------
Traceback (most recent call last):
  File "D:\software\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "D:\software\Anaconda\envs\visage\lib\asyncio\base_events.py", line 634, in run_until_complete
    self.run_forever()
  File "D:\software\Anaconda\envs\visage\lib\asyncio\base_events.py", line 601, in run_forever
    self._run_once()
  File "D:\software\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1869, in _run_once
    event_list = self._selector.select(timeout)
  File "D:\software\Anaconda\envs\visage\lib\selectors.py", line 324, in select
    r, w, _ = self._select(self._readers, self._writers, [], timeout)
  File "D:\software\Anaconda\envs\visage\lib\selectors.py", line 315, in _select
    r, w, x = select.select(r, w, w, timeout)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\code\yxy\naga\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 73, in wrapper
    await func(*args, **kwargs)
  File "D:\code\yxy\naga\pyqcat-apps\pyQCat\experiments\composite_experiment.py", line 516, in run_experiment
    await self._sync_composite_run()
  File "D:\code\yxy\naga\pyqcat-apps\pyQCat\experiments\composite\single_gate\qubit_spectrum_zamp.py", line 283, in _sync_composite_run
    values = result.peaks.value
AttributeError: 'dict' object has no attribute 'peaks'

6878a7ea5f0e2b5dc9341477	Experiment Fail
--------------------------------------------------
q1-CavityFreqSpectrum-687867ae500d3e827b584e35
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6879c3b45f0e2b5dc93414be	Experiment Fail
--------------------------------------------------
q1-QubitSpectrum-6879c4094f2efb27741282bf
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
687dd2c45f0e2b5dc93416a7	Experiment Fail
--------------------------------------------------
q102-QubitSpectrum-687dd1fb78d2ea653251ff48
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6881d111f340c94b3b446c73	Experiment Fail
--------------------------------------------------
c74-80-CouplerSpectrum-6881d18f1a68801703dd2eb6
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6881f3cdf4b30d9991446d08	Experiment Fail
--------------------------------------------------
q1-CavityFreqSpectrum-6881f4137fce99d2947f2cb7
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6882dd21b242e0e00cd3f417	Experiment Fail
--------------------------------------------------
q79q86-FixedSwapFreqCaliCoupler2-SwapOnce(1-unknow)-zamp=0.316,target_swap_freq=25MHz,-6882dd01138eb1c95b2149f9
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6882dd22b242e0e00cd3f418	Experiment Crash
--------------------------------------------------
q79q86-FixedSwapFreqCaliCoupler2-68825211138eb1c95b2149ef
--------------------------------------------------
'dict' object has no attribute 'freq'
--------------------------------------------------
Traceback (most recent call last):
  File "C:\ProgramData\Miniconda3\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\ProgramData\Miniconda3\lib\asyncio\base_events.py", line 634, in run_until_complete
    self.run_forever()
  File "C:\ProgramData\Miniconda3\lib\asyncio\base_events.py", line 601, in run_forever
    self._run_once()
  File "C:\ProgramData\Miniconda3\lib\asyncio\base_events.py", line 1869, in _run_once
    event_list = self._selector.select(timeout)
  File "C:\ProgramData\Miniconda3\lib\selectors.py", line 324, in select
    r, w, _ = self._select(self._readers, self._writers, [], timeout)
  File "C:\ProgramData\Miniconda3\lib\selectors.py", line 315, in _select
    r, w, x = select.select(r, w, w, timeout)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\lzw\code\GitProject\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "E:\lzw\code\GitProject\0.23.2\pyqcat-apps\pyQCat\experiments\composite_experiment.py", line 516, in run_experiment
    await self._sync_composite_run()
  File "E:\lzw\code\GitProject\0.23.2\pyqcat-apps\pyQCat\experiments\composite\calibration\voltage_drift_calibration.py", line 1686, in _sync_composite_run
    is_drift = await self._check_drift()
  File "E:\lzw\code\GitProject\0.23.2\pyqcat-apps\pyQCat\experiments\composite\calibration\voltage_drift_calibration.py", line 1640, in _check_drift
    await self._run_child_experiment_2(zamp, add_child=False)
  File "E:\lzw\code\GitProject\0.23.2\pyqcat-apps\pyQCat\experiments\composite\calibration\voltage_drift_calibration.py", line 1627, in _run_child_experiment_2
    self.run_options.freq_list.append(results.freq.value)
AttributeError: 'dict' object has no attribute 'freq'

6882fd5ff6e8e052d6d3f430	Experiment Fail
--------------------------------------------------
c94-101-SingleShot_01-6882fba43d62a0578e70b360
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6882fdc8f4b30d9991446d36	Experiment Fail
--------------------------------------------------
c70-77-SingleShot_01-6882fe06a4d9717b388ff173
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688332dfb242e0e00cd3f4a7	Experiment Fail
--------------------------------------------------
c70-77-SingleShot_01-6883331dca0f6b72b4bc186d
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68834d6ef340c94b3b446e8e	Experiment Fail
--------------------------------------------------
c82-89-SingleShot_01-68834d06c4033b268a7be3c5
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68835961f4b30d9991446d73	Experiment Fail
--------------------------------------------------
c80-86-SingleShot_01-688358ff06f17e5393b30f2e
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68873a1df340c94b3b446f61	Experiment Fail
--------------------------------------------------
q63q69-Swap-SwapOnce(3-20)-scan q69 4273.363MHz-68873a7e450cffbab09c25b8
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68873a1df4b30d9991446e40	Experiment Fail
--------------------------------------------------
q63q69-Swap-SwapOnce(10-20)-scan q69 4310.205MHz-68873a80450cffbab09c25bf
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68873a1df4b30d9991446e41	Experiment Fail
--------------------------------------------------
q63q69-Swap-SwapOnce(2-20)-scan q69 4268.1MHz-68873a7d450cffbab09c25b7
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68873a1df4b30d9991446e42	Experiment Fail
--------------------------------------------------
q63q69-Swap-SwapOnce(4-20)-scan q69 4278.626MHz-68873a7e450cffbab09c25b9
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68873a1df4b30d9991446e43	Experiment Fail
--------------------------------------------------
q63q69-Swap-SwapOnce(18-20)-scan q69 4352.311MHz-68873a80450cffbab09c25c7
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68873a1df340c94b3b446f62	Experiment Fail
--------------------------------------------------
q63q69-Swap-SwapOnce(5-20)-scan q69 4283.89MHz-68873a7e450cffbab09c25ba
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68873a1df4b30d9991446e44	Experiment Fail
--------------------------------------------------
q63q69-Swap-SwapOnce(8-20)-scan q69 4299.679MHz-68873a7f450cffbab09c25bd
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68873a1df340c94b3b446f63	Experiment Fail
--------------------------------------------------
q63q69-Swap-SwapOnce(6-20)-scan q69 4289.153MHz-68873a7f450cffbab09c25bb
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68873a1df340c94b3b446f64	Experiment Fail
--------------------------------------------------
q63q69-Swap-SwapOnce(7-20)-scan q69 4294.416MHz-68873a7f450cffbab09c25bc
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68873a1df340c94b3b446f65	Experiment Fail
--------------------------------------------------
q63q69-Swap-SwapOnce(9-20)-scan q69 4304.942MHz-68873a7f450cffbab09c25be
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6888249bb242e0e00cd3f8d7	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(1-31)-power=-40dBm-6888252184be8d21641d8b90
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6888249bf6e8e052d6d3fb16	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(5-31)-power=-36dBm-6888252184be8d21641d8b94
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6888249bb242e0e00cd3f8d8	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(3-31)-power=-38dBm-6888252184be8d21641d8b92
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6888249bf6e8e052d6d3fb17	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(4-31)-power=-37dBm-6888252184be8d21641d8b93
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6888249bf6e8e052d6d3fb18	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(7-31)-power=-34dBm-6888252184be8d21641d8b96
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6888249bf6e8e052d6d3fb19	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(9-31)-power=-32dBm-6888252184be8d21641d8b98
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6888249bf6e8e052d6d3fb1a	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(2-31)-power=-39dBm-6888252184be8d21641d8b91
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6888249bb242e0e00cd3f8d9	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(6-31)-power=-35dBm-6888252184be8d21641d8b95
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6888249bb242e0e00cd3f8da	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(8-31)-power=-33dBm-6888252184be8d21641d8b97
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6888249bb242e0e00cd3f8db	Experiment Fail
--------------------------------------------------
q1-CavityPowerScan-CavityFreqSpectrum(10-31)-power=-31dBm-6888252184be8d21641d8b99
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68886fc4f4b30d9991446eef	Experiment Fail
--------------------------------------------------
c13-19-CouplerT1Spectrum-CouplerT1(9-unknow)-z_amp=0.3 freq=5721.026157894737-68886f650dba76aebded2e72
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68886fc4f340c94b3b447018	Experiment Fail
--------------------------------------------------
c13-19-CouplerT1Spectrum-CouplerT1(14-unknow)-z_amp=0.3 freq=5721.0256315789475-68886f650dba76aebded2e77
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68886fc4f340c94b3b447019	Experiment Fail
--------------------------------------------------
c13-19-CouplerT1Spectrum-CouplerT1(3-unknow)-z_amp=0.3 freq=5721.026789473684-68886f630dba76aebded2e6c
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68886fc4f340c94b3b44701a	Experiment Fail
--------------------------------------------------
c13-19-CouplerT1Spectrum-CouplerT1(5-unknow)-z_amp=0.3 freq=5721.026578947369-68886f630dba76aebded2e6e
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68886fc4f340c94b3b44701b	Experiment Fail
--------------------------------------------------
c13-19-CouplerT1Spectrum-CouplerT1(8-unknow)-z_amp=0.3 freq=5721.026263157894-68886f640dba76aebded2e71
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68886fc4f340c94b3b44701c	Experiment Fail
--------------------------------------------------
c13-19-CouplerT1Spectrum-CouplerT1(18-unknow)-z_amp=0.3 freq=5721.025210526315-68886f660dba76aebded2e7b
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68886fc4f4b30d9991446ef0	Experiment Fail
--------------------------------------------------
c13-19-CouplerT1Spectrum-CouplerT1(6-unknow)-z_amp=0.3 freq=5721.02647368421-68886f640dba76aebded2e6f
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68886fc4f4b30d9991446ef1	Experiment Fail
--------------------------------------------------
c13-19-CouplerT1Spectrum-CouplerT1(4-unknow)-z_amp=0.3 freq=5721.026684210527-68886f630dba76aebded2e6d
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68886fc4f4b30d9991446ef2	Experiment Fail
--------------------------------------------------
c13-19-CouplerT1Spectrum-CouplerT1(7-unknow)-z_amp=0.3 freq=5721.026368421052-68886f640dba76aebded2e70
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68886fc5f4b30d9991446ef3	Experiment Fail
--------------------------------------------------
c13-19-CouplerT1Spectrum-CouplerT1(10-unknow)-z_amp=0.3 freq=5721.026052631579-68886f650dba76aebded2e73
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68886fc5f340c94b3b44701d	Experiment Crash
--------------------------------------------------
c13-19-CouplerT1Spectrum-68886f620dba76aebded2e68
--------------------------------------------------
setting an array element with a sequence. The requested array has an inhomogeneous shape after 1 dimensions. The detected shape was (20,) + inhomogeneous part.
--------------------------------------------------
Traceback (most recent call last):
  File "C:\ProgramData\miniconda3\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\ProgramData\miniconda3\envs\visage\lib\asyncio\base_events.py", line 634, in run_until_complete
    self.run_forever()
  File "C:\ProgramData\miniconda3\envs\visage\lib\asyncio\base_events.py", line 601, in run_forever
    self._run_once()
  File "C:\ProgramData\miniconda3\envs\visage\lib\asyncio\base_events.py", line 1878, in _run_once
    handle = heapq.heappop(self._scheduled)
  File "C:\ProgramData\miniconda3\envs\visage\lib\asyncio\events.py", line 122, in __lt__
    return self._when < other._when
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\experiments\composite_experiment.py", line 518, in run_experiment
    await self._async_composite_run_base()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\experiments\composite_experiment.py", line 469, in _async_composite_run_base
    await self._async_run_analysis()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\experiments\composite_experiment.py", line 503, in _async_run_analysis
    self._analysis = run_analysis_process(*args)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 254, in run_analysis_process
    analysis_obj.run_analysis()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\analysis\curve_analysis.py", line 406, in run_analysis
    self._initialize()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\analysis\curve_analysis.py", line 180, in _initialize
    self._analysis_data_dict = self._create_analysis_data()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\analysis\curve_analysis.py", line 127, in _create_analysis_data
    y=np.copy(self.experiment_data.y_data.get(key, [])),
  File "C:\ProgramData\miniconda3\envs\visage\lib\site-packages\numpy\lib\function_base.py", line 962, in copy
    return array(a, order=order, subok=subok, copy=True)
ValueError: setting an array element with a sequence. The requested array has an inhomogeneous shape after 1 dimensions. The detected shape was (20,) + inhomogeneous part.

68887329b242e0e00cd3f955	Experiment Fail
--------------------------------------------------
c13-19-CouplerACSpectrum-CouplerRamsey(8-39)-z_amp=0.062-688873555acc10d4a90b5aca
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688989ebf4b30d9991447092	Experiment Fail
--------------------------------------------------
c28-34-CouplerSpectrum-68898a74013a5ca82d546e9b
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68898edab242e0e00cd3fa08	Experiment Fail
--------------------------------------------------
c28-34-SingleShot_01-68898f6104a311281e479396
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68898f9df4b30d9991447093	Experiment Fail
--------------------------------------------------
c28-34-SingleShot_01-688990267d57fcfca85a4cda
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688990f2f4b30d9991447094	Experiment Fail
--------------------------------------------------
c28-34-SingleShot_01-6889917b7f53841142d72763
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688993fdf4b30d9991447096	Experiment Fail
--------------------------------------------------
q22q29-SQPhaseTMSE-CPhaseTMSE(1-40)-q29-cz_num=1-6889948828a56c575983ac5a
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688993fdf340c94b3b4470b2	Experiment Fail
--------------------------------------------------
q22q29-SQPhaseTMSE-CPhaseTMSE(3-40)-q29-cz_num=3-6889948828a56c575983ac5c
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688993fdf4b30d9991447097	Experiment Fail
--------------------------------------------------
q22q29-SQPhaseTMSE-CPhaseTMSE(9-40)-q29-cz_num=9-6889948828a56c575983ac62
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688993fdf340c94b3b4470b3	Experiment Fail
--------------------------------------------------
q22q29-SQPhaseTMSE-CPhaseTMSE(2-40)-q29-cz_num=2-6889948828a56c575983ac5b
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688993fdf4b30d9991447098	Experiment Fail
--------------------------------------------------
q22q29-SQPhaseTMSE-CPhaseTMSE(4-40)-q29-cz_num=4-6889948828a56c575983ac5d
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688993fdf340c94b3b4470b4	Experiment Fail
--------------------------------------------------
q22q29-SQPhaseTMSE-CPhaseTMSE(5-40)-q29-cz_num=5-6889948828a56c575983ac5e
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688993fdf340c94b3b4470b5	Experiment Fail
--------------------------------------------------
q22q29-SQPhaseTMSE-CPhaseTMSE(10-40)-q29-cz_num=10-6889948828a56c575983ac63
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688993fdf340c94b3b4470b6	Experiment Fail
--------------------------------------------------
q22q29-SQPhaseTMSE-CPhaseTMSE(6-40)-q29-cz_num=6-6889948828a56c575983ac5f
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688993fdf340c94b3b4470b7	Experiment Fail
--------------------------------------------------
q22q29-SQPhaseTMSE-CPhaseTMSE(8-40)-q29-cz_num=8-6889948828a56c575983ac61
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688993fdf4b30d9991447099	Experiment Fail
--------------------------------------------------
q22q29-SQPhaseTMSE-CPhaseTMSE(7-40)-q29-cz_num=7-6889948828a56c575983ac60
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6889945eb242e0e00cd3fa9c	Experiment Fail
--------------------------------------------------
c28-34-SingleShot_01-688994eabd80ae1060976cf3
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688994c1f340c94b3b4470b8	Experiment Fail
--------------------------------------------------
c28-34-SingleShot_01-6889954aa2a6fb78545963f0
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688994cdf6e8e052d6d3fde2	Experiment Fail
--------------------------------------------------
c28-34-SingleShot_01-6889955ab2dfe7640f614490
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6889b50ff4b30d99914470b9	Experiment Fail
--------------------------------------------------
q22q29-SQPhaseTMSE-CPhaseTMSE(23-40)-q22-cz_num=3-6889b585a8a1b4e48c3904ac
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6889b50ff4b30d99914470ba	Experiment Fail
--------------------------------------------------
q22q29-SQPhaseTMSE-CPhaseTMSE(9-40)-q29-cz_num=9-6889b585a8a1b4e48c39049e
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6889b50ff340c94b3b4470db	Experiment Fail
--------------------------------------------------
q22q29-SQPhaseTMSE-CPhaseTMSE(35-40)-q22-cz_num=15-6889b585a8a1b4e48c3904b8
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6889b50ff340c94b3b4470dc	Experiment Fail
--------------------------------------------------
q22q29-SQPhaseTMSE-CPhaseTMSE(12-40)-q29-cz_num=12-6889b585a8a1b4e48c3904a1
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6889b50ff4b30d99914470bb	Experiment Fail
--------------------------------------------------
q22q29-SQPhaseTMSE-CPhaseTMSE(10-40)-q29-cz_num=10-6889b585a8a1b4e48c39049f
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6889b50ff4b30d99914470bc	Experiment Fail
--------------------------------------------------
q22q29-SQPhaseTMSE-CPhaseTMSE(26-40)-q22-cz_num=6-6889b585a8a1b4e48c3904af
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6889b50ff340c94b3b4470dd	Experiment Fail
--------------------------------------------------
q22q29-SQPhaseTMSE-CPhaseTMSE(20-40)-q29-cz_num=20-6889b585a8a1b4e48c3904a9
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6889b50ff4b30d99914470bd	Experiment Fail
--------------------------------------------------
q22q29-SQPhaseTMSE-CPhaseTMSE(29-40)-q22-cz_num=9-6889b585a8a1b4e48c3904b2
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6889b50ff340c94b3b4470de	Experiment Fail
--------------------------------------------------
q22q29-SQPhaseTMSE-CPhaseTMSE(34-40)-q22-cz_num=14-6889b585a8a1b4e48c3904b7
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6889bba9b242e0e00cd3fab6	Experiment Fail
--------------------------------------------------
c28-34-SingleShot_01-6889bc170d3f8665e28ecc25
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688ade37b242e0e00cd3fb88	Experiment Fail
--------------------------------------------------
q56-RBSingle-688add80948fe3eab18fb7fb
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688b1114f6e8e052d6d3ff07	Experiment Fail
--------------------------------------------------
c50-56-SingleShot_01-688b1196af7defe319f337bf
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688c66d1f340c94b3b4473ec	Experiment Fail
--------------------------------------------------
q24-F12XpiDetection-RabiScanAmpF12(1-unknow)-count=0-drive_power=-20.6-688c675bb4884c052ae3711f
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688c6eadf4b30d999144764c	Experiment Fail
--------------------------------------------------
q39q45-Swap-SwapOnce(5-16)-scan q45 4223.252MHz-688c6f0daca4089ee28d738a
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688c6eadf340c94b3b4473ef	Experiment Fail
--------------------------------------------------
q39q45-Swap-SwapOnce(3-16)-scan q45 4219.252MHz-688c6f09aca4089ee28d7388
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688c6eadf4b30d999144764d	Experiment Fail
--------------------------------------------------
q39q45-Swap-SwapOnce(10-16)-scan q45 4233.252MHz-688c6f33aca4089ee28d738f
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688c6eadf340c94b3b4473f0	Experiment Fail
--------------------------------------------------
q39q45-Swap-SwapOnce(2-16)-scan q45 4217.252MHz-688c6f07aca4089ee28d7387
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688c6eadf340c94b3b4473f1	Experiment Fail
--------------------------------------------------
q39q45-Swap-SwapOnce(6-16)-scan q45 4225.252MHz-688c6f2daca4089ee28d738b
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688c6eadf340c94b3b4473f3	Experiment Fail
--------------------------------------------------
q39q45-Swap-SwapOnce(4-16)-scan q45 4221.252MHz-688c6f0caca4089ee28d7389
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688c6eadf340c94b3b4473f2	Experiment Fail
--------------------------------------------------
q39q45-Swap-SwapOnce(11-16)-scan q45 4235.252MHz-688c6f34aca4089ee28d7390
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688c6eadf4b30d999144764e	Experiment Fail
--------------------------------------------------
q39q45-Swap-SwapOnce(9-16)-scan q45 4231.252MHz-688c6f32aca4089ee28d738e
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688c6eadf4b30d999144764f	Experiment Fail
--------------------------------------------------
q39q45-Swap-SwapOnce(8-16)-scan q45 4229.252MHz-688c6f30aca4089ee28d738d
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688c6eadf4b30d9991447650	Experiment Fail
--------------------------------------------------
q39q45-Swap-SwapOnce(7-16)-scan q45 4227.252MHz-688c6f2faca4089ee28d738c
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688c7141f340c94b3b44750e	Experiment Fail
--------------------------------------------------
q39q45-FixedSwapFreqCaliCoupler2-SwapOnce(1-unknow)-zamp=-0.141,target_swap_freq=25MHz,-688c71ca4deffe15f4201bc7
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688c989cf4b30d9991447690	Experiment Fail
--------------------------------------------------
q33q39-SQPhaseTMSE-CPhaseTMSE(16-40)-q33-cz_num=16-688c98cfadeab209e7859eb3
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688c989cf340c94b3b447550	Experiment Fail
--------------------------------------------------
q33q39-SQPhaseTMSE-CPhaseTMSE(10-40)-q33-cz_num=10-688c98c4adeab209e7859ead
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688c989cf340c94b3b447551	Experiment Fail
--------------------------------------------------
q33q39-SQPhaseTMSE-CPhaseTMSE(12-40)-q33-cz_num=12-688c98caadeab209e7859eaf
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688c989cf340c94b3b447552	Experiment Fail
--------------------------------------------------
q33q39-SQPhaseTMSE-CPhaseTMSE(14-40)-q33-cz_num=14-688c98cfadeab209e7859eb1
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688c989cf4b30d9991447691	Experiment Fail
--------------------------------------------------
q33q39-SQPhaseTMSE-CPhaseTMSE(18-40)-q33-cz_num=18-688c98d1adeab209e7859eb5
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688c989cf4b30d9991447692	Experiment Fail
--------------------------------------------------
q33q39-SQPhaseTMSE-CPhaseTMSE(17-40)-q33-cz_num=17-688c98d0adeab209e7859eb4
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688c989cf4b30d9991447693	Experiment Fail
--------------------------------------------------
q33q39-SQPhaseTMSE-CPhaseTMSE(11-40)-q33-cz_num=11-688c98c6adeab209e7859eae
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688c989cf4b30d9991447694	Experiment Fail
--------------------------------------------------
q33q39-SQPhaseTMSE-CPhaseTMSE(19-40)-q33-cz_num=19-688c98d1adeab209e7859eb6
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688c989cf4b30d9991447695	Experiment Fail
--------------------------------------------------
q33q39-SQPhaseTMSE-CPhaseTMSE(13-40)-q33-cz_num=13-688c98cfadeab209e7859eb0
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
688c989cf4b30d9991447696	Experiment Fail
--------------------------------------------------
q33q39-SQPhaseTMSE-CPhaseTMSE(15-40)-q33-cz_num=15-688c98cfadeab209e7859eb2
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6890629ab242e0e00cd40a16	Experiment Fail
--------------------------------------------------
q1-CoherentPumpExperiment-689062ea9be9b52923bd52a6
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68906306b242e0e00cd40a1c	Experiment Fail
--------------------------------------------------
q1-CoherentPumpExperiment-6890637edcf4af4dc45a7835
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6890633ef4b30d9991447aea	Experiment Fail
--------------------------------------------------
q1-CoherentPumpExperiment-689063c635d9f565e184c753
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68906504f4b30d9991447aeb	Experiment Fail
--------------------------------------------------
q15-SingleShot_01-6890657ad9bdf9c7736e4303
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
689197d8f340c94b3b44820a	Experiment Fail
--------------------------------------------------
q55-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(6-61)-changing_phase=0.26666666666666666 amp=None-6891982d1ac5ea2f5d0549c5
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
689197d8f4b30d9991448211	Experiment Fail
--------------------------------------------------
q55-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(5-61)-changing_phase=0.25333333333333335 amp=None-689198221ac5ea2f5d0549c4
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68919b5c11ad73c4231f6e5d	Experiment Crash
--------------------------------------------------
c61-67-ACBiasTunable-CavityFreqSpectrum(19-33)-ac_bias = 0.06v-68919b53b9baf053e0469103
--------------------------------------------------
'utf-8' codec can't decode byte 0x8d in position 2: invalid start byte
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\project\lzw\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 724, in async_execute_loop
    await self._async_get_measure_data(self.sample_channels, loop)
  File "D:\code\project\lzw\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 782, in _async_get_measure_data
    status, measure_data = DataClient().query_acq_data_one(
  File "D:\code\project\lzw\pyqcat-apps\pyQCat\concurrent\data_client.py", line 187, in query_acq_data_one
    acq_data_buf = self._sock.recv_multipart()
  File "C:\Users\<USER>\anaconda3\envs\visage\lib\site-packages\zmq\sugar\socket.py", line 806, in recv_multipart
    parts = [self.recv(flags, copy=copy, track=track)]
  File "_zmq.py", line 1137, in zmq.backend.cython._zmq.Socket.recv
  File "_zmq.py", line 1172, in zmq.backend.cython._zmq.Socket.recv
  File "_zmq.py", line 1259, in zmq.backend.cython._zmq._recv_copy
  File "_zmq.py", line 141, in zmq.backend.cython._zmq._check_rc
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\code\project\lzw\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\project\lzw\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 632, in run_experiment
    self.analysis = await self._async_analysis(require_id)
  File "D:\code\project\lzw\pyqcat-apps\pyQCat\experiments\top_experiment_v1.py", line 523, in _async_analysis
    result = await top_experiment_analysis(**kwargs)
  File "D:\code\project\lzw\pyqcat-apps\pyQCat\concurrent\worker\analysis_interface.py", line 168, in top_experiment_analysis
    await data_acquisition.async_execute_loop()
  File "D:\code\project\lzw\pyqcat-apps\pyQCat\acquisition\acquisition.py", line 753, in async_execute_loop
    DataClient().clear_acq_data(self._id, self.sample_channels)
  File "D:\code\project\lzw\pyqcat-apps\pyQCat\concurrent\data_client.py", line 272, in clear_acq_data
    return TransferTaskStatusEnum.from_description(decode_msg(message[0]))
  File "D:\code\project\lzw\pyqcat-apps\pyQCat\data_transfer\util.py", line 37, in decode_msg
    msg_str = msg.decode(mode)
UnicodeDecodeError: 'utf-8' codec can't decode byte 0x8d in position 2: invalid start byte

68919b11f6e8e052d6d41166	Experiment Fail
--------------------------------------------------
q55-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(15-61)-changing_phase=0.3866666666666667 amp=None-689198e3059f68207ca4ee6a
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68919b11b242e0e00cd4102b	Experiment Fail
--------------------------------------------------
q55-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(47-61)-changing_phase=0.8133333333333335 amp=None-689198e6059f68207ca4ee8a
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6892c145f340c94b3b448890	Experiment Fail
--------------------------------------------------
q1-ACBiasTunable-CavityFreqSpectrum(16-54)-ac_bias = -0.175v-68929b314b9402e9faf2dce6
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6892c145f4b30d9991448897	Experiment Fail
--------------------------------------------------
q1-ACBiasTunable-CavityFreqSpectrum(19-54)-ac_bias = -0.13v-68929b314b9402e9faf2dce9
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6892c145f4b30d9991448898	Experiment Fail
--------------------------------------------------
q1-ACBiasTunable-CavityFreqSpectrum(22-54)-ac_bias = -0.085v-68929b314b9402e9faf2dcec
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6892c145f4b30d9991448899	Experiment Fail
--------------------------------------------------
q1-ACBiasTunable-CavityFreqSpectrum(28-54)-ac_bias = 0.005v-68929b314b9402e9faf2dcf2
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6892c145f340c94b3b448891	Experiment Fail
--------------------------------------------------
q1-ACBiasTunable-CavityFreqSpectrum(25-54)-ac_bias = -0.04v-68929b314b9402e9faf2dcef
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6892c145f340c94b3b448892	Experiment Fail
--------------------------------------------------
q1-ACBiasTunable-CavityFreqSpectrum(42-54)-ac_bias = 0.215v-68929b314b9402e9faf2dd00
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6892c145f340c94b3b448893	Experiment Fail
--------------------------------------------------
q1-ACBiasTunable-CavityFreqSpectrum(46-54)-ac_bias = 0.275v-68929b314b9402e9faf2dd04
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6892c145f340c94b3b448894	Experiment Fail
--------------------------------------------------
q1-ACBiasTunable-CavityFreqSpectrum(49-54)-ac_bias = 0.32v-68929b314b9402e9faf2dd07
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6892c145f4b30d999144889a	Experiment Fail
--------------------------------------------------
q1-ACBiasTunable-CavityFreqSpectrum(40-54)-ac_bias = 0.185v-68929b314b9402e9faf2dcfe
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6892c145f4b30d999144889b	Experiment Fail
--------------------------------------------------
q1-ACBiasTunable-CavityFreqSpectrum(43-54)-ac_bias = 0.23v-68929b314b9402e9faf2dd01
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6892c8286946627d159cc98d	Experiment Fail
--------------------------------------------------
q7-DetuneCalibration-APEComposite(1-2)-RoughScan-APE(3-3)-N=8-6892c822b59393c78b98c28e
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6892e474890a246c6cfb0eb1	Experiment Fail
--------------------------------------------------
q7-SingleShot_01-6892e464c26e81862bd1b792
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
6892e5246946627d159cc9a0	Experiment Fail
--------------------------------------------------
q7-SingleShot_01-6892e4a5510cfc99cf1232b1
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68935c5fb087fecfc9fe9cb7	Experiment Fail
--------------------------------------------------
q3-ACSpectrum-Ramsey(1-40)-z_amp=0.0-68935c55b2d7425f919d82ca
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68935c60b087fecfc9fe9cb8	Experiment Crash
--------------------------------------------------
q3-ACSpectrum-68935c55b2d7425f919d82c8
--------------------------------------------------
'dict' object has no attribute 'freq'
--------------------------------------------------
Traceback (most recent call last):
  File "D:\software\Anaconda\envs\visage\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "D:\software\Anaconda\envs\visage\lib\asyncio\base_events.py", line 634, in run_until_complete
    self.run_forever()
  File "D:\software\Anaconda\envs\visage\lib\asyncio\base_events.py", line 601, in run_forever
    self._run_once()
  File "D:\software\Anaconda\envs\visage\lib\asyncio\base_events.py", line 1869, in _run_once
    event_list = self._selector.select(timeout)
  File "D:\software\Anaconda\envs\visage\lib\selectors.py", line 324, in select
    r, w, _ = self._select(self._readers, self._writers, [], timeout)
  File "D:\software\Anaconda\envs\visage\lib\selectors.py", line 315, in _select
    r, w, x = select.select(r, w, w, timeout)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\code\yxy\naga\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 73, in wrapper
    await func(*args, **kwargs)
  File "D:\code\yxy\naga\pyqcat-apps\pyQCat\experiments\composite_experiment.py", line 516, in run_experiment
    await self._sync_composite_run()
  File "D:\code\yxy\naga\pyqcat-apps\pyQCat\experiments\composite\single_gate\ac_spectrum.py", line 246, in _sync_composite_run
    osc_freq = result.freq.value
AttributeError: 'dict' object has no attribute 'freq'

6895421ef340c94b3b448923	Experiment Fail
--------------------------------------------------
q54-QubitSpectrum-689540a9ef8cae4415fb8fd9
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
689575d7b242e0e00cd416bb	Experiment Fail
--------------------------------------------------
q55-FloquetCalibrationSingleLeakageVerify2-FloquetCalibrationSingleLeakageVerifyOnce(9-21)-changing_phase=0.76 amp=None-689571e4943441271278f037
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
689575d7f6e8e052d6d41905	Experiment Fail
--------------------------------------------------
q55-FloquetCalibrationSingleLeakageVerify2-FloquetCalibrationSingleLeakageVerifyOnce(6-21)-changing_phase=0.625 amp=None-689571e3943441271278f034
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
689575d7b242e0e00cd416bc	Experiment Fail
--------------------------------------------------
q55-FloquetCalibrationSingleLeakageVerify2-FloquetCalibrationSingleLeakageVerifyOnce(12-21)-changing_phase=0.895 amp=None-689571e4943441271278f03a
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
689575ddf340c94b3b448937	Experiment Fail
--------------------------------------------------
q55-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(11-31)-changing_phase=0.6666666666666665 amp=None-689573adadd475bf4fee659e
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
689575ddf340c94b3b448938	Experiment Fail
--------------------------------------------------
q55-FloquetCalibrationSingleLeakageVerify-FloquetCalibrationSingleLeakageVerifyOnce(14-31)-changing_phase=0.7766666666666666 amp=None-689573adadd475bf4fee65a1
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68968fe1f4b30d9991448bbf	Experiment Fail
--------------------------------------------------
q61-PopulationLossSpectrum-PopulationLossOnce(2-3)-delay=1000-6896906db542f160a2177ebb
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68968fe1f340c94b3b448aab	Experiment Fail
--------------------------------------------------
q61-PopulationLossSpectrum-PopulationLossOnce(1-3)-delay=100-6896906db542f160a2177eba
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68968fe1f340c94b3b448aac	Experiment Fail
--------------------------------------------------
q61-PopulationLossSpectrum-PopulationLossOnce(3-3)-delay=10000-6896906db542f160a2177ebc
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
68994e1fb242e0e00cd42150	Experiment Fail
--------------------------------------------------
q94-CavityFreqSpectrum-68994ec2119e4fe4f9398c94
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt
689a9be8f340c94b3b449851	Experiment Fail
--------------------------------------------------
q6-QubitSpectrum-689a9bf129322960a189afa3
--------------------------------------------------
<Analysis Error> | User KeyboardInterrupt