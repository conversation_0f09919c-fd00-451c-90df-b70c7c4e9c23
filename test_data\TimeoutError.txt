_id	message
68635583a0b5e01b01b32a24	Experiment Crash
--------------------------------------------------
BUS1-Channel1-ImpaSetParams-686355cca120104c8c63f63e
--------------------------------------------------
connect fail, ip:*************. port:6001
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 294, in _initial
    self.select_mic_source(
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 121, in select_mic_source
    self.mic_source = DomesticMic(mic_source, ip, device_id)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_inst.py", line 315, in __init__
    self._mic_obj = mic_obj(ip=ip, device_id=device_id)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_inst.py", line 286, in __init__
    super(Stfsh9004, self).__init__(ip, port, device_id)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\STFSH9004.py", line 31, in __init__
    self._connection = TcpClient(ip, port, once=once)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\socket_service\tcp_client.py", line 17, in __init__
    self.connect()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\socket_service\tcp_client.py", line 29, in connect
    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')
TimeoutError: connect fail, ip:*************. port:6001

68945b343ca81fdd3d90ae90	Experiment Crash
--------------------------------------------------
bus-3-ImpaOptiParams-68945b89445196c99a9844df
--------------------------------------------------
connect fail, ip:************. port:5001
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 294, in _initial
    self.select_mic_source(
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 121, in select_mic_source
    self.mic_source = DomesticMic(mic_source, ip, device_id)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 315, in __init__
    self._mic_obj = mic_obj(ip=ip, device_id=device_id)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\BYFS.py", line 474, in __init__
    self._connection = TcpClient(ip, port, once=True)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 17, in __init__
    self.connect()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 29, in connect
    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')
TimeoutError: connect fail, ip:************. port:5001

68945b383366dee48090afc1	Experiment Crash
--------------------------------------------------
bus-3-ImpaOptiParams-68945b8d445196c99a9844e2
--------------------------------------------------
connect fail, ip:************. port:5001
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 294, in _initial
    self.select_mic_source(
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 121, in select_mic_source
    self.mic_source = DomesticMic(mic_source, ip, device_id)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 315, in __init__
    self._mic_obj = mic_obj(ip=ip, device_id=device_id)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\BYFS.py", line 474, in __init__
    self._connection = TcpClient(ip, port, once=True)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 17, in __init__
    self.connect()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 29, in connect
    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')
TimeoutError: connect fail, ip:************. port:5001

689462775dd635150013b5d0	Experiment Crash
--------------------------------------------------
bus-3-ImpaOptiParams-689462cc3d04340aef6a5c7e
--------------------------------------------------
connect fail, ip:************. port:5001
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 294, in _initial
    self.select_mic_source(
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 121, in select_mic_source
    self.mic_source = DomesticMic(mic_source, ip, device_id)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 315, in __init__
    self._mic_obj = mic_obj(ip=ip, device_id=device_id)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\BYFS.py", line 474, in __init__
    self._connection = TcpClient(ip, port, once=True)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 17, in __init__
    self.connect()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 29, in connect
    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')
TimeoutError: connect fail, ip:************. port:5001

6894628126465fe58213b5da	Experiment Crash
--------------------------------------------------
bus-4-ImpaCavityFluxScan-689462d63d04340aef6a5c81
--------------------------------------------------
connect fail, ip:************. port:5001
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 294, in _initial
    self.select_mic_source(
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 121, in select_mic_source
    self.mic_source = DomesticMic(mic_source, ip, device_id)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 315, in __init__
    self._mic_obj = mic_obj(ip=ip, device_id=device_id)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\BYFS.py", line 474, in __init__
    self._connection = TcpClient(ip, port, once=True)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 17, in __init__
    self.connect()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 29, in connect
    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')
TimeoutError: connect fail, ip:************. port:5001

6894693c26465fe58213b5dc	Experiment Crash
--------------------------------------------------
bus-11-ImpaOptiParams-689469913d04340aef6a5cc4
--------------------------------------------------
connect fail, ip:************. port:5001
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 294, in _initial
    self.select_mic_source(
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 121, in select_mic_source
    self.mic_source = DomesticMic(mic_source, ip, device_id)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 315, in __init__
    self._mic_obj = mic_obj(ip=ip, device_id=device_id)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\BYFS.py", line 474, in __init__
    self._connection = TcpClient(ip, port, once=True)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 17, in __init__
    self.connect()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 29, in connect
    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')
TimeoutError: connect fail, ip:************. port:5001

689469405dd635150013b5d2	Experiment Crash
--------------------------------------------------
bus-11-ImpaOptiParams-689469953d04340aef6a5cc7
--------------------------------------------------
connect fail, ip:************. port:5001
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 294, in _initial
    self.select_mic_source(
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 121, in select_mic_source
    self.mic_source = DomesticMic(mic_source, ip, device_id)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 315, in __init__
    self._mic_obj = mic_obj(ip=ip, device_id=device_id)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\BYFS.py", line 474, in __init__
    self._connection = TcpClient(ip, port, once=True)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 17, in __init__
    self.connect()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 29, in connect
    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')
TimeoutError: connect fail, ip:************. port:5001

6894694526465fe58213b5dd	Experiment Crash
--------------------------------------------------
bus-11-ImpaOptiParams-6894699a3d04340aef6a5cca
--------------------------------------------------
connect fail, ip:************. port:5001
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 294, in _initial
    self.select_mic_source(
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 121, in select_mic_source
    self.mic_source = DomesticMic(mic_source, ip, device_id)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 315, in __init__
    self._mic_obj = mic_obj(ip=ip, device_id=device_id)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\BYFS.py", line 474, in __init__
    self._connection = TcpClient(ip, port, once=True)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 17, in __init__
    self.connect()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 29, in connect
    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')
TimeoutError: connect fail, ip:************. port:5001

689469495dd635150013b5d3	Experiment Crash
--------------------------------------------------
bus-11-ImpaOptiParams-6894699e3d04340aef6a5ccd
--------------------------------------------------
connect fail, ip:************. port:5001
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 294, in _initial
    self.select_mic_source(
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 121, in select_mic_source
    self.mic_source = DomesticMic(mic_source, ip, device_id)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 315, in __init__
    self._mic_obj = mic_obj(ip=ip, device_id=device_id)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\BYFS.py", line 474, in __init__
    self._connection = TcpClient(ip, port, once=True)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 17, in __init__
    self.connect()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 29, in connect
    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')
TimeoutError: connect fail, ip:************. port:5001

6894695326465fe58213b5de	Experiment Crash
--------------------------------------------------
bus-12-ImpaCavityFluxScan-689469a83d04340aef6a5cd0
--------------------------------------------------
connect fail, ip:************. port:5001
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 294, in _initial
    self.select_mic_source(
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 121, in select_mic_source
    self.mic_source = DomesticMic(mic_source, ip, device_id)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 315, in __init__
    self._mic_obj = mic_obj(ip=ip, device_id=device_id)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\BYFS.py", line 474, in __init__
    self._connection = TcpClient(ip, port, once=True)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 17, in __init__
    self.connect()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 29, in connect
    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')
TimeoutError: connect fail, ip:************. port:5001

68947e6226465fe58213b5df	Experiment Crash
--------------------------------------------------
bus-8-ImpaOptiParams-68947eb7cdb9adbbae8f4351
--------------------------------------------------
connect fail, ip:************. port:5001
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 294, in _initial
    self.select_mic_source(
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 121, in select_mic_source
    self.mic_source = DomesticMic(mic_source, ip, device_id)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 315, in __init__
    self._mic_obj = mic_obj(ip=ip, device_id=device_id)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\BYFS.py", line 474, in __init__
    self._connection = TcpClient(ip, port, once=True)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 17, in __init__
    self.connect()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 29, in connect
    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')
TimeoutError: connect fail, ip:************. port:5001

68947e6726465fe58213b5e0	Experiment Crash
--------------------------------------------------
bus-8-ImpaOptiParams-68947ebbcdb9adbbae8f4354
--------------------------------------------------
connect fail, ip:************. port:5001
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 294, in _initial
    self.select_mic_source(
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 121, in select_mic_source
    self.mic_source = DomesticMic(mic_source, ip, device_id)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 315, in __init__
    self._mic_obj = mic_obj(ip=ip, device_id=device_id)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\BYFS.py", line 474, in __init__
    self._connection = TcpClient(ip, port, once=True)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 17, in __init__
    self.connect()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 29, in connect
    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')
TimeoutError: connect fail, ip:************. port:5001

68947e6b26465fe58213b5e1	Experiment Crash
--------------------------------------------------
bus-8-ImpaOptiParams-68947ec0cdb9adbbae8f4357
--------------------------------------------------
connect fail, ip:************. port:5001
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 294, in _initial
    self.select_mic_source(
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 121, in select_mic_source
    self.mic_source = DomesticMic(mic_source, ip, device_id)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 315, in __init__
    self._mic_obj = mic_obj(ip=ip, device_id=device_id)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\BYFS.py", line 474, in __init__
    self._connection = TcpClient(ip, port, once=True)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 17, in __init__
    self.connect()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 29, in connect
    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')
TimeoutError: connect fail, ip:************. port:5001

68947e6f26465fe58213b5e2	Experiment Crash
--------------------------------------------------
bus-8-ImpaOptiParams-68947ec4cdb9adbbae8f435a
--------------------------------------------------
connect fail, ip:************. port:5001
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 294, in _initial
    self.select_mic_source(
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 121, in select_mic_source
    self.mic_source = DomesticMic(mic_source, ip, device_id)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 315, in __init__
    self._mic_obj = mic_obj(ip=ip, device_id=device_id)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\BYFS.py", line 474, in __init__
    self._connection = TcpClient(ip, port, once=True)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 17, in __init__
    self.connect()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 29, in connect
    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')
TimeoutError: connect fail, ip:************. port:5001

68948ec826465fe58213b5e3	Experiment Crash
--------------------------------------------------
bus-5-ImpaOptiParams-68948f1dcdb9adbbae8f43ef
--------------------------------------------------
connect fail, ip:************. port:5001
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 294, in _initial
    self.select_mic_source(
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 121, in select_mic_source
    self.mic_source = DomesticMic(mic_source, ip, device_id)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 315, in __init__
    self._mic_obj = mic_obj(ip=ip, device_id=device_id)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\BYFS.py", line 474, in __init__
    self._connection = TcpClient(ip, port, once=True)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 17, in __init__
    self.connect()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 29, in connect
    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')
TimeoutError: connect fail, ip:************. port:5001

68948ed426465fe58213b5e4	Experiment Crash
--------------------------------------------------
bus-7-ImpaCavityFluxScan-68948f29cdb9adbbae8f43f5
--------------------------------------------------
connect fail, ip:************. port:5001
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 294, in _initial
    self.select_mic_source(
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 121, in select_mic_source
    self.mic_source = DomesticMic(mic_source, ip, device_id)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 315, in __init__
    self._mic_obj = mic_obj(ip=ip, device_id=device_id)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\BYFS.py", line 474, in __init__
    self._connection = TcpClient(ip, port, once=True)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 17, in __init__
    self.connect()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 29, in connect
    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')
TimeoutError: connect fail, ip:************. port:5001

68948ed85dd635150013b5d9	Experiment Crash
--------------------------------------------------
bus-7-ImpaOptiParams-68948f2dcdb9adbbae8f43f8
--------------------------------------------------
connect fail, ip:************. port:5001
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 294, in _initial
    self.select_mic_source(
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 121, in select_mic_source
    self.mic_source = DomesticMic(mic_source, ip, device_id)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 315, in __init__
    self._mic_obj = mic_obj(ip=ip, device_id=device_id)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\BYFS.py", line 474, in __init__
    self._connection = TcpClient(ip, port, once=True)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 17, in __init__
    self.connect()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 29, in connect
    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')
TimeoutError: connect fail, ip:************. port:5001

68948edc26465fe58213b5e5	Experiment Crash
--------------------------------------------------
bus-7-ImpaOptiParams-68948f31cdb9adbbae8f43fb
--------------------------------------------------
connect fail, ip:************. port:5001
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 294, in _initial
    self.select_mic_source(
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 121, in select_mic_source
    self.mic_source = DomesticMic(mic_source, ip, device_id)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 315, in __init__
    self._mic_obj = mic_obj(ip=ip, device_id=device_id)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\BYFS.py", line 474, in __init__
    self._connection = TcpClient(ip, port, once=True)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 17, in __init__
    self.connect()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 29, in connect
    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')
TimeoutError: connect fail, ip:************. port:5001

68948ee15dd635150013b5da	Experiment Crash
--------------------------------------------------
bus-7-ImpaOptiParams-68948f36cdb9adbbae8f43fe
--------------------------------------------------
connect fail, ip:************. port:5001
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 294, in _initial
    self.select_mic_source(
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 121, in select_mic_source
    self.mic_source = DomesticMic(mic_source, ip, device_id)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 315, in __init__
    self._mic_obj = mic_obj(ip=ip, device_id=device_id)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\BYFS.py", line 474, in __init__
    self._connection = TcpClient(ip, port, once=True)
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 17, in __init__
    self.connect()
  File "D:\code\PYQCAT\lzw\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 29, in connect
    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')
TimeoutError: connect fail, ip:************. port:5001

68998a4ff340c94b3b449610	Experiment Crash
--------------------------------------------------
bus-15-ImpaGain-68998af277ff99b4c37a1b3b
--------------------------------------------------
connect fail, ip:*************. port:5001
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 294, in _initial
    self.select_mic_source(
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 121, in select_mic_source
    self.mic_source = DomesticMic(mic_source, ip, device_id)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 315, in __init__
    self._mic_obj = mic_obj(ip=ip, device_id=device_id)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\instrument\BYFS.py", line 474, in __init__
    self._connection = TcpClient(ip, port, once=True)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 17, in __init__
    self.connect()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 29, in connect
    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')
TimeoutError: connect fail, ip:*************. port:5001

68998a58f4b30d99914495be	Experiment Crash
--------------------------------------------------
bus-15-ImpaGain-68998afc77ff99b4c37a1b3e
--------------------------------------------------
connect fail, ip:*************. port:5001
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 294, in _initial
    self.select_mic_source(
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 121, in select_mic_source
    self.mic_source = DomesticMic(mic_source, ip, device_id)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 315, in __init__
    self._mic_obj = mic_obj(ip=ip, device_id=device_id)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\instrument\BYFS.py", line 474, in __init__
    self._connection = TcpClient(ip, port, once=True)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 17, in __init__
    self.connect()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 29, in connect
    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')
TimeoutError: connect fail, ip:*************. port:5001

68998a62f4b30d99914495c0	Experiment Crash
--------------------------------------------------
bus-16-ImpaGain-68998b0677ff99b4c37a1b41
--------------------------------------------------
connect fail, ip:*************. port:5001
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 294, in _initial
    self.select_mic_source(
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 121, in select_mic_source
    self.mic_source = DomesticMic(mic_source, ip, device_id)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 315, in __init__
    self._mic_obj = mic_obj(ip=ip, device_id=device_id)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\instrument\BYFS.py", line 474, in __init__
    self._connection = TcpClient(ip, port, once=True)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 17, in __init__
    self.connect()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 29, in connect
    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')
TimeoutError: connect fail, ip:*************. port:5001

68998a6cf340c94b3b449611	Experiment Crash
--------------------------------------------------
bus-16-ImpaGain-68998b0f77ff99b4c37a1b44
--------------------------------------------------
connect fail, ip:*************. port:5001
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 294, in _initial
    self.select_mic_source(
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 121, in select_mic_source
    self.mic_source = DomesticMic(mic_source, ip, device_id)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 315, in __init__
    self._mic_obj = mic_obj(ip=ip, device_id=device_id)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\instrument\BYFS.py", line 474, in __init__
    self._connection = TcpClient(ip, port, once=True)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 17, in __init__
    self.connect()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 29, in connect
    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')
TimeoutError: connect fail, ip:*************. port:5001

68998a75f4b30d99914495c1	Experiment Crash
--------------------------------------------------
bus-16-ImpaGain-68998b1977ff99b4c37a1b47
--------------------------------------------------
connect fail, ip:*************. port:5001
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 294, in _initial
    self.select_mic_source(
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_models.py", line 121, in select_mic_source
    self.mic_source = DomesticMic(mic_source, ip, device_id)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\preliminary\preliminary_inst.py", line 315, in __init__
    self._mic_obj = mic_obj(ip=ip, device_id=device_id)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\instrument\BYFS.py", line 474, in __init__
    self._connection = TcpClient(ip, port, once=True)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 17, in __init__
    self.connect()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-apps\pyQCat\instrument\socket_service\tcp_client.py", line 29, in connect
    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')
TimeoutError: connect fail, ip:*************. port:5001

68998ba6f6e8e052d6d4262b	Experiment Crash
--------------------------------------------------
q1q2q3q4q5q6-BUS15-Channel7-ImpaSetParams-68998c4aa0467b7306dc43d2
--------------------------------------------------
connect fail, ip:*************. port:5001
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 294, in _initial
    self.select_mic_source(
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 121, in select_mic_source
    self.mic_source = DomesticMic(mic_source, ip, device_id)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_inst.py", line 315, in __init__
    self._mic_obj = mic_obj(ip=ip, device_id=device_id)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\BYFS.py", line 473, in __init__
    self._id = device_id
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\socket_service\tcp_client.py", line 17, in __init__
    self.connect()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\socket_service\tcp_client.py", line 29, in connect
    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')
TimeoutError: connect fail, ip:*************. port:5001

68998bcab242e0e00cd4217b	Experiment Crash
--------------------------------------------------
q1q2q3q4q5q6-BUS15-Channel7-ImpaSetParams-68998c6ea0467b7306dc43d5
--------------------------------------------------
connect fail, ip:*************. port:5001
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 294, in _initial
    self.select_mic_source(
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 121, in select_mic_source
    self.mic_source = DomesticMic(mic_source, ip, device_id)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_inst.py", line 315, in __init__
    self._mic_obj = mic_obj(ip=ip, device_id=device_id)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\BYFS.py", line 473, in __init__
    self._id = device_id
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\socket_service\tcp_client.py", line 17, in __init__
    self.connect()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\socket_service\tcp_client.py", line 29, in connect
    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')
TimeoutError: connect fail, ip:*************. port:5001

68998c97b242e0e00cd4217e	Experiment Crash
--------------------------------------------------
q1q2q3q4q5q6-BUS15-Channel7-ImpaSetParams-68998d3ba0467b7306dc43d8
--------------------------------------------------
connect fail, ip:*************. port:5001
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 294, in _initial
    self.select_mic_source(
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 121, in select_mic_source
    self.mic_source = DomesticMic(mic_source, ip, device_id)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_inst.py", line 315, in __init__
    self._mic_obj = mic_obj(ip=ip, device_id=device_id)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\BYFS.py", line 473, in __init__
    self._id = device_id
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\socket_service\tcp_client.py", line 17, in __init__
    self.connect()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\socket_service\tcp_client.py", line 29, in connect
    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')
TimeoutError: connect fail, ip:*************. port:5001

68998d32b242e0e00cd42185	Experiment Crash
--------------------------------------------------
q1q2q3q4q5q6-BUS15-Channel7-ImpaSetParams-68998dd6a0467b7306dc43db
--------------------------------------------------
connect fail, ip:*************. port:5001
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 294, in _initial
    self.select_mic_source(
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 121, in select_mic_source
    self.mic_source = DomesticMic(mic_source, ip, device_id)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_inst.py", line 315, in __init__
    self._mic_obj = mic_obj(ip=ip, device_id=device_id)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\BYFS.py", line 473, in __init__
    self._id = device_id
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\socket_service\tcp_client.py", line 17, in __init__
    self.connect()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\socket_service\tcp_client.py", line 29, in connect
    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')
TimeoutError: connect fail, ip:*************. port:5001

68998d51f6e8e052d6d42632	Experiment Crash
--------------------------------------------------
q1q2q3q4q5q6-BUS15-Channel7-ImpaSetParams-68998df5a0467b7306dc43de
--------------------------------------------------
connect fail, ip:*************. port:5001
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 294, in _initial
    self.select_mic_source(
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 121, in select_mic_source
    self.mic_source = DomesticMic(mic_source, ip, device_id)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_inst.py", line 315, in __init__
    self._mic_obj = mic_obj(ip=ip, device_id=device_id)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\BYFS.py", line 473, in __init__
    self._id = device_id
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\socket_service\tcp_client.py", line 17, in __init__
    self.connect()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\socket_service\tcp_client.py", line 29, in connect
    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')
TimeoutError: connect fail, ip:*************. port:5001

68998da7f6e8e052d6d42633	Experiment Crash
--------------------------------------------------
q1q2q3q4q5q6-BUS15-Channel7-ImpaSetParams-68998e4ba0467b7306dc43e1
--------------------------------------------------
connect fail, ip:*************. port:5001
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 294, in _initial
    self.select_mic_source(
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 121, in select_mic_source
    self.mic_source = DomesticMic(mic_source, ip, device_id)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_inst.py", line 315, in __init__
    self._mic_obj = mic_obj(ip=ip, device_id=device_id)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\BYFS.py", line 473, in __init__
    self._id = device_id
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\socket_service\tcp_client.py", line 17, in __init__
    self.connect()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\socket_service\tcp_client.py", line 29, in connect
    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')
TimeoutError: connect fail, ip:*************. port:5001

68998de4f6e8e052d6d42634	Experiment Crash
--------------------------------------------------
q1q2q3q4q5q6-BUS15-Channel7-ImpaSetParams-68998e872d6a0302ccaad336
--------------------------------------------------
connect fail, ip:*************. port:5001
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 294, in _initial
    self.select_mic_source(
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 121, in select_mic_source
    self.mic_source = DomesticMic(mic_source, ip, device_id)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_inst.py", line 315, in __init__
    self._mic_obj = mic_obj(ip=ip, device_id=device_id)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\BYFS.py", line 474, in __init__
    self._connection = TcpClient(ip, port, once=True)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\socket_service\tcp_client.py", line 17, in __init__
    self.connect()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\socket_service\tcp_client.py", line 29, in connect
    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')
TimeoutError: connect fail, ip:*************. port:5001

68998e51b242e0e00cd42186	Experiment Crash
--------------------------------------------------
bus-15-ImpaGain-68998ef52d6a0302ccaad339
--------------------------------------------------
connect fail, ip:*************. port:5001
--------------------------------------------------
Traceback (most recent call last):
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\experiments\_wrappers.py", line 74, in wrapper
    await func(*args, **kwargs)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 337, in run_experiment
    self.run()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 326, in run
    self._initial()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 294, in _initial
    self.select_mic_source(
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_models.py", line 121, in select_mic_source
    self.mic_source = DomesticMic(mic_source, ip, device_id)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\preliminary\preliminary_inst.py", line 315, in __init__
    self._mic_obj = mic_obj(ip=ip, device_id=device_id)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\BYFS.py", line 474, in __init__
    self._connection = TcpClient(ip, port, once=True)
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\socket_service\tcp_client.py", line 17, in __init__
    self.connect()
  File "D:\code\shenxiang\Git-Project\0.23.2\pyqcat-visage\pyQCat\instrument\socket_service\tcp_client.py", line 29, in connect
    raise TimeoutError(f'connect fail, ip:{self.ip}. port:{self.port}')
TimeoutError: connect fail, ip:*************. port:5001
