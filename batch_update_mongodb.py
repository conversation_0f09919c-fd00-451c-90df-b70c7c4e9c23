#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MongoDB批量更新脚本
作者: KangKang Geng
功能: 根据txt文件中的_id批量更新MongoDB数据库中的tackle字段
"""

import json
import logging
import sys
import argparse
from pathlib import Path
from typing import List, Optional, Dict, Any
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, OperationFailure
from bson import ObjectId
from bson.errors import InvalidId


class MongoDBUpdater:
    """MongoDB批量更新器"""

    def __init__(self, config_file: str = "db_config.json"):
        """
        初始化MongoDB连接

        Args:
            config_file: 数据库配置文件路径
        """
        self.config = self._load_config(config_file)
        self.client = None
        self.db = None
        self.collection = None
        self._setup_logging()

    def _setup_logging(self):
        """设置日志记录"""
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s - %(levelname)s - %(message)s",
            handlers=[
                logging.FileHandler("mongodb_update.log", encoding="utf-8"),
                logging.StreamHandler(sys.stdout),
            ],
        )
        self.logger = logging.getLogger(__name__)

    def _load_config(self, config_file: str) -> Dict[str, Any]:
        """
        加载数据库配置

        Args:
            config_file: 配置文件路径

        Returns:
            配置字典

        Raises:
            FileNotFoundError: 配置文件不存在
            json.JSONDecodeError: 配置文件格式错误
        """
        config_path = Path(config_file)
        if not config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_file}")

        try:
            with open(config_path, "r", encoding="utf-8") as f:
                config = json.load(f)
            return config
        except json.JSONDecodeError as e:
            raise json.JSONDecodeError(f"配置文件格式错误: {e}")

    def connect(self) -> bool:
        """
        连接到MongoDB数据库

        Returns:
            连接是否成功
        """
        try:
            # 构建连接字符串
            if "username" in self.config and "password" in self.config:
                connection_string = (
                    f"mongodb://{self.config['username']}:{self.config['password']}"
                    f"@{self.config['host']}:{self.config['port']}"
                    f"/{self.config['db_name']}?authSource={self.config.get('authSource', 'admin')}"
                )
            else:
                connection_string = (
                    f"mongodb://{self.config['host']}:{self.config['port']}"
                )

            self.client = MongoClient(connection_string, serverSelectionTimeoutMS=5000)

            # 测试连接
            self.client.admin.command("ping")

            # 获取数据库和集合
            self.db = self.client[self.config["db_name"]]
            self.collection = self.db[self.config["collection_name"]]

            self.logger.info(
                f"成功连接到MongoDB: {self.config['host']}:{self.config['port']}"
            )
            self.logger.info(
                f"数据库: {self.config['db_name']}, 集合: {self.config['collection_name']}"
            )
            return True

        except ConnectionFailure as e:
            self.logger.error(f"MongoDB连接失败: {e}")
            return False
        except Exception as e:
            self.logger.error(f"连接过程中发生错误: {e}")
            return False

    def disconnect(self):
        """断开MongoDB连接"""
        if self.client is not None:
            self.client.close()
            self.logger.info("已断开MongoDB连接")

    def parse_txt_file(
        self, file_path: str, start_line: int, end_line: int
    ) -> List[str]:
        """
        解析txt文件，提取指定行范围内的_id

        Args:
            file_path: txt文件路径
            start_line: 起始行号（从1开始）
            end_line: 结束行号（包含）

        Returns:
            _id列表

        Raises:
            FileNotFoundError: 文件不存在
            ValueError: 行号范围无效
        """
        file_path = Path(file_path)
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")

        if start_line < 1 or end_line < start_line:
            raise ValueError(f"无效的行号范围: {start_line}-{end_line}")

        ids = []
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                lines = f.readlines()

            # 检查行号范围是否超出文件长度
            if end_line > len(lines):
                self.logger.warning(
                    f"结束行号 {end_line} 超出文件长度 {len(lines)}，将调整为文件末尾"
                )
                end_line = len(lines)

            # 提取指定范围内的行
            for line_num in range(start_line - 1, end_line):  # 转换为0基索引
                if line_num < len(lines):
                    line = lines[line_num].strip()
                    if line and "\t" in line:
                        # 分割制表符，获取第一列（_id）
                        parts = line.split("\t", 1)
                        if parts[0] and parts[0] != "_id":  # 跳过标题行
                            ids.append(parts[0])

            self.logger.info(
                f"从文件 {file_path} 的第 {start_line}-{end_line} 行提取到 {len(ids)} 个ID"
            )
            return ids

        except Exception as e:
            self.logger.error(f"解析文件时发生错误: {e}")
            raise

    def update_tackle_field(
        self, object_ids: List[str], tackle_value: Any
    ) -> Dict[str, int]:
        """
        批量更新tackle字段

        Args:
            object_ids: ObjectId字符串列表
            tackle_value: tackle字段的目标值

        Returns:
            更新统计信息字典
        """
        if self.collection is None:
            raise RuntimeError("未连接到数据库")

        stats = {"total": len(object_ids), "updated": 0, "not_found": 0, "errors": 0}

        self.logger.info(
            f"开始批量更新 {stats['total']} 条记录的tackle字段为: {tackle_value}"
        )

        for i, oid_str in enumerate(object_ids, 1):
            try:
                # 验证并转换ObjectId
                try:
                    oid = ObjectId(oid_str)
                except InvalidId:
                    self.logger.error(f"无效的ObjectId格式: {oid_str}")
                    stats["errors"] += 1
                    continue

                # 执行更新操作
                result = self.collection.update_one(
                    {"_id": oid},
                    {"$set": {"tackle": tackle_value}},
                    upsert=False,  # 不创建新文档，只更新存在的文档
                )

                if result.matched_count > 0:
                    stats["updated"] += 1
                    self.logger.debug(f"成功更新记录: {oid_str}")
                else:
                    stats["not_found"] += 1
                    self.logger.warning(f"未找到记录: {oid_str}")

                # 显示进度
                if i % 100 == 0 or i == stats["total"]:
                    progress = (i / stats["total"]) * 100
                    self.logger.info(f"进度: {i}/{stats['total']} ({progress:.1f}%)")

            except OperationFailure as e:
                self.logger.error(f"更新记录 {oid_str} 时发生数据库错误: {e}")
                stats["errors"] += 1
            except Exception as e:
                self.logger.error(f"处理记录 {oid_str} 时发生未知错误: {e}")
                stats["errors"] += 1

        return stats

    def print_summary(self, stats: Dict[str, int]):
        """打印更新摘要"""
        self.logger.info("=" * 50)
        self.logger.info("更新摘要:")
        self.logger.info(f"总计处理: {stats['total']} 条记录")
        self.logger.info(f"成功更新: {stats['updated']} 条记录")
        self.logger.info(f"未找到记录: {stats['not_found']} 条记录")
        self.logger.info(f"发生错误: {stats['errors']} 条记录")
        self.logger.info("=" * 50)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="MongoDB批量更新脚本")
    parser.add_argument("--file", "-f", required=True, help="txt文件路径")
    parser.add_argument(
        "--start", "-s", type=int, required=True, help="起始行号（从1开始）"
    )
    parser.add_argument("--end", "-e", type=int, required=True, help="结束行号（包含）")
    parser.add_argument(
        "--tackle", "-t", type=int, default=1, help="tackle字段的目标值（默认为1）"
    )
    parser.add_argument(
        "--config", "-c", default="db_config.json", help="数据库配置文件路径"
    )

    args = parser.parse_args()

    updater = MongoDBUpdater(args.config)

    try:
        # 连接数据库
        if not updater.connect():
            sys.exit(1)

        # 解析文件
        object_ids = updater.parse_txt_file(args.file, args.start, args.end)

        if not object_ids:
            updater.logger.warning("未找到任何有效的ID，退出程序")
            sys.exit(0)

        # 执行更新
        stats = updater.update_tackle_field(object_ids, args.tackle)

        # 打印摘要
        updater.print_summary(stats)

    except Exception as e:
        updater.logger.error(f"程序执行过程中发生错误: {e}")
        sys.exit(1)
    finally:
        updater.disconnect()


if __name__ == "__main__":
    main()
